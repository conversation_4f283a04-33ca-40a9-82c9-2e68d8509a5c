//+------------------------------------------------------------------+
//|                                            三维紧迫度模型测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "三维紧迫度模型功能测试脚本"
#property script_show_inputs

// 测试参数
input int TestScenarios = 20;        // 测试场景数量
input bool EnableDetailedLog = true; // 启用详细日志
input double TestUrgencyRange = 100.0; // 测试紧迫度范围

//+------------------------------------------------------------------+
//| 三维紧迫度模型测试脚本                                            |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始三维紧迫度模型测试");
    Print("测试场景: ", TestScenarios);
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("紧迫度范围: ", TestUrgencyRange);
    Print("========================================");
    
    // 测试1: 紧迫度计算公式测试
    TestUrgencyCalculationFormula();
    
    // 测试2: 订单参数计算测试
    TestOrderParametersCalculation();
    
    // 测试3: 非对称权重分配测试
    TestAsymmetricWeightAllocation();
    
    // 测试4: 紧迫度等级分类测试
    TestUrgencyLevelClassification();
    
    // 测试5: 订单流压力计算测试
    TestOrderFlowPressureCalculation();
    
    // 测试6: 状态衰减机制测试
    TestStateDurationDecayMechanism();
    
    // 测试7: 完整场景模拟测试
    TestCompleteScenarioSimulation();
    
    Print("========================================");
    Print("✅ 三维紧迫度模型测试完成");
}

//+------------------------------------------------------------------+
//| 测试紧迫度计算公式                                                |
//+------------------------------------------------------------------+
void TestUrgencyCalculationFormula()
{
    Print("📋 测试1: 紧迫度计算公式测试");
    
    // 测试不同组件组合的紧迫度计算
    struct TestCase {
        double breakoutProb;
        double accelerationProb;
        double orderFlowPressure;
        double expectedUrgency;
        string description;
    };
    
    TestCase testCases[] = {
        {80.0, 70.0, 50.0, 85.0, "高爆发+高加速+正压力"},
        {60.0, 50.0, -30.0, 65.0, "中爆发+中加速+负压力"},
        {90.0, 80.0, 80.0, 95.0, "极高全维度"},
        {20.0, 15.0, 10.0, 25.0, "低全维度"},
        {50.0, 40.0, 0.0, 50.0, "中等平衡"}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        TestCase tc = testCases[i];
        
        // 使用Doubao-Seed-1.6增强公式
        double calculatedUrgency = tc.breakoutProb * 1.7 +      // 爆发概率权重1.7
                                  tc.accelerationProb * 0.9 +   // 加速概率权重0.9
                                  MathAbs(tc.orderFlowPressure) * 0.4; // 订单流权重0.4
        
        calculatedUrgency = MathMax(0.0, MathMin(100.0, calculatedUrgency));
        
        Print("场景 ", i+1, ": ", tc.description);
        Print("  输入: 爆发=", tc.breakoutProb, " 加速=", tc.accelerationProb, " 流=", tc.orderFlowPressure);
        Print("  计算紧迫度: ", DoubleToString(calculatedUrgency, 1));
        Print("  预期紧迫度: ", DoubleToString(tc.expectedUrgency, 1));
        Print("  偏差: ", DoubleToString(MathAbs(calculatedUrgency - tc.expectedUrgency), 1));
    }
    
    Print("✅ 紧迫度计算公式测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试订单参数计算                                                  |
//+------------------------------------------------------------------+
void TestOrderParametersCalculation()
{
    Print("📋 测试2: 订单参数计算测试");
    
    // 测试不同紧迫度下的订单参数
    double testUrgencies[] = {10.0, 30.0, 50.0, 70.0, 90.0};
    
    for(int i = 0; i < ArraySize(testUrgencies); i++) {
        double urgency = testUrgencies[i];
        
        // 计算订单参数 (与实际算法一致)
        int orderCount = (int)(urgency / 10) + 1;
        orderCount = MathMax(1, MathMin(10, orderCount));
        
        int intervalSeconds = 900 - (int)(urgency * 6);
        intervalSeconds = MathMax(300, MathMin(900, intervalSeconds));
        
        double orderSize = 0.01 + (urgency / 100.0) * 0.09;
        orderSize = MathMax(0.01, MathMin(0.1, orderSize));
        
        Print("紧迫度: ", DoubleToString(urgency, 1), "%");
        Print("  订单数量: ", orderCount, "个");
        Print("  订单间隔: ", intervalSeconds, "秒");
        Print("  订单规模: ", DoubleToString(orderSize, 3), "手");
        
        // 验证是否符合用户需求 (900-300秒间隔)
        bool intervalValid = (intervalSeconds >= 300 && intervalSeconds <= 900);
        Print("  间隔验证: ", intervalValid ? "✓" : "✗");
    }
    
    Print("✅ 订单参数计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试非对称权重分配                                                |
//+------------------------------------------------------------------+
void TestAsymmetricWeightAllocation()
{
    Print("📋 测试3: 非对称权重分配测试");
    
    // 测试不同趋势场景下的权重分配
    struct TrendScenario {
        double trendDirection;
        double trendStrength;
        double expectedBullishWeight;
        string description;
    };
    
    TrendScenario scenarios[] = {
        {0.8, 70.0, 0.85, "强看涨趋势"},
        {-0.8, 70.0, 0.15, "强看跌趋势"},
        {0.5, 45.0, 0.7, "中等看涨趋势"},
        {-0.5, 45.0, 0.3, "中等看跌趋势"},
        {0.1, 20.0, 0.5, "中性市场"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        TrendScenario scenario = scenarios[i];
        
        // 模拟权重计算逻辑
        double bullishWeight = 0.5; // 默认对称
        
        if(scenario.trendStrength > 60.0 && MathAbs(scenario.trendDirection) > 0.6) {
            // 强趋势
            if(scenario.trendDirection > 0) {
                bullishWeight = 0.85; // 85%看涨
            } else {
                bullishWeight = 0.15; // 15%看涨
            }
        } else if(scenario.trendStrength > 30.0 && MathAbs(scenario.trendDirection) > 0.3) {
            // 中等趋势
            if(scenario.trendDirection > 0) {
                bullishWeight = 0.7; // 70%看涨
            } else {
                bullishWeight = 0.3; // 30%看涨
            }
        }
        
        double bearishWeight = 1.0 - bullishWeight;
        
        Print("场景: ", scenario.description);
        Print("  趋势方向: ", DoubleToString(scenario.trendDirection, 2));
        Print("  趋势强度: ", DoubleToString(scenario.trendStrength, 1), "%");
        Print("  看涨权重: ", DoubleToString(bullishWeight * 100, 0), "%");
        Print("  看跌权重: ", DoubleToString(bearishWeight * 100, 0), "%");
        Print("  预期看涨: ", DoubleToString(scenario.expectedBullishWeight * 100, 0), "%");
        
        bool weightCorrect = (MathAbs(bullishWeight - scenario.expectedBullishWeight) < 0.01);
        Print("  权重验证: ", weightCorrect ? "✓" : "✗");
    }
    
    Print("✅ 非对称权重分配测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试紧迫度等级分类                                                |
//+------------------------------------------------------------------+
void TestUrgencyLevelClassification()
{
    Print("📋 测试4: 紧迫度等级分类测试");
    
    // 测试紧迫度等级边界
    double testValues[] = {5.0, 15.0, 25.0, 35.0, 45.0, 55.0, 65.0, 75.0, 85.0, 95.0};
    string expectedLevels[] = {"无", "无", "低", "低", "中等", "中等", "高", "高", "临界", "临界"};
    
    for(int i = 0; i < ArraySize(testValues); i++) {
        double urgency = testValues[i];
        string expectedLevel = expectedLevels[i];
        
        // 计算等级
        string actualLevel = "未知";
        if(urgency >= 80) actualLevel = "临界";
        else if(urgency >= 60) actualLevel = "高";
        else if(urgency >= 40) actualLevel = "中等";
        else if(urgency >= 20) actualLevel = "低";
        else actualLevel = "无";
        
        bool levelCorrect = (actualLevel == expectedLevel);
        
        Print("紧迫度: ", DoubleToString(urgency, 1), "% → 等级: ", actualLevel, 
              " (预期: ", expectedLevel, ") ", levelCorrect ? "✓" : "✗");
    }
    
    Print("✅ 紧迫度等级分类测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试订单流压力计算                                                |
//+------------------------------------------------------------------+
void TestOrderFlowPressureCalculation()
{
    Print("📋 测试5: 订单流压力计算测试");
    
    // 模拟不同订单流场景
    struct OrderFlowScenario {
        double bidVolume;
        double askVolume;
        double expectedPressure;
        string description;
    };
    
    OrderFlowScenario scenarios[] = {
        {800, 200, 60.0, "强买压"},
        {200, 800, -60.0, "强卖压"},
        {500, 500, 0.0, "平衡"},
        {700, 300, 40.0, "中等买压"},
        {300, 700, -40.0, "中等卖压"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        OrderFlowScenario scenario = scenarios[i];
        
        // 计算订单流压力
        double totalVolume = scenario.bidVolume + scenario.askVolume;
        double flowPressure = 0.0;
        
        if(totalVolume > 0.0001) {
            flowPressure = ((scenario.bidVolume - scenario.askVolume) / totalVolume) * 100.0;
        }
        
        Print("场景: ", scenario.description);
        Print("  买单量: ", DoubleToString(scenario.bidVolume, 0));
        Print("  卖单量: ", DoubleToString(scenario.askVolume, 0));
        Print("  计算压力: ", DoubleToString(flowPressure, 1));
        Print("  预期压力: ", DoubleToString(scenario.expectedPressure, 1));
        
        bool pressureCorrect = (MathAbs(flowPressure - scenario.expectedPressure) < 5.0);
        Print("  压力验证: ", pressureCorrect ? "✓" : "✗");
    }
    
    Print("✅ 订单流压力计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试状态衰减机制                                                  |
//+------------------------------------------------------------------+
void TestStateDurationDecayMechanism()
{
    Print("📋 测试6: 状态衰减机制测试");
    
    // 测试不同持续时间的衰减效果
    int testDurations[] = {1, 3, 5, 7, 10, 15, 20};
    double decayRate = 0.05; // 5%衰减率
    
    for(int i = 0; i < ArraySize(testDurations); i++) {
        int duration = testDurations[i];
        
        // 计算衰减因子
        double decayFactor = 1.0;
        if(duration > 5) {
            decayFactor = 1.0 - ((duration - 5) * decayRate);
            decayFactor = MathMax(0.5, decayFactor); // 最多衰减50%
        }
        
        Print("持续时间: ", duration, "根K线");
        Print("  衰减因子: ", DoubleToString(decayFactor, 3));
        Print("  衰减程度: ", DoubleToString((1.0 - decayFactor) * 100, 1), "%");
        
        // 验证衰减逻辑
        bool decayCorrect = (duration <= 5) ? (decayFactor == 1.0) : (decayFactor < 1.0);
        Print("  衰减验证: ", decayCorrect ? "✓" : "✗");
    }
    
    Print("✅ 状态衰减机制测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整场景模拟                                                  |
//+------------------------------------------------------------------+
void TestCompleteScenarioSimulation()
{
    Print("📋 测试7: 完整场景模拟测试");
    
    // 模拟完整的交易场景
    for(int i = 0; i < 5; i++) {
        Print("=== 场景 ", i+1, " ===");
        
        // 随机生成市场参数
        double breakoutProb = MathRand() / 32767.0 * 100;
        double accelerationProb = MathRand() / 32767.0 * 100;
        double orderFlowPressure = (MathRand() / 32767.0 - 0.5) * 200; // -100到+100
        int compressionDuration = (int)(MathRand() / 32767.0 * 20) + 1; // 1-20
        
        // 计算紧迫度
        double urgency = breakoutProb * 1.7 + accelerationProb * 0.9 + MathAbs(orderFlowPressure) * 0.4;
        
        // 应用衰减
        if(compressionDuration > 5) {
            double decayFactor = MathMax(0.5, 1.0 - ((compressionDuration - 5) * 0.05));
            urgency *= decayFactor;
        }
        
        urgency = MathMax(0.0, MathMin(100.0, urgency));
        
        // 计算订单参数
        int orderCount = (int)(urgency / 10) + 1;
        orderCount = MathMax(1, MathMin(10, orderCount));
        
        int intervalSeconds = 900 - (int)(urgency * 6);
        intervalSeconds = MathMax(300, MathMin(900, intervalSeconds));
        
        double orderSize = 0.01 + (urgency / 100.0) * 0.09;
        
        // 输出结果
        Print("输入参数:");
        Print("  爆发概率: ", DoubleToString(breakoutProb, 1), "%");
        Print("  加速概率: ", DoubleToString(accelerationProb, 1), "%");
        Print("  订单流压力: ", DoubleToString(orderFlowPressure, 1));
        Print("  压缩持续: ", compressionDuration, "根K线");
        
        Print("计算结果:");
        Print("  最终紧迫度: ", DoubleToString(urgency, 1), "%");
        Print("  订单数量: ", orderCount, "个");
        Print("  订单间隔: ", intervalSeconds, "秒");
        Print("  订单规模: ", DoubleToString(orderSize, 3), "手");
        
        // 验证结果合理性
        bool resultsValid = (orderCount >= 1 && orderCount <= 10) &&
                           (intervalSeconds >= 300 && intervalSeconds <= 900) &&
                           (orderSize >= 0.01 && orderSize <= 0.1);
        Print("  结果验证: ", resultsValid ? "✓" : "✗");
        Print("");
    }
    
    Print("✅ 完整场景模拟测试完成\n");
}
