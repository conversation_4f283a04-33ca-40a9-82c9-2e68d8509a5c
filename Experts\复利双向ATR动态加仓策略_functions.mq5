//+------------------------------------------------------------------+
//| 创建信息面板                                                   |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 创建背景
   ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 10);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 300);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 240); // 增加高度以容纳更多信息
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrDarkSlateGray);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
   
   // 创建标题
   ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 20);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_SELECTABLE, false);
   ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "复利双向ATR动态加仓策略");
   ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, "Arial Bold");
   
   // 创建信息标签
   string labels[] = {
      "账户净值:", 
      "当前多单数:", 
      "当前空单数:", 
      "多单总手数:", 
      "空单总手数:", 
      "总风险(%):", 
      "当前ATR:", 
      "当前RSI:", 
      "当前ADX:", 
      "多单目标盈利:",
      "空单目标盈利:"
   };
   
   for(int i = 0; i < ArraySize(labels); i++)
   {
      // 创建标签
      ObjectCreate(0, "InfoPanel_Label_" + IntegerToString(i), OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_XDISTANCE, 20);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_YDISTANCE, 40 + i * 20);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_COLOR, clrWhite);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_SELECTABLE, false);
      ObjectSetString(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_TEXT, labels[i]);
      ObjectSetString(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_FONT, "Arial");
      
      // 创建值
      ObjectCreate(0, "InfoPanel_Value_" + IntegerToString(i), OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_XDISTANCE, 150);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_YDISTANCE, 40 + i * 20);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_COLOR, clrYellow);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_SELECTABLE, false);
      ObjectSetString(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_TEXT, "0");
      ObjectSetString(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_FONT, "Arial");
   }
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   // 更新值
   ObjectSetString(0, "InfoPanel_Value_0", OBJPROP_TEXT, DoubleToString(account_equity, 2));
   ObjectSetString(0, "InfoPanel_Value_1", OBJPROP_TEXT, IntegerToString(total_long_orders));
   ObjectSetString(0, "InfoPanel_Value_2", OBJPROP_TEXT, IntegerToString(total_short_orders));
   ObjectSetString(0, "InfoPanel_Value_3", OBJPROP_TEXT, DoubleToString(total_long_lot, 2));
   ObjectSetString(0, "InfoPanel_Value_4", OBJPROP_TEXT, DoubleToString(total_short_lot, 2));
   ObjectSetString(0, "InfoPanel_Value_5", OBJPROP_TEXT, DoubleToString(CalculateTotalRisk(), 2));
   ObjectSetString(0, "InfoPanel_Value_6", OBJPROP_TEXT, DoubleToString(current_atr, 5));
   ObjectSetString(0, "InfoPanel_Value_7", OBJPROP_TEXT, DoubleToString(current_rsi, 2));
   ObjectSetString(0, "InfoPanel_Value_8", OBJPROP_TEXT, DoubleToString(current_adx, 2));
   
   // 计算多空目标盈利
   double risk_amount = account_equity * (InpMaxRiskPercent / 100.0);
   double target_long = MathMax(InpBaseProfitTarget, risk_amount * (current_atr / 0.0015));
   double target_short = target_long; // 多空目标盈利相同
   
   ObjectSetString(0, "InfoPanel_Value_9", OBJPROP_TEXT, DoubleToString(target_long, 0) + " 点");
   ObjectSetString(0, "InfoPanel_Value_10", OBJPROP_TEXT, DoubleToString(target_short, 0) + " 点");
   
   // 根据风险级别改变颜色
   double risk = CalculateTotalRisk();
   if(risk < InpMaxRiskPercent * 0.5)
      ObjectSetInteger(0, "InfoPanel_Value_5", OBJPROP_COLOR, clrLime);
   else if(risk < InpMaxRiskPercent * 0.8)
      ObjectSetInteger(0, "InfoPanel_Value_5", OBJPROP_COLOR, clrYellow);
   else
      ObjectSetInteger(0, "InfoPanel_Value_5", OBJPROP_COLOR, clrRed);
   
   // 根据RSI值改变颜色
   if(current_rsi > 70)
      ObjectSetInteger(0, "InfoPanel_Value_7", OBJPROP_COLOR, clrRed);
   else if(current_rsi < 30)
      ObjectSetInteger(0, "InfoPanel_Value_7", OBJPROP_COLOR, clrLime);
   else
      ObjectSetInteger(0, "InfoPanel_Value_7", OBJPROP_COLOR, clrYellow);
   
   // 根据ADX值改变颜色 (ADX > 25 表示强趋势)
   if(current_adx > 25)
      ObjectSetInteger(0, "InfoPanel_Value_8", OBJPROP_COLOR, clrLime);
   else
      ObjectSetInteger(0, "InfoPanel_Value_8", OBJPROP_COLOR, clrYellow);
}