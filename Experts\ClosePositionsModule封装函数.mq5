#property copyright "Copyright © 2018, <PERSON><PERSON> <PERSON>"
#property link      "https://www.mql5.com/en/users/amrali"
#property version   "1.00"
#property description "Module for closing market positions"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Arrays\ArrayLong.mqh>

CTrade         m_trade;
CPositionInfo  m_position;
CArrayLong     m_arr_tickets;

// 定义RTOTAL和SLEEPTIME
#define RTOTAL 3
#define SLEEPTIME 1000

// 异步平仓函数
bool ClosePositionsAsync()
{
    m_trade.SetDeviationInPoints(INT_MAX);
    m_trade.SetAsyncMode(true);
    m_trade.SetMarginMode();
    m_trade.LogLevel(LOG_LEVEL_ERRORS);

    for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
    {
        bool result = true;
        m_arr_tickets.Shutdown();

        for(int i = 0; i < PositionsTotal() && !IsStopped(); i++)
        {
            if(m_position.SelectByIndex(i))
            {
                m_arr_tickets.Add(m_position.Ticket());
            }
        }

        for(int i = 0; i < m_arr_tickets.Total() && !IsStopped(); i++)
        {
            ulong ticket = m_arr_tickets.At(i);
            if(m_position.SelectByTicket(ticket))
            {
                int freeze_level = (int)SymbolInfoInteger(m_position.Symbol(), SYMBOL_TRADE_FREEZE_LEVEL);
                double point = SymbolInfoDouble(m_position.Symbol(), SYMBOL_POINT);
                bool TP_check = (MathAbs(m_position.PriceCurrent() - m_position.TakeProfit()) > freeze_level * point);
                bool SL_check = (MathAbs(m_position.PriceCurrent() - m_position.StopLoss()) > freeze_level * point);

                if(TP_check && SL_check)
                {
                    m_trade.SetExpertMagicNumber(m_position.Magic());
                    m_trade.SetTypeFillingBySymbol(m_position.Symbol());
                    if(m_trade.PositionClose(ticket) && (m_trade.ResultRetcode() == TRADE_RETCODE_DONE || m_trade.ResultRetcode() == TRADE_RETCODE_PLACED))
                    {
                        PrintFormat("Position ticket #%I64u on %s to be closed.", ticket, m_position.Symbol());
                        PlaySound("expert.wav");
                    }
                    else
                    {
                        PrintFormat("> Error: closing position ticket #%I64u on %s failed. Retcode=%u (%s)", ticket, m_position.Symbol(), m_trade.ResultRetcode(), m_trade.ResultComment());
                        result = false;
                    }
                }
                else
                {
                    PrintFormat("> Error: closing position ticket #%I64u on %s is prohibited. Position TP or SL is too close to activation price [FROZEN].", ticket, m_position.Symbol());
                    result = false;
                }
            }
        }

        if(result)
            break;

        Sleep(SLEEPTIME);
        PlaySound("timeout.wav");
    }
    return true;
}

// 事件处理函数：OnStart
int OnStart()
{
    // 调用异步平仓函数
    ClosePositionsAsync();
    return(INIT_SUCCEEDED);
}    