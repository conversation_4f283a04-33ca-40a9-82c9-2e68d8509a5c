import time

import MetaTrader5 as mt5
from tools.order import *
import datetime


if __name__ == '__main__':
    path = "D:\\MetaTrader 5\\terminal64.exe"
    if not login(path=path,
                 server="Exness-MT5Trial5",
                 username=76314481,
                 password="6ixuhbWp",
                 timeout=2000):
        quit()

    delta = datetime.timedelta(seconds=120)
    dt = (datetime.datetime.now() + delta).timetuple()
    sec = int(time.mktime(dt))
    print(sec)

    symbol = "EURUSDz"
    magic = 8199231
    symbol_info:mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        stop()

    # 基础挂单测试
    m_price = symbol_info.ask - 100 * symbol_info.point

    order_send(symbol,  # 交易品种
               mt5.ORDER_TYPE_BUY_LIMIT,  # 挂单类型
               m_price,  # 挂单价格
               0.01,  # 挂单手数
               0,  # 挂单止损点数
               0,  # 挂单止盈点数
               5,  # 挂单成交时的滑点值
               0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
               2,  # 挂单到期时间的模式
               sec,  # 挂单到期时间(描述)
               "ceshi",
               magic
               )

    # buy limit
    m_price = symbol_info.ask - 100 * symbol_info.point

    buy_limit(symbol,  # 交易品种
               m_price,  # 挂单价格
               0.01,  # 挂单手数
               100,  # 挂单止损点数
               200,  # 挂单止盈点数
               5,  # 挂单成交时的滑点值
               0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
               2,  # 挂单到期时间的模式
               sec,  # 挂单到期时间(描述)
               "buy limit",
               magic
               )

    # sell limit挂单
    m_price = symbol_info.ask + 100 * symbol_info.point

    sell_limit(symbol,  # 交易品种
              m_price,  # 挂单价格
              0.01,  # 挂单手数
              100,  # 挂单止损点数
              200,  # 挂单止盈点数
              5,  # 挂单成交时的滑点值
              0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
              2,  # 挂单到期时间的模式
              sec,  # 挂单到期时间(描述)
              "sell limit",
              magic
              )

    # buy stop挂单
    m_price = symbol_info.ask + 100 * symbol_info.point

    buy_stop(symbol,  # 交易品种
               m_price,  # 挂单价格
               0.01,  # 挂单手数
               100,  # 挂单止损点数
               0,  # 挂单止盈点数
               5,  # 挂单成交时的滑点值
               0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
               2,  # 挂单到期时间的模式
               sec,  # 挂单到期时间(描述)
               "buy stop",
               magic
               )

    # buy stop挂单
    m_price = symbol_info.ask - 100 * symbol_info.point

    sell_stop(symbol,  # 交易品种
             m_price,  # 挂单价格
             0.01,  # 挂单手数
             100,  # 挂单止损点数
             0,  # 挂单止盈点数
             5,  # 挂单成交时的滑点值
             0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
             2,  # 挂单到期时间的模式
             sec,  # 挂单到期时间(描述)
             "sell stop",
             magic
             )

    # buy stop挂单
    m_price = symbol_info.ask + 50 * symbol_info.point
    m_stop_limit = m_price - 100 * symbol_info.point
    buy_stop_limit(symbol,  # 交易品种
              m_price,  # 挂单价格
              0.01,  # 挂单手数
              100,  # 挂单止损点数
              0,  # 挂单止盈点数
              5,  # 挂单成交时的滑点值
              m_stop_limit,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
              2,  # 挂单到期时间的模式
              sec,  # 挂单到期时间(描述)
              "buy stop limit",
              magic
              )

    # buy stop挂单
    m_price = symbol_info.ask - 50 * symbol_info.point
    m_stop_limit = m_price + 100 * symbol_info.point
    sell_stop_limit(symbol,  # 交易品种
                   m_price,  # 挂单价格
                   0.01,  # 挂单手数
                   100,  # 挂单止损点数
                   0,  # 挂单止盈点数
                   5,  # 挂单成交时的滑点值
                   m_stop_limit,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
                   2,  # 挂单到期时间的模式
                   sec,  # 挂单到期时间(描述)
                   "sell stop limit",
                   magic
                   )