# 导入MT5的模块
import MetaTrader5 as mt5
from pprint import pprint

if __name__ in "__main__":
    path = "D:\\MetaTrader 5\\terminal64.exe"
    # 第一种连接方式
    is_ok = mt5.initialize(path=path,
                           server="Exness-MT5Trial5",
                           login=76314481,
                           password="6ixuhbWp",
                           timeout=2000)
    # 检测连接状态
    if is_ok:
        # 查询连接状态信息
        print("MT5的作者: ", mt5.__author__)
        print("MT5的版本: ", mt5.__version__)
        # 查询MT5软件相关信息
        terminal_info: dict = mt5.terminal_info()._asdict()
        # pprint(type(terminal_info))
        # pprint(terminal_info.__class__.__dict__)
        # pprint(terminal_info)
        # pprint(terminal_info["company"])
        # pprint(terminal_info.get("company"))

        # 查询MT5 用户账号信息
        account_info: dict = mt5.account_info()._asdict()
        # pprint(account_info)

        for k, v in account_info.items():
            pprint("{0}: {1}".format(k, v))

    else:
        print("连接失败: {0}".format(mt5.last_error()))