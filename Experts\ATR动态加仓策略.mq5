//+------------------------------------------------------------------+
//|                                           ATR动态加仓策略.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\SymbolInfo.mqh>

// 全局变量
CTrade         Trade;          // 交易对象
CPositionInfo  PositionInfo;   // 持仓信息对象
CSymbolInfo    SymbolInfo;     // 品种信息对象

// 订单管理数组
struct OrderData
{
   ulong    ticket;           // 订单号
   datetime open_time;        // 开仓时间
   double   open_price;       // 开仓价格
   double   lot;              // 手数
   double   sl;               // 止损价格
   double   tp;               // 止盈价格
   int      direction;        // 方向 (1=多单, -1=空单)
   int      level;            // 加仓层级
   bool     is_revived;       // 是否为复活订单
   int      revival_count;    // 复活次数
   datetime last_revival_time;// 上次复活时间
   string   comment;          // 订单注释
};

OrderData orders[];           // 订单数据数组

//--- 资金管理参数
enum MONEY_MANAGEMENT_MODE
{
   MODE_COMPOUND,            // 复利模式
   MODE_FIXED                 // 固定模式
};

input group "===== 资金管理设置 ====="
input MONEY_MANAGEMENT_MODE InpMoneyManagementMode = MODE_COMPOUND; // 资金管理模式
input double InpEquityPerLot = 5000.0;                           // 每多少净值建仓0.01手
input double InpMaxLotDivisor = 0.01;                            // 基础仓位(手)
input double InpMaxRiskPercent = 10.0;                           // 最大总仓位风险比例(%)

//--- 加仓参数
input group "===== 加仓设置 ====="
input int    InpMaxLevels = 15;                                  // 最大加仓层数
input double InpATRMultiplier = 5.0;                             // ATR加仓间距系数
input double InpFirstLevelMultiplier = 2.0;                      // 首次加仓间距系数(相对于普通间距)
input double InpInitialLotMultiplier = 1.5;                      // 初始加仓倍数(1-5层)
input double InpMidLotMultiplier = 1.3;                          // 中期加仓倍数(6-10层)
input double InpLateLotMultiplier = 1.2;                         // 后期加仓倍数(11-15层)
input double InpFinalLotMultiplier = 1.1;                        // 最终加仓倍数(>15层)

//--- 止损设置
enum STOP_LOSS_MODE
{
   SL_ATR,                   // ATR动态止损
   SL_FIXED                  // 固定点数止损
};

input group "===== 止损设置 ====="
input STOP_LOSS_MODE InpStopLossMode = SL_ATR;                   // 止损模式
input double InpATRStopMultiplier = 2.0;                         // ATR止损倍数
input int    InpFixedStopLoss = 400;                             // 固定止损点数
input bool   InpUseBreakEven = true;                             // 启用移动保本止损
input int    InpBreakEvenStart = 300;                            // 移动保本启动点数
input int    InpBreakEvenProfit = 10;                            // 保本后额外获利点数

//--- 止盈设置
input group "===== 止盈设置 ====="
input double InpBaseProfitTarget = 2000.0;                       // 基础目标盈利点数
input double InpRiskPercent = 2.0;                               // 风险比例(%)
input bool   InpUsePartialClose = true;                           // 启用分批平仓
input double InpPartialClosePercent1 = 50.0;                     // 第一次平仓比例(%)
input double InpPartialClosePercent2 = 30.0;                     // 第二次平仓比例(%)

//--- 订单复活设置
input group "===== 订单复活设置 ====="
input bool   InpUseRevival = true;                                // 启用订单复活
input double InpRevivalATRMultiplier = 1.5;                      // 复活ATR倍数
input int    InpRevivalRSIThresholdBuy = 55;                     // 多单复活RSI阈值
input int    InpRevivalRSIThresholdSell = 45;                    // 空单复活RSI阈值
input double InpRevivalATRFilter = 0.8;                          // 复活波动率过滤(原ATR比例)
input int    InpRevivalTimeInterval = 15;                         // 复活时间间隔(分钟)
input double InpRevivalLotMultiplier = 0.8;                      // 复活手数倍数
input int    InpMaxRevivalCount = 3;                             // 最大复活次数

//--- 指标参数
input group "===== 指标设置 ====="
input int    InpATRPeriod = 14;                                  // ATR周期
input int    InpRSIPeriod = 14;                                  // RSI周期

//--- 全局变量
int handle_atr;                // ATR指标句柄
int handle_rsi;                // RSI指标句柄
double current_atr;            // 当前ATR值
double current_rsi;            // 当前RSI值
int total_orders;              // 当前订单总数
double total_lot;              // 当前总手数
double account_equity;         // 账户净值
double account_balance;        // 账户余额
double account_profit;         // 当前浮动盈亏
double point_value;            // 点值
double current_spread;         // 当前点差

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化交易对象
   Trade.SetExpertMagicNumber(123456);
   Trade.SetMarginMode();
   Trade.SetTypeFillingBySymbol(Symbol());
   Trade.SetDeviationInPoints(10);
   
   // 初始化品种信息
   SymbolInfo.Name(Symbol());
   point_value = SymbolInfo.Point();
   
   // 创建指标句柄
   handle_atr = iATR(Symbol(), PERIOD_CURRENT, InpATRPeriod);
   handle_rsi = iRSI(Symbol(), PERIOD_CURRENT, InpRSIPeriod, PRICE_CLOSE);
   
   if(handle_atr == INVALID_HANDLE || handle_rsi == INVALID_HANDLE)
   {
      Print("指标句柄创建失败!");
      return INIT_FAILED;
   }
   
   // 初始化订单数组
   ArrayResize(orders, 0);
   LoadExistingPositions();
   
   // 显示信息面板
   CreateInfoPanel();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   if(handle_atr != INVALID_HANDLE)
      IndicatorRelease(handle_atr);
      
   if(handle_rsi != INVALID_HANDLE)
      IndicatorRelease(handle_rsi);
      
   // 删除信息面板
   ObjectsDeleteAll(0, "InfoPanel_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新市场数据
   if(!UpdateMarketData())
      return;
      
   // 更新订单状态
   UpdateOrdersStatus();
   
   // 检查订单复活
   if(InpUseRevival)
      CheckOrderRevival();
      
   // 更新移动止损
   if(InpUseBreakEven)
      UpdateBreakEven();
      
   // 更新信息面板
   UpdateInfoPanel();
}

//+------------------------------------------------------------------+
//| 更新市场数据                                                     |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
   // 获取账户信息
   account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
   
   // 获取点差
   current_spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * SymbolInfo.Point();
   
   // 更新指标数据
   double atr_buffer[];
   double rsi_buffer[];
   
   if(CopyBuffer(handle_atr, 0, 0, 1, atr_buffer) <= 0)
      return false;
      
   if(CopyBuffer(handle_rsi, 0, 0, 1, rsi_buffer) <= 0)
      return false;
      
   current_atr = atr_buffer[0];
   current_rsi = rsi_buffer[0];
   
   return true;
}

//+------------------------------------------------------------------+
//| 加载现有持仓                                                     |
//+------------------------------------------------------------------+
void LoadExistingPositions()
{
   total_orders = 0;
   total_lot = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) == Symbol() && 
            PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
         {
            int idx = ArraySize(orders);
            ArrayResize(orders, idx + 1);
            
            orders[idx].ticket = PositionGetInteger(POSITION_TICKET);
            orders[idx].open_time = (datetime)PositionGetInteger(POSITION_TIME);
            orders[idx].open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            orders[idx].lot = PositionGetDouble(POSITION_VOLUME);
            orders[idx].sl = PositionGetDouble(POSITION_SL);
            orders[idx].tp = PositionGetDouble(POSITION_TP);
            orders[idx].direction = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 1 : -1;
            orders[idx].comment = PositionGetString(POSITION_COMMENT);
            
            // 解析注释中的层级和复活信息
            string comment = orders[idx].comment;
            if(StringFind(comment, "Level=") >= 0)
            {
               string level_str = StringSubstr(comment, StringFind(comment, "Level=") + 6, 2);
               orders[idx].level = (int)StringToInteger(level_str);
            }
            else
            {
               orders[idx].level = 1; // 默认为第一层
            }
            
            if(StringFind(comment, "Revived=") >= 0)
            {
               string revived_str = StringSubstr(comment, StringFind(comment, "Revived=") + 8, 1);
               orders[idx].is_revived = true;
               orders[idx].revival_count = (int)StringToInteger(revived_str);
            }
            else
            {
               orders[idx].is_revived = false;
               orders[idx].revival_count = 0;
            }
            
            total_orders++;
            total_lot += orders[idx].lot;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 更新订单状态                                                     |
//+------------------------------------------------------------------+
void UpdateOrdersStatus()
{
   // 重新加载所有持仓
   LoadExistingPositions();
   
   // 检查是否需要开仓
   if(total_orders == 0)
   {
      // 这里可以添加开仓信号判断
      // 本EA主要关注资金管理和风险控制，开仓信号可以根据需要自行添加
   }
   else
   {
      // 检查是否需要加仓
      CheckAddPosition();
      
      // 检查是否需要部分平仓
      if(InpUsePartialClose)
         CheckPartialClose();
   }
}

//+------------------------------------------------------------------+
//| 检查加仓条件                                                     |
//+------------------------------------------------------------------+
void CheckAddPosition()
{
   if(total_orders >= InpMaxLevels)
      return; // 达到最大加仓层数
      
   // 计算当前总风险
   double total_risk = CalculateTotalRisk();
   if(total_risk > InpMaxRiskPercent)
      return; // 超过最大风险比例
   
   // 获取最后一个订单
   int last_idx = -1;
   datetime last_time = 0;
   
   for(int i = 0; i < ArraySize(orders); i++)
   {
      if(orders[i].open_time > last_time)
      {
         last_time = orders[i].open_time;
         last_idx = i;
      }
   }
   
   if(last_idx < 0)
      return;
      
   // 计算加仓间距
   double add_distance = CalculateAddDistance(orders[last_idx].level);
   
   // 获取当前价格
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   // 检查是否满足加仓条件
   bool add_condition = false;
   double entry_price = 0;
   
   if(orders[last_idx].direction > 0) // 多单
   {
      if(last_tick.ask >= orders[last_idx].open_price + add_distance)
      {
         add_condition = true;
         entry_price = last_tick.ask;
      }
   }
   else // 空单
   {
      if(last_tick.bid <= orders[last_idx].open_price - add_distance)
      {
         add_condition = true;
         entry_price = last_tick.bid;
      }
   }
   
   // 执行加仓
   if(add_condition)
   {
      // 计算加仓手数
      double add_lot = CalculateAddLot(orders[last_idx].lot, orders[last_idx].level + 1);
      
      // 计算止损止盈
      double sl = CalculateStopLoss(entry_price, orders[last_idx].direction);
      double tp = CalculateTakeProfit(entry_price, orders[last_idx].direction);
      
      // 构建注释
      string comment = StringFormat("Level=%d", orders[last_idx].level + 1);
      
      // 执行交易
      if(orders[last_idx].direction > 0) // 多单
      {
         Trade.Buy(add_lot, Symbol(), entry_price, sl, tp, comment);
      }
      else // 空单
      {
         Trade.Sell(add_lot, Symbol(), entry_price, sl, tp, comment);
      }
   }
}

//+------------------------------------------------------------------+
//| 计算加仓间距                                                     |
//+------------------------------------------------------------------+
double CalculateAddDistance(int level)
{
   double base_distance = current_atr * InpATRMultiplier;
   
   // 首次加仓间距更大
   if(level == 1)
      return base_distance * InpFirstLevelMultiplier;
      
   // 趋势加速时可以缩短间距（这里可以添加趋势加速判断逻辑）
   // 简单示例：如果RSI超过70或低于30，认为趋势加速
   if(current_rsi > 70 || current_rsi < 30)
      return base_distance * 0.8;
      
   return base_distance;
}

//+------------------------------------------------------------------+
//| 格式化手数，确保符合交易品种的最小手数和步长要求                 |
//+------------------------------------------------------------------+
double formatlots(string symbol, double lots)
{
   double a = 0;
   double minilots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double steplots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(lots < minilots)
      return(0);
   else
   {
      double a1 = MathFloor(lots / minilots) * minilots;
      a = a1 + MathFloor((lots - a1) / steplots) * steplots;
   }
   
   return(a);
}

//+------------------------------------------------------------------+
//| 计算加仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateAddLot(double prev_lot, int level)
{
   // 根据层级选择倍数
   double multiplier;
   
   if(level <= 5)
      multiplier = InpInitialLotMultiplier;
   else if(level <= 10)
      multiplier = InpMidLotMultiplier;
   else if(level <= 15)
      multiplier = InpLateLotMultiplier;
   else
      multiplier = InpFinalLotMultiplier;
      
   // 非线性递减公式
   double lot_multiplier = 1 + (multiplier - 1) / level;
   
   // 计算新手数
   double new_lot = NormalizeDouble(prev_lot * lot_multiplier, 2);
   
   // 检查是否超过最大手数限制
   double max_lot = NormalizeDouble(InpMaxLotDivisor * (account_equity / InpEquityPerLot), 2);
   if(new_lot > max_lot)
      new_lot = max_lot;
      
   // 使用formatlots函数格式化手数
   new_lot = formatlots(Symbol(), new_lot);
      
   return new_lot;
}

//+------------------------------------------------------------------+
//| 计算开仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateInitialLot()
{
   double lot;
   
   if(InpMoneyManagementMode == MODE_COMPOUND)
   {
      // 复利模式 - 新仓位=基础仓位×（当前账户余额/5000）
      lot = NormalizeDouble(InpMaxLotDivisor * (account_balance / InpEquityPerLot), 2);
   }
   else
   {
      // 固定模式
      lot = InpMaxLotDivisor; // 使用基础仓位作为固定模式手数
   }
   
   // 检查最小/最大手数限制
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   
   if(lot < min_lot) lot = min_lot;
   if(lot > max_lot) lot = max_lot;
   
   // 使用formatlots函数格式化手数
   lot = formatlots(Symbol(), lot);
   
   return lot;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CalculateStopLoss(double entry_price, int direction)
{
   double sl;
   
   if(InpStopLossMode == SL_ATR)
   {
      // ATR动态止损
      double sl_distance = current_atr * InpATRStopMultiplier;
      
      if(direction > 0) // 多单
         sl = entry_price - sl_distance;
      else // 空单
         sl = entry_price + sl_distance;
   }
   else
   {
      // 固定点数止损
      double sl_distance = InpFixedStopLoss * point_value;
      
      if(direction > 0) // 多单
         sl = entry_price - sl_distance;
      else // 空单
         sl = entry_price + sl_distance;
   }
   
   return NormalizeDouble(sl, SymbolInfo.Digits());
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                     |
//+------------------------------------------------------------------+
double CalculateTakeProfit(double entry_price, int direction)
{
   // 动态计算盈利目标
   double risk_amount = account_equity * (InpRiskPercent / 100.0);
   double target = MathMax(InpBaseProfitTarget, risk_amount * (current_atr / 0.0015));
   
   // 转换为价格
   double tp_distance = target * point_value;
   
   double tp;
   if(direction > 0) // 多单
      tp = entry_price + tp_distance;
   else // 空单
      tp = entry_price - tp_distance;
      
   return NormalizeDouble(tp, SymbolInfo.Digits());
}

//+------------------------------------------------------------------+
//| 更新移动保本止损                                                 |
//+------------------------------------------------------------------+
void UpdateBreakEven()
{
   for(int i = 0; i < ArraySize(orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      double current_price;
      if(orders[i].direction > 0) // 多单
         current_price = last_tick.bid;
      else // 空单
         current_price = last_tick.ask;
         
      // 计算当前盈利点数
      double profit_points;
      if(orders[i].direction > 0) // 多单
         profit_points = (current_price - orders[i].open_price) / point_value;
      else // 空单
         profit_points = (orders[i].open_price - current_price) / point_value;
         
      // 检查是否达到移动保本条件
      if(profit_points >= InpBreakEvenStart)
      {
         double new_sl;
         if(orders[i].direction > 0) // 多单
            new_sl = orders[i].open_price + InpBreakEvenProfit * point_value;
         else // 空单
            new_sl = orders[i].open_price - InpBreakEvenProfit * point_value;
            
         // 检查是否需要修改止损
         if((orders[i].direction > 0 && (orders[i].sl < orders[i].open_price || orders[i].sl == 0)) || 
            (orders[i].direction < 0 && (orders[i].sl > orders[i].open_price || orders[i].sl == 0)))
         {
            Trade.PositionModify(orders[i].ticket, new_sl, orders[i].tp);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查部分平仓                                                     |
//+------------------------------------------------------------------+
void CheckPartialClose()
{
   // 计算当前总盈利
   double total_profit = 0;
   for(int i = 0; i < ArraySize(orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      double current_price;
      if(orders[i].direction > 0) // 多单
         current_price = last_tick.bid;
      else // 空单
         current_price = last_tick.ask;
         
      // 计算当前盈利点数
      double profit_points;
      if(orders[i].direction > 0) // 多单
         profit_points = (current_price - orders[i].open_price) / point_value;
      else // 空单
         profit_points = (orders[i].open_price - current_price) / point_value;
         
      total_profit += profit_points * orders[i].lot / 0.01; // 转换为标准手的盈利
   }
   
   // 动态计算目标盈利
   double risk_amount = account_equity * (InpRiskPercent / 100.0);
   double target = MathMax(InpBaseProfitTarget, risk_amount * (current_atr / 0.0015));
   
   // 检查是否达到第一次平仓条件
   static bool first_close_done = false;
   static bool second_close_done = false;
   
   if(!first_close_done && total_profit >= target * 0.7) // 达到70%目标时开始第一次平仓
   {
      // 平掉InpPartialClosePercent1%的仓位
      ClosePartialPositions(InpPartialClosePercent1 / 100.0);
      first_close_done = true;
   }
   else if(first_close_done && !second_close_done && total_profit >= target * 0.9) // 达到90%目标时开始第二次平仓
   {
      // 平掉InpPartialClosePercent2%的仓位
      ClosePartialPositions(InpPartialClosePercent2 / 100.0);
      second_close_done = true;
   }
   else if(first_close_done && second_close_done && total_profit >= target) // 达到100%目标时平掉剩余仓位
   {
      // 平掉所有剩余仓位
      CloseAllPositions();
      first_close_done = false;
      second_close_done = false;
   }
   
   // 如果总盈利回撤超过30%，平掉所有仓位
   static double max_profit = 0;
   if(total_profit > max_profit)
      max_profit = total_profit;
      
   if(max_profit > 0 && total_profit < max_profit * 0.7)
   {
      CloseAllPositions();
      max_profit = 0;
      first_close_done = false;
      second_close_done = false;
   }
}

//+------------------------------------------------------------------+
//| 部分平仓                                                         |
//+------------------------------------------------------------------+
void ClosePartialPositions(double percent)
{
   double lots_to_close = total_lot * percent;
   double closed_lots = 0;
   
   // 从最早的订单开始平仓
   while(closed_lots < lots_to_close && ArraySize(orders) > 0)
   {
      // 找到最早的订单
      int earliest_idx = -1;
      datetime earliest_time = TimeCurrent();
      
      for(int i = 0; i < ArraySize(orders); i++)
      {
         if(orders[i].open_time < earliest_time)
         {
            earliest_time = orders[i].open_time;
            earliest_idx = i;
         }
      }
      
      if(earliest_idx < 0)
         break;
         
      // 计算要平仓的手数
      double lot_to_close = MathMin(orders[earliest_idx].lot, lots_to_close - closed_lots);
      
      // 执行平仓
      if(lot_to_close < orders[earliest_idx].lot)
      {
         // 部分平仓
         Trade.PositionClosePartial(orders[earliest_idx].ticket, lot_to_close);
         closed_lots += lot_to_close;
      }
      else
      {
         // 全部平仓
         Trade.PositionClose(orders[earliest_idx].ticket);
         closed_lots += orders[earliest_idx].lot;
      }
   }
   
   // 重新加载持仓
   LoadExistingPositions();
}

//+------------------------------------------------------------------+
//| 平掉所有仓位                                                     |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   for(int i = 0; i < ArraySize(orders); i++)
   {
      Trade.PositionClose(orders[i].ticket);
   }
   
   // 重新加载持仓
   LoadExistingPositions();
}

//+------------------------------------------------------------------+
//| 计算总风险                                                       |
//+------------------------------------------------------------------+
double CalculateTotalRisk()
{
   double total_risk = 0;
   
   for(int i = 0; i < ArraySize(orders); i++)
   {
      // 计算每个订单的风险
      double order_risk;
      
      if(orders[i].sl != 0) // 如果有止损
      {
         // 计算止损距离
         double sl_distance;
         if(orders[i].direction > 0) // 多单
            sl_distance = orders[i].open_price - orders[i].sl;
         else // 空单
            sl_distance = orders[i].sl - orders[i].open_price;
            
         // 计算风险金额
         double risk_amount = sl_distance * orders[i].lot / point_value;
         
         // 计算风险百分比
         order_risk = (risk_amount / account_equity) * 100.0;
      }
      else // 如果没有止损，使用默认风险计算
      {
         double default_sl_distance;
         if(InpStopLossMode == SL_ATR)
            default_sl_distance = current_atr * InpATRStopMultiplier;
         else
            default_sl_distance = InpFixedStopLoss * point_value;
            
         // 计算风险金额
         double risk_amount = default_sl_distance * orders[i].lot / point_value;
         
         // 计算风险百分比
         order_risk = (risk_amount / account_equity) * 100.0;
      }
      
      // 根据层级递减风险（层级越高，风险占比越低）
      order_risk = order_risk * (1.0 - (orders[i].level - 1) * 0.05);
      
      total_risk += order_risk;
   }
   
   return total_risk;
}

//+------------------------------------------------------------------+
//| 检查订单复活                                                     |
//+------------------------------------------------------------------+
void CheckOrderRevival()
{
   // 创建一个数组来存储已被止损的订单信息
   struct ClosedOrderData
   {
      datetime close_time;    // 平仓时间
      double    open_price;    // 开仓价格
      double    close_price;   // 平仓价格
      double    lot;           // 手数
      int       direction;     // 方向
      int       level;         // 层级
      int       revival_count; // 复活次数
      double    atr_at_close;  // 平仓时的ATR值
      string    comment;       // 订单注释
   };
   
   static ClosedOrderData closed_orders[];
   static datetime last_history_check = 0;
   
   // 每分钟检查一次历史订单
   if(TimeCurrent() - last_history_check > 60)
   {
      // 检查历史订单
      HistorySelect(TimeCurrent() - 86400, TimeCurrent()); // 检查最近24小时的历史
      
      for(int i = 0; i < HistoryDealsTotal(); i++)
      {
         ulong deal_ticket = HistoryDealGetTicket(i);
         if(deal_ticket <= 0) continue;
         
         // 检查是否是我们的EA的订单
         if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != Trade.RequestMagic()) continue;
         if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != Symbol()) continue;
         
         // 检查是否是平仓操作
         if(HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) != DEAL_ENTRY_OUT) continue;
         
         // 检查是否是止损平仓
         if(HistoryDealGetInteger(deal_ticket, DEAL_REASON) != DEAL_REASON_SL) continue;
         
         // 获取订单信息
         ulong position_ticket = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);
         string comment = HistoryDealGetString(deal_ticket, DEAL_COMMENT);
         
         // 检查是否是手动平仓
         if(StringFind(comment, "MANUAL_CLOSE") >= 0) continue;
         
         // 检查是否已经在复活列表中
         bool already_in_list = false;
         for(int j = 0; j < ArraySize(closed_orders); j++)
         {
            if(StringFind(closed_orders[j].comment, StringFormat("#%d", position_ticket)) >= 0)
            {
               already_in_list = true;
               break;
            }
         }
         
         if(already_in_list) continue;
         
         // 添加到复活列表
         int idx = ArraySize(closed_orders);
         ArrayResize(closed_orders, idx + 1);
         
         closed_orders[idx].close_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
         closed_orders[idx].open_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
         closed_orders[idx].close_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
         closed_orders[idx].lot = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
         closed_orders[idx].direction = (HistoryDealGetInteger(deal_ticket, DEAL_TYPE) == DEAL_TYPE_SELL) ? -1 : 1;
         closed_orders[idx].atr_at_close = current_atr; // 使用当前ATR作为近似值
         closed_orders[idx].comment = comment;
         
         // 解析注释中的层级和复活信息
         if(StringFind(comment, "Level=") >= 0)
         {
            string level_str = StringSubstr(comment, StringFind(comment, "Level=") + 6, 2);
            closed_orders[idx].level = (int)StringToInteger(level_str);
         }
         else
         {
            closed_orders[idx].level = 1; // 默认为第一层
         }
         
         if(StringFind(comment, "Revived=") >= 0)
         {
            string revived_str = StringSubstr(comment, StringFind(comment, "Revived=") + 8, 1);
            closed_orders[idx].revival_count = (int)StringToInteger(revived_str);
         }
         else
         {
            closed_orders[idx].revival_count = 0;
         }
         
         // 添加订单号到注释中，以便后续识别
         closed_orders[idx].comment = StringFormat("#%d %s", position_ticket, closed_orders[idx].comment);
      }
      
      last_history_check = TimeCurrent();
   }
   
   // 检查是否有订单可以复活
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   for(int i = ArraySize(closed_orders) - 1; i >= 0; i--)
   {
      // 检查复活次数是否超过限制
      if(closed_orders[i].revival_count >= InpMaxRevivalCount)
      {
         // 从列表中移除
         for(int j = i; j < ArraySize(closed_orders) - 1; j++)
            closed_orders[j] = closed_orders[j + 1];
            
         ArrayResize(closed_orders, ArraySize(closed_orders) - 1);
         continue;
      }
      
      // 检查时间间隔
      if(TimeCurrent() - closed_orders[i].close_time < InpRevivalTimeInterval * 60)
         continue;
         
      // 检查价格重入条件
      bool price_condition = false;
      if(closed_orders[i].direction > 0) // 原来是多单
      {
         if(last_tick.ask > closed_orders[i].close_price + closed_orders[i].atr_at_close * InpRevivalATRMultiplier)
            price_condition = true;
      }
      else // 原来是空单
      {
         if(last_tick.bid < closed_orders[i].close_price - closed_orders[i].atr_at_close * InpRevivalATRMultiplier)
            price_condition = true;
      }
      
      // 检查动量确认条件
      bool momentum_condition = false;
      if(closed_orders[i].direction > 0 && current_rsi > InpRevivalRSIThresholdBuy) // 多单
         momentum_condition = true;
      else if(closed_orders[i].direction < 0 && current_rsi < InpRevivalRSIThresholdSell) // 空单
         momentum_condition = true;
         
      // 检查波动率过滤条件
      bool volatility_condition = (current_atr < closed_orders[i].atr_at_close * InpRevivalATRFilter);
      
      // 如果满足所有条件，复活订单
      if(price_condition && momentum_condition && volatility_condition)
      {
         // 计算复活手数
         double revival_lot = NormalizeDouble(closed_orders[i].lot * InpRevivalLotMultiplier, 2);
         
         // 计算止损止盈
         double entry_price = (closed_orders[i].direction > 0) ? last_tick.ask : last_tick.bid;
         double sl = CalculateStopLoss(entry_price, closed_orders[i].direction);
         double tp = CalculateTakeProfit(entry_price, closed_orders[i].direction);
         
         // 构建注释
         string comment = StringFormat("Level=%d Revived=%d", 
                                      closed_orders[i].level, 
                                      closed_orders[i].revival_count + 1);
         
         // 执行交易
         bool result = false;
         if(closed_orders[i].direction > 0) // 多单
            result = Trade.Buy(revival_lot, Symbol(), entry_price, sl, tp, comment);
         else // 空单
            result = Trade.Sell(revival_lot, Symbol(), entry_price, sl, tp, comment);
            
         // 如果交易成功，从列表中移除
         if(result)
         {
            for(int j = i; j < ArraySize(closed_orders) - 1; j++)
               closed_orders[j] = closed_orders[j + 1];
               
            ArrayResize(closed_orders, ArraySize(closed_orders) - 1);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 创建背景
   ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 10);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 300);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 200);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrDarkSlateGray);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
   
   // 创建标题
   ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 20);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_SELECTABLE, false);
   ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "ATR动态加仓策略");
   ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, "Arial Bold");
   
   // 创建信息标签
   string labels[] = {"账户净值:", "当前订单数:", "总手数:", "总风险(%):", "当前ATR:", "当前RSI:", "目标盈利:"};
   
   for(int i = 0; i < ArraySize(labels); i++)
   {
      // 创建标签
      ObjectCreate(0, "InfoPanel_Label_" + IntegerToString(i), OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_XDISTANCE, 20);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_YDISTANCE, 40 + i * 20);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_COLOR, clrWhite);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_SELECTABLE, false);
      ObjectSetString(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_TEXT, labels[i]);
      ObjectSetString(0, "InfoPanel_Label_" + IntegerToString(i), OBJPROP_FONT, "Arial");
      
      // 创建值
      ObjectCreate(0, "InfoPanel_Value_" + IntegerToString(i), OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_XDISTANCE, 150);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_YDISTANCE, 40 + i * 20);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_COLOR, clrYellow);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_SELECTABLE, false);
      ObjectSetString(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_TEXT, "0");
      ObjectSetString(0, "InfoPanel_Value_" + IntegerToString(i), OBJPROP_FONT, "Arial");
   }
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   // 更新值
   ObjectSetString(0, "InfoPanel_Value_0", OBJPROP_TEXT, DoubleToString(account_equity, 2));
   ObjectSetString(0, "InfoPanel_Value_1", OBJPROP_TEXT, IntegerToString(total_orders));
   ObjectSetString(0, "InfoPanel_Value_2", OBJPROP_TEXT, DoubleToString(total_lot, 2));
   ObjectSetString(0, "InfoPanel_Value_3", OBJPROP_TEXT, DoubleToString(CalculateTotalRisk(), 2));
   ObjectSetString(0, "InfoPanel_Value_4", OBJPROP_TEXT, DoubleToString(current_atr, 5));
   ObjectSetString(0, "InfoPanel_Value_5", OBJPROP_TEXT, DoubleToString(current_rsi, 2));
   
   // 计算目标盈利
   double risk_amount = account_equity * (InpRiskPercent / 100.0);
   double target = MathMax(InpBaseProfitTarget, risk_amount * (current_atr / 0.0015));
   ObjectSetString(0, "InfoPanel_Value_6", OBJPROP_TEXT, DoubleToString(target, 0) + " 点");
   
   // 根据风险级别改变颜色
   double risk = CalculateTotalRisk();
   if(risk < InpMaxRiskPercent * 0.5)
      ObjectSetInteger(0, "InfoPanel_Value_3", OBJPROP_COLOR, clrLime);
   else if(risk < InpMaxRiskPercent * 0.8)
      ObjectSetInteger(0, "InfoPanel_Value_3", OBJPROP_COLOR, clrYellow);
   else
      ObjectSetInteger(0, "InfoPanel_Value_3", OBJPROP_COLOR, clrRed);
}

//+------------------------------------------------------------------+