//+------------------------------------------------------------------+
//|                                              自适应权重系统测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "自适应矩阵权重系统功能测试脚本"
#property script_show_inputs

// 测试参数
input int TestCycles = 50;           // 测试周期数
input bool EnableDetailedLog = true; // 启用详细日志
input double TestVolatilityRange = 2.0; // 测试波动率范围

//+------------------------------------------------------------------+
//| 自适应权重系统测试脚本                                            |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始自适应矩阵权重系统测试");
    Print("测试周期: ", TestCycles);
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("波动率范围: ", TestVolatilityRange);
    Print("========================================");
    
    // 测试1: 市场稳定性计算测试
    TestMarketStabilityCalculation();
    
    // 测试2: 权重动态调整测试
    TestDynamicWeightAdjustment();
    
    // 测试3: 波动率触发机制测试
    TestVolatilityTriggerMechanism();
    
    // 测试4: 状态转移学习测试
    TestStateTransitionLearning();
    
    // 测试5: 预测准确性测试
    TestPredictionAccuracy();
    
    // 测试6: 性能压力测试
    TestPerformanceStress();
    
    Print("========================================");
    Print("✅ 自适应权重系统测试完成");
}

//+------------------------------------------------------------------+
//| 测试市场稳定性计算                                                |
//+------------------------------------------------------------------+
void TestMarketStabilityCalculation()
{
    Print("📋 测试1: 市场稳定性计算测试");
    
    // 模拟不同波动率场景
    double testVolatilities[] = {0.001, 0.005, 0.01, 0.02, 0.05};
    double testPrices[] = {1.1000, 1.1000, 1.1000, 1.1000, 1.1000};
    
    for(int i = 0; i < ArraySize(testVolatilities); i++) {
        double volatilityRatio = testVolatilities[i] / testPrices[i];
        double stabilityIndex = 1.0 - MathMin(volatilityRatio * 100, 1.0);
        stabilityIndex = MathMax(0.0, MathMin(1.0, stabilityIndex));
        
        Print("波动率: ", testVolatilities[i], " 稳定性指数: ", DoubleToString(stabilityIndex, 3));
    }
    
    Print("✅ 市场稳定性计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试权重动态调整                                                  |
//+------------------------------------------------------------------+
void TestDynamicWeightAdjustment()
{
    Print("📋 测试2: 权重动态调整测试");
    
    // 模拟不同稳定性指数下的权重调整
    double testStabilityIndices[] = {0.1, 0.3, 0.5, 0.7, 0.9};
    
    for(int i = 0; i < ArraySize(testStabilityIndices); i++) {
        double stabilityIndex = testStabilityIndices[i];
        
        // 计算权重 (与实际算法一致)
        double historicalWeight = MathMin(0.7, 0.4 + (stabilityIndex * 0.3));
        double recentWeight = 1.0 - historicalWeight;
        
        Print("稳定性: ", DoubleToString(stabilityIndex, 1),
              " 历史权重: ", DoubleToString(historicalWeight, 2),
              " 近期权重: ", DoubleToString(recentWeight, 2));
    }
    
    Print("✅ 权重动态调整测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试波动率触发机制                                                |
//+------------------------------------------------------------------+
void TestVolatilityTriggerMechanism()
{
    Print("📋 测试3: 波动率触发机制测试");
    
    double baselineVolatility = 0.01;
    double triggerThreshold = 1.5;
    
    // 模拟波动率变化
    double testVolatilities[] = {0.01, 0.012, 0.015, 0.018, 0.025};
    
    for(int i = 0; i < ArraySize(testVolatilities); i++) {
        double currentVolatility = testVolatilities[i];
        double volatilityRatio = currentVolatility / (baselineVolatility + 0.0001);
        bool triggered = (volatilityRatio > triggerThreshold);
        
        Print("当前波动率: ", DoubleToString(currentVolatility, 4),
              " 比率: ", DoubleToString(volatilityRatio, 2),
              " 触发: ", triggered ? "是" : "否");
    }
    
    Print("✅ 波动率触发机制测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试状态转移学习                                                  |
//+------------------------------------------------------------------+
void TestStateTransitionLearning()
{
    Print("📋 测试4: 状态转移学习测试");
    
    // 模拟状态转移序列
    string stateNames[] = {"无压缩", "轻度压缩", "中度压缩", "重度压缩", "极度压缩", "压缩峰值", "压缩衰竭"};
    int transitionSequence[] = {0, 1, 2, 3, 4, 5, 0, 1, 2, 4, 6, 0}; // 模拟转移序列
    
    // 初始化转移计数矩阵
    int transitionCounts[7][7];
    ArrayInitialize(transitionCounts, 0);
    
    // 统计转移
    for(int i = 0; i < ArraySize(transitionSequence) - 1; i++) {
        int fromState = transitionSequence[i];
        int toState = transitionSequence[i + 1];
        transitionCounts[fromState][toState]++;
        
        if(EnableDetailedLog) {
            Print("转移: ", stateNames[fromState], " → ", stateNames[toState]);
        }
    }
    
    // 计算转移概率
    for(int i = 0; i < 7; i++) {
        int totalTransitions = 0;
        for(int j = 0; j < 7; j++) {
            totalTransitions += transitionCounts[i][j];
        }
        
        if(totalTransitions > 0) {
            Print("从 ", stateNames[i], " 的转移概率:");
            for(int j = 0; j < 7; j++) {
                if(transitionCounts[i][j] > 0) {
                    double probability = (double)transitionCounts[i][j] / totalTransitions;
                    Print("  → ", stateNames[j], ": ", DoubleToString(probability, 2));
                }
            }
        }
    }
    
    Print("✅ 状态转移学习测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试预测准确性                                                    |
//+------------------------------------------------------------------+
void TestPredictionAccuracy()
{
    Print("📋 测试5: 预测准确性测试");
    
    // 模拟预测场景
    struct PredictionTest {
        string currentState;
        string predictedState;
        double confidence;
        string actualState;
        bool correct;
    };
    
    PredictionTest tests[] = {
        {"重度压缩", "极度压缩", 0.8, "极度压缩", true},
        {"极度压缩", "压缩峰值", 0.7, "压缩峰值", true},
        {"压缩峰值", "无压缩", 0.6, "轻度压缩", false},
        {"中度压缩", "重度压缩", 0.9, "重度压缩", true},
        {"极度压缩", "压缩衰竭", 0.3, "压缩衰竭", true}
    };
    
    int correctPredictions = 0;
    double totalConfidence = 0.0;
    
    for(int i = 0; i < ArraySize(tests); i++) {
        PredictionTest test = tests[i];
        
        Print("预测 ", i+1, ": ", test.currentState, " → ", test.predictedState,
              " (置信度: ", DoubleToString(test.confidence, 2), ")",
              " 实际: ", test.actualState,
              " 结果: ", test.correct ? "✓" : "✗");
        
        if(test.correct) correctPredictions++;
        totalConfidence += test.confidence;
    }
    
    double accuracy = (double)correctPredictions / ArraySize(tests) * 100;
    double avgConfidence = totalConfidence / ArraySize(tests);
    
    Print("预测准确率: ", DoubleToString(accuracy, 1), "%");
    Print("平均置信度: ", DoubleToString(avgConfidence, 2));
    
    Print("✅ 预测准确性测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试性能压力                                                      |
//+------------------------------------------------------------------+
void TestPerformanceStress()
{
    Print("📋 测试6: 性能压力测试");
    
    uint startTime = GetTickCount();
    
    // 执行大量权重计算
    for(int i = 0; i < TestCycles; i++) {
        // 模拟市场稳定性计算
        double volatilityRatio = MathRand() / 32767.0 * 0.1; // 0-0.1范围
        double stabilityIndex = 1.0 - MathMin(volatilityRatio * 100, 1.0);
        
        // 模拟权重调整
        double historicalWeight = MathMin(0.7, 0.4 + (stabilityIndex * 0.3));
        double recentWeight = 1.0 - historicalWeight;
        
        // 模拟矩阵更新 (简化版)
        for(int j = 0; j < 7; j++) {
            for(int k = 0; k < 7; k++) {
                double historicalProb = MathRand() / 32767.0;
                double recentProb = MathRand() / 32767.0;
                double adaptiveProb = historicalProb * historicalWeight + recentProb * recentWeight;
                // 这里只是计算，不存储
            }
        }
    }
    
    uint endTime = GetTickCount();
    uint executionTime = endTime - startTime;
    
    Print("执行 ", TestCycles, " 次权重计算耗时: ", executionTime, " 毫秒");
    Print("平均每次计算: ", (double)executionTime/TestCycles, " 毫秒");
    
    // 性能评估
    if(executionTime < 100) {
        Print("性能评级: 优秀 ⭐⭐⭐⭐⭐");
    } else if(executionTime < 500) {
        Print("性能评级: 良好 ⭐⭐⭐⭐");
    } else if(executionTime < 1000) {
        Print("性能评级: 一般 ⭐⭐⭐");
    } else {
        Print("性能评级: 需要优化 ⭐⭐");
    }
    
    Print("✅ 性能压力测试完成\n");
}
