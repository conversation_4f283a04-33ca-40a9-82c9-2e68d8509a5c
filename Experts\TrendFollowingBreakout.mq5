//+------------------------------------------------------------------+
//|                                    TrendFollowingBreakout.mq5   |
//|                          修正逆势逻辑的顺势突破策略              |
//|                                                   Augment Agent |
//+------------------------------------------------------------------+
#property copyright "Augment Agent - 基于用户深度分析优化"
#property link      ""
#property version   "3.0"
#property strict

// 输入参数
input group "=== 核心参数 ==="
input int    InpStrength = 5;                    // 高低点检测强度
input double InpRiskPercent = 1.0;               // 单笔风险百分比
input int    InpMagicNumber = 20241219;          // 魔术数字

input group "=== 趋势线参数 ==="
input int    InpMinTrendPoints = 3;              // 最少趋势点数量
input int    InpMaxTrendAge = 50;                // 趋势线最大年龄(K线数)
input double InpSlopeThreshold = 0.0001;         // 最小斜率阈值

input group "=== ATR动态参数 ==="
input int    InpATRPeriod = 14;                  // ATR计算周期
input double InpTargetATRMultiplier = 1.5;       // 目标价ATR倍数
input double InpStopATRMultiplier = 0.5;         // 止损ATR倍数
input double InpTrailATRMultiplier = 0.8;        // 追踪止损ATR倍数

input group "=== 信号过滤参数 ==="
input double InpVolatilityFilter = 0.7;          // 波动率过滤阈值
input int    InpADXPeriod = 14;                  // ADX趋势强度周期
input double InpMinADX = 25.0;                   // 最小ADX值
input bool   InpUseTimeFilter = true;            // 启用时间过滤

// 趋势点结构
struct TrendPoint {
    double price;
    int bar;
    datetime time;
    bool isValid;
};

// 趋势线结构
struct TrendLine {
    TrendPoint points[10];  // 支持最多10个点
    int pointCount;
    double slope;
    double currentValue;
    bool isUpTrend;
    bool isValid;
    int age;  // 趋势线年龄
};

// 全局变量
TrendPoint g_highPoints[50];
TrendPoint g_lowPoints[50];
int g_highCount = 0;
int g_lowCount = 0;

TrendLine g_upTrendLine;    // 上升趋势线
TrendLine g_downTrendLine;  // 下降趋势线

// ATR风险管理器
class ATRRiskManager {
private:
    double m_atrValue;
    double m_avgATR;
    
public:
    void UpdateATR() {
        m_atrValue = iATR(Symbol(), Period(), InpATRPeriod, 0);
        
        // 计算ATR平均值
        double sum = 0;
        for(int i = 0; i < 20; i++) {
            sum += iATR(Symbol(), Period(), InpATRPeriod, i);
        }
        m_avgATR = sum / 20.0;
    }
    
    double GetATR() { return m_atrValue; }
    double GetAvgATR() { return m_avgATR; }
    
    // 波动率过滤
    bool IsHighVolatilityPeriod() {
        return m_atrValue > m_avgATR * InpVolatilityFilter;
    }
    
    // 计算动态目标价
    double CalculateTargetPrice(double entryPrice, bool isLong) {
        double targetDistance = m_atrValue * InpTargetATRMultiplier;
        return isLong ? entryPrice + targetDistance : entryPrice - targetDistance;
    }
    
    // 计算初始止损
    double CalculateStopLoss(double entryPrice, bool isLong) {
        double stopDistance = m_atrValue * InpStopATRMultiplier;
        return isLong ? entryPrice - stopDistance : entryPrice + stopDistance;
    }
    
    // 计算追踪止损
    double CalculateTrailingStop(double currentPrice, double currentStop, bool isLong) {
        double trailDistance = m_atrValue * InpTrailATRMultiplier;
        
        if(isLong) {
            double newStop = currentPrice - trailDistance;
            return MathMax(newStop, currentStop); // 只能向上调整
        } else {
            double newStop = currentPrice + trailDistance;
            return MathMin(newStop, currentStop); // 只能向下调整
        }
    }
    
    // 基于ATR的仓位计算
    double CalculatePositionSize(double entryPrice, double stopLoss) {
        double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        double riskAmount = accountBalance * InpRiskPercent / 100.0;
        
        double stopDistance = MathAbs(entryPrice - stopLoss);
        if(stopDistance <= 0) return 0;
        
        double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
        double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
        
        double lotSize = riskAmount / (stopDistance / tickSize * tickValue);
        
        // 标准化手数
        double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
        double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
        double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
        
        lotSize = MathMax(minLot, MathMin(maxLot, 
                  MathRound(lotSize / lotStep) * lotStep));
        
        return lotSize;
    }
};

// 信号过滤器
class SignalFilter {
public:
    // ADX趋势强度过滤
    bool IsTrendStrong() {
        double adx = iADX(Symbol(), Period(), InpADXPeriod, PRICE_CLOSE, MODE_MAIN, 0);
        return adx > InpMinADX;
    }
    
    // 交易时间过滤
    bool IsGoodTradingTime() {
        if(!InpUseTimeFilter) return true;
        
        datetime currentTime = TimeCurrent();
        int hour = TimeHour(currentTime);
        int dayOfWeek = TimeDayOfWeek(currentTime);
        
        // 避开周末和重要时段
        if(dayOfWeek == 0 || dayOfWeek == 6) return false;  // 周末
        if(hour < 2 || hour > 22) return false;             // 夜间
        
        return true;
    }
    
    // 成交量确认
    bool ConfirmVolume() {
        long currentVolume = iVolume(Symbol(), Period(), 0);
        long avgVolume = 0;
        
        for(int i = 1; i <= 20; i++) {
            avgVolume += iVolume(Symbol(), Period(), i);
        }
        avgVolume /= 20;
        
        return currentVolume > avgVolume * 1.2;
    }
    
    // 综合信号确认
    bool ConfirmSignal() {
        return IsTrendStrong() && IsGoodTradingTime() && ConfirmVolume();
    }
};

// 全局对象
ATRRiskManager g_riskManager;
SignalFilter g_signalFilter;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("顺势突破策略启动 - 版本 3.0 (修正逆势逻辑)");
    
    // 初始化趋势线
    g_upTrendLine.isValid = false;
    g_downTrendLine.isValid = false;
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("策略停止，原因: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // 检查新K线
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(Symbol(), Period(), 0);
    
    if(currentBarTime == lastBarTime) return;
    lastBarTime = currentBarTime;
    
    // 更新ATR和市场数据
    g_riskManager.UpdateATR();
    
    // 检查波动率过滤
    if(!g_riskManager.IsHighVolatilityPeriod()) {
        return; // 低波动期暂停交易
    }
    
    // 更新高低点
    UpdateTrendPoints();
    
    // 构建趋势线
    BuildTrendLines();
    
    // 检查顺势突破信号
    CheckTrendFollowingSignals();
    
    // 管理现有持仓
    ManagePositions();
}

//+------------------------------------------------------------------+
//| 更新趋势点                                                       |
//+------------------------------------------------------------------+
void UpdateTrendPoints() {
    // 检测新高点
    if(IsNewHigh(InpStrength)) {
        AddHighPoint(iHigh(Symbol(), Period(), InpStrength), InpStrength);
    }
    
    // 检测新低点
    if(IsNewLow(InpStrength)) {
        AddLowPoint(iLow(Symbol(), Period(), InpStrength), InpStrength);
    }
}

//+------------------------------------------------------------------+
//| 检查新高点                                                       |
//+------------------------------------------------------------------+
bool IsNewHigh(int strength) {
    double currentHigh = iHigh(Symbol(), Period(), strength);
    
    // 左右对称检查
    for(int i = 1; i <= strength; i++) {
        if(iHigh(Symbol(), Period(), strength - i) >= currentHigh ||
           iHigh(Symbol(), Period(), strength + i) >= currentHigh) {
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查新低点                                                       |
//+------------------------------------------------------------------+
bool IsNewLow(int strength) {
    double currentLow = iLow(Symbol(), Period(), strength);
    
    // 左右对称检查
    for(int i = 1; i <= strength; i++) {
        if(iLow(Symbol(), Period(), strength - i) <= currentLow ||
           iLow(Symbol(), Period(), strength + i) <= currentLow) {
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 添加高点                                                         |
//+------------------------------------------------------------------+
void AddHighPoint(double price, int barIndex) {
    // 移动数组
    for(int i = 49; i > 0; i--) {
        g_highPoints[i] = g_highPoints[i-1];
    }
    
    // 添加新点
    g_highPoints[0].price = price;
    g_highPoints[0].bar = barIndex;
    g_highPoints[0].time = iTime(Symbol(), Period(), barIndex);
    g_highPoints[0].isValid = true;
    
    if(g_highCount < 50) g_highCount++;
}

//+------------------------------------------------------------------+
//| 添加低点                                                         |
//+------------------------------------------------------------------+
void AddLowPoint(double price, int barIndex) {
    // 移动数组
    for(int i = 49; i > 0; i--) {
        g_lowPoints[i] = g_lowPoints[i-1];
    }
    
    // 添加新点
    g_lowPoints[0].price = price;
    g_lowPoints[0].bar = barIndex;
    g_lowPoints[0].time = iTime(Symbol(), Period(), barIndex);
    g_lowPoints[0].isValid = true;
    
    if(g_lowCount < 50) g_lowCount++;
}

//+------------------------------------------------------------------+
//| 构建趋势线 - 三点确认机制                                        |
//+------------------------------------------------------------------+
void BuildTrendLines() {
    // 构建上升趋势线（连接递增低点）
    BuildUpTrendLine();

    // 构建下降趋势线（连接递减高点）
    BuildDownTrendLine();
}

//+------------------------------------------------------------------+
//| 构建上升趋势线 - 修正版                                          |
//+------------------------------------------------------------------+
void BuildUpTrendLine() {
    g_upTrendLine.isValid = false;
    g_upTrendLine.pointCount = 0;

    if(g_lowCount < InpMinTrendPoints) return;

    // 寻找三个递增的低点 (P1 < P2 < P3)
    for(int i = 0; i < g_lowCount - 2; i++) {
        for(int j = i + 1; j < g_lowCount - 1; j++) {
            for(int k = j + 1; k < g_lowCount; k++) {
                // 检查价格递增关系
                if(g_lowPoints[k].price < g_lowPoints[j].price &&
                   g_lowPoints[j].price < g_lowPoints[i].price) {

                    // 检查时间间隔
                    if((g_lowPoints[k].bar - g_lowPoints[i].bar) > InpMaxTrendAge) continue;

                    // 计算斜率
                    double slope = (g_lowPoints[i].price - g_lowPoints[k].price) /
                                  (g_lowPoints[k].bar - g_lowPoints[i].bar);

                    // 验证斜率方向（上升趋势线斜率必须为正）
                    if(slope <= InpSlopeThreshold) continue;

                    // 验证趋势线有效性
                    if(ValidateUpTrendLine(g_lowPoints[k], g_lowPoints[j], g_lowPoints[i])) {
                        g_upTrendLine.points[0] = g_lowPoints[k];  // 最早点
                        g_upTrendLine.points[1] = g_lowPoints[j];  // 中间点
                        g_upTrendLine.points[2] = g_lowPoints[i];  // 最新点
                        g_upTrendLine.pointCount = 3;
                        g_upTrendLine.slope = slope;
                        g_upTrendLine.isUpTrend = true;
                        g_upTrendLine.isValid = true;
                        g_upTrendLine.age = g_lowPoints[k].bar - 0;

                        // 计算当前趋势线值
                        g_upTrendLine.currentValue = g_lowPoints[k].price +
                            slope * (g_lowPoints[k].bar - 0);

                        return; // 找到有效趋势线就返回
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 构建下降趋势线 - 修正版                                          |
//+------------------------------------------------------------------+
void BuildDownTrendLine() {
    g_downTrendLine.isValid = false;
    g_downTrendLine.pointCount = 0;

    if(g_highCount < InpMinTrendPoints) return;

    // 寻找三个递减的高点 (P1 > P2 > P3)
    for(int i = 0; i < g_highCount - 2; i++) {
        for(int j = i + 1; j < g_highCount - 1; j++) {
            for(int k = j + 1; k < g_highCount; k++) {
                // 检查价格递减关系
                if(g_highPoints[k].price > g_highPoints[j].price &&
                   g_highPoints[j].price > g_highPoints[i].price) {

                    // 检查时间间隔
                    if((g_highPoints[k].bar - g_highPoints[i].bar) > InpMaxTrendAge) continue;

                    // 计算斜率
                    double slope = (g_highPoints[i].price - g_highPoints[k].price) /
                                  (g_highPoints[k].bar - g_highPoints[i].bar);

                    // 验证斜率方向（下降趋势线斜率必须为负）
                    if(slope >= -InpSlopeThreshold) continue;

                    // 验证趋势线有效性
                    if(ValidateDownTrendLine(g_highPoints[k], g_highPoints[j], g_highPoints[i])) {
                        g_downTrendLine.points[0] = g_highPoints[k];  // 最早点
                        g_downTrendLine.points[1] = g_highPoints[j];  // 中间点
                        g_downTrendLine.points[2] = g_highPoints[i];  // 最新点
                        g_downTrendLine.pointCount = 3;
                        g_downTrendLine.slope = slope;
                        g_downTrendLine.isUpTrend = false;
                        g_downTrendLine.isValid = true;
                        g_downTrendLine.age = g_highPoints[k].bar - 0;

                        // 计算当前趋势线值
                        g_downTrendLine.currentValue = g_highPoints[k].price +
                            slope * (g_highPoints[k].bar - 0);

                        return; // 找到有效趋势线就返回
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 验证上升趋势线有效性                                             |
//+------------------------------------------------------------------+
bool ValidateUpTrendLine(TrendPoint p1, TrendPoint p2, TrendPoint p3) {
    double slope = (p3.price - p1.price) / (p1.bar - p3.bar);
    int violations = 0;
    double atr = g_riskManager.GetATR();
    double tolerance = atr * 0.3; // 允许小幅穿透

    // 检查趋势线之间是否有过多违反
    for(int i = p1.bar - 1; i > p3.bar; i--) {
        double trendValue = p1.price + slope * (p1.bar - i);

        // 检查是否有显著跌破趋势线
        if(iLow(Symbol(), Period(), i) < trendValue - tolerance) {
            violations++;
        }
    }

    // 允许最多20%的违反
    int maxViolations = (p1.bar - p3.bar) / 5;
    return violations <= maxViolations;
}

//+------------------------------------------------------------------+
//| 验证下降趋势线有效性                                             |
//+------------------------------------------------------------------+
bool ValidateDownTrendLine(TrendPoint p1, TrendPoint p2, TrendPoint p3) {
    double slope = (p3.price - p1.price) / (p1.bar - p3.bar);
    int violations = 0;
    double atr = g_riskManager.GetATR();
    double tolerance = atr * 0.3; // 允许小幅穿透

    // 检查趋势线之间是否有过多违反
    for(int i = p1.bar - 1; i > p3.bar; i--) {
        double trendValue = p1.price + slope * (p1.bar - i);

        // 检查是否有显著突破趋势线
        if(iHigh(Symbol(), Period(), i) > trendValue + tolerance) {
            violations++;
        }
    }

    // 允许最多20%的违反
    int maxViolations = (p1.bar - p3.bar) / 5;
    return violations <= maxViolations;
}

//+------------------------------------------------------------------+
//| 检查顺势突破信号 - 核心逻辑修正                                  |
//+------------------------------------------------------------------+
void CheckTrendFollowingSignals() {
    double currentPrice = iClose(Symbol(), Period(), 0);
    double atr = g_riskManager.GetATR();

    // 检查上升趋势线向上突破（做多信号）
    if(g_upTrendLine.isValid && g_upTrendLine.age < InpMaxTrendAge) {
        // 顺势突破：价格向上突破上升趋势线
        if(currentPrice > g_upTrendLine.currentValue + atr * 0.1) {
            if(g_signalFilter.ConfirmSignal() && !HasPosition(true)) {
                OpenLongPosition();
            }
        }
    }

    // 检查下降趋势线向下突破（做空信号）
    if(g_downTrendLine.isValid && g_downTrendLine.age < InpMaxTrendAge) {
        // 顺势突破：价格向下突破下降趋势线
        if(currentPrice < g_downTrendLine.currentValue - atr * 0.1) {
            if(g_signalFilter.ConfirmSignal() && !HasPosition(false)) {
                OpenShortPosition();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 开多头仓位                                                       |
//+------------------------------------------------------------------+
void OpenLongPosition() {
    double entryPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double stopLoss = g_riskManager.CalculateStopLoss(entryPrice, true);
    double takeProfit = g_riskManager.CalculateTargetPrice(entryPrice, true);
    double lotSize = g_riskManager.CalculatePositionSize(entryPrice, stopLoss);

    if(lotSize <= 0) return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.magic = InpMagicNumber;
    request.comment = "TrendFollow_BUY";

    if(OrderSend(request, result)) {
        Print(StringFormat("多头开仓成功: %.2f手 @ %.5f, SL: %.5f, TP: %.5f",
              lotSize, entryPrice, stopLoss, takeProfit));
    } else {
        Print("多头开仓失败: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| 开空头仓位                                                       |
//+------------------------------------------------------------------+
void OpenShortPosition() {
    double entryPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double stopLoss = g_riskManager.CalculateStopLoss(entryPrice, false);
    double takeProfit = g_riskManager.CalculateTargetPrice(entryPrice, false);
    double lotSize = g_riskManager.CalculatePositionSize(entryPrice, stopLoss);

    if(lotSize <= 0) return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.magic = InpMagicNumber;
    request.comment = "TrendFollow_SELL";

    if(OrderSend(request, result)) {
        Print(StringFormat("空头开仓成功: %.2f手 @ %.5f, SL: %.5f, TP: %.5f",
              lotSize, entryPrice, stopLoss, takeProfit));
    } else {
        Print("空头开仓失败: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| 检查是否有持仓                                                   |
//+------------------------------------------------------------------+
bool HasPosition(bool isLong) {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByIndex(i)) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == InpMagicNumber) {
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                if((isLong && posType == POSITION_TYPE_BUY) ||
                   (!isLong && posType == POSITION_TYPE_SELL)) {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 管理现有持仓 - 移动止损                                          |
//+------------------------------------------------------------------+
void ManagePositions() {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByIndex(i)) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == InpMagicNumber) {

                ulong ticket = PositionGetInteger(POSITION_TICKET);
                double entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
                double currentSL = PositionGetDouble(POSITION_SL);
                double currentTP = PositionGetDouble(POSITION_TP);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                bool isLong = (posType == POSITION_TYPE_BUY);

                // 计算新的追踪止损
                double newSL = g_riskManager.CalculateTrailingStop(currentPrice, currentSL, isLong);

                // 检查是否需要更新止损
                if(MathAbs(newSL - currentSL) > Point() * 5) {
                    ModifyPosition(ticket, newSL, currentTP);
                }

                // 检查盈亏平衡条件
                CheckBreakEven(ticket, entryPrice, currentPrice, currentSL, currentTP, isLong);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查盈亏平衡                                                     |
//+------------------------------------------------------------------+
void CheckBreakEven(ulong ticket, double entryPrice, double currentPrice,
                   double currentSL, double currentTP, bool isLong) {
    double atr = g_riskManager.GetATR();
    double profit = isLong ? (currentPrice - entryPrice) : (entryPrice - currentPrice);

    // 当利润达到1倍ATR时，移动止损到盈亏平衡点
    if(profit >= atr * 1.0) {
        double breakEvenSL = entryPrice + (isLong ? Point() * 5 : -Point() * 5);

        // 确保新止损比当前止损更有利
        bool shouldUpdate = false;
        if(isLong && breakEvenSL > currentSL) shouldUpdate = true;
        if(!isLong && breakEvenSL < currentSL) shouldUpdate = true;

        if(shouldUpdate) {
            ModifyPosition(ticket, breakEvenSL, currentTP);
            Print(StringFormat("持仓 %d 移动到盈亏平衡点: %.5f", ticket, breakEvenSL));
        }
    }
}

//+------------------------------------------------------------------+
//| 修改持仓                                                         |
//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double stopLoss, double takeProfit) {
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = stopLoss;
    request.tp = takeProfit;

    if(OrderSend(request, result)) {
        Print(StringFormat("持仓修改成功: %d, SL: %.5f, TP: %.5f",
              ticket, stopLoss, takeProfit));
        return true;
    } else {
        Print(StringFormat("持仓修改失败: %d, 错误: %d - %s",
              ticket, result.retcode, result.comment));
        return false;
    }
}

//+------------------------------------------------------------------+
//| 熔断机制 - 单日最大亏损保护                                      |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit() {
    static datetime lastCheckDate = 0;
    static double dailyStartBalance = 0;

    datetime currentDate = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    // 新的一天重置
    if(currentDate != lastCheckDate) {
        lastCheckDate = currentDate;
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        return true;
    }

    // 检查当日亏损
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double dailyPL = (currentBalance - dailyStartBalance) / dailyStartBalance;

    // 单日亏损超过5%时暂停交易
    if(dailyPL < -0.05) {
        Print("触发熔断机制：单日亏损超过5%，暂停交易");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 获取策略统计信息                                                 |
//+------------------------------------------------------------------+
void PrintStrategyStats() {
    static datetime lastPrintTime = 0;
    datetime currentTime = TimeCurrent();

    // 每小时打印一次统计
    if(currentTime - lastPrintTime < 3600) return;
    lastPrintTime = currentTime;

    double atr = g_riskManager.GetATR();
    double avgATR = g_riskManager.GetAvgATR();
    double volatilityRatio = atr / avgATR;

    Print("=== 策略状态 ===");
    Print("当前ATR: ", DoubleToString(atr, 5));
    Print("平均ATR: ", DoubleToString(avgATR, 5));
    Print("波动率比率: ", DoubleToString(volatilityRatio, 2));
    Print("上升趋势线有效: ", g_upTrendLine.isValid ? "是" : "否");
    Print("下降趋势线有效: ", g_downTrendLine.isValid ? "是" : "否");
    Print("当前持仓数: ", PositionsTotal());
    Print("账户余额: ", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
    Print("===============");
}
