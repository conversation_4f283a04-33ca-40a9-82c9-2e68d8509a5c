//+------------------------------------------------------------------+
//|                                                 TrendAnalysis.mqh |
//|                                      Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.metaquotes.net |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net"
#property version   "1.00"

// 趋势方向枚举
enum TREND_DIRECTION {
    TREND_NEUTRAL,  // 中性
    TREND_BULLISH,  // 看涨
    TREND_BEARISH   // 看跌
};

// 趋势强度枚举
enum TREND_STRENGTH {
    STRENGTH_WEAK,    // 弱
    STRENGTH_MODERATE, // 中等
    STRENGTH_STRONG    // 强
};

// 趋势质量枚举（Deepseek优化增强）
enum TREND_QUALITY {
    NO_TREND_QUALITY,           // 无趋势质量
    EARLY_TREND_QUALITY,        // 初期趋势质量
    STRONG_TREND_QUALITY,       // 强势趋势质量
    VERY_STRONG_TREND_QUALITY   // 极强趋势质量
};

// 趋势分析结构（增强版）
struct TrendAnalysis {
    TREND_DIRECTION direction;  // 趋势方向
    TREND_STRENGTH strength;    // 趋势强度
    double score;               // 趋势评分(-100到100)
    int consecutiveBars;        // 趋势连续Bar数
    double momentumScore;       // 动量评分(0-100)
    bool volumeConfirmation;    // 成交量确认
    double retracementDepth;    // 回撤深度(0-1)

    // === 新增：Deepseek优化字段 ===
    TREND_QUALITY quality;      // 趋势质量等级
    double fibonacciRetrace;    // 斐波那契回撤比例
    double macdValue;           // MACD主线值
    double rsiValue;            // RSI当前值
    bool multiTimeframeAlign;   // 多时间框架一致性
};

//+------------------------------------------------------------------+
//| 分析趋势方向和强度                                                 |
//+------------------------------------------------------------------+
TrendAnalysis AnalyzeTrend(int period = 50) {
    TrendAnalysis result;
    
    // 获取价格数据
    double close[];
    double high[];
    double low[];
    
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    
    int copied = CopyClose(_Symbol, PERIOD_CURRENT, 0, period, close);
    copied = MathMin(copied, CopyHigh(_Symbol, PERIOD_CURRENT, 0, period, high));
    copied = MathMin(copied, CopyLow(_Symbol, PERIOD_CURRENT, 0, period, low));
    
    if(copied < period) {
        Print("数据复制失败! 错误代码 = ", GetLastError());
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    // 计算移动平均线
    double ma20[], ma50[];
    ArraySetAsSeries(ma20, true);
    ArraySetAsSeries(ma50, true);
    
    int ma20Handle = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE);
    int ma50Handle = iMA(_Symbol, PERIOD_CURRENT, 50, 0, MODE_SMA, PRICE_CLOSE);
    
    if(ma20Handle == INVALID_HANDLE || ma50Handle == INVALID_HANDLE) {
        Print("MA指标创建失败! 错误代码 = ", GetLastError());
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    // 计算需要的MA数据量 (最多需要20个用于趋势持续性分析)
    int maDataSize = MathMin(25, period + 5);

    if(CopyBuffer(ma20Handle, 0, 0, maDataSize, ma20) <= 0 ||
       CopyBuffer(ma50Handle, 0, 0, maDataSize, ma50) <= 0) {
        Print("MA数据复制失败! 错误代码 = ", GetLastError());
        IndicatorRelease(ma20Handle);
        IndicatorRelease(ma50Handle);
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    IndicatorRelease(ma20Handle);
    IndicatorRelease(ma50Handle);
    
    // 计算RSI
    double rsi[];
    ArraySetAsSeries(rsi, true);
    
    int rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
    
    if(rsiHandle == INVALID_HANDLE) {
        Print("RSI指标创建失败! 错误代码 = ", GetLastError());
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    if(CopyBuffer(rsiHandle, 0, 0, 3, rsi) <= 0) {
        Print("RSI数据复制失败! 错误代码 = ", GetLastError());
        IndicatorRelease(rsiHandle);
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    IndicatorRelease(rsiHandle);
    
    // 计算MACD
    double macdMain[], macdSignal[];
    ArraySetAsSeries(macdMain, true);
    ArraySetAsSeries(macdSignal, true);
    
    int macdHandle = iMACD(_Symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
    
    if(macdHandle == INVALID_HANDLE) {
        Print("MACD指标创建失败! 错误代码 = ", GetLastError());
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    if(CopyBuffer(macdHandle, 0, 0, 3, macdMain) <= 0 ||
       CopyBuffer(macdHandle, 1, 0, 3, macdSignal) <= 0) {
        Print("MACD数据复制失败! 错误代码 = ", GetLastError());
        IndicatorRelease(macdHandle);
        result.direction = TREND_NEUTRAL;
        result.strength = STRENGTH_WEAK;
        result.score = 0.0;
        return result;
    }
    
    IndicatorRelease(macdHandle);
    
    // === 优化的趋势评分计算 ===
    double trendScore = CalculateAdvancedTrendScore(close, high, low, ma20, ma50, rsi, macdMain, macdSignal, period);

    // 限制评分范围
    trendScore = MathMax(-100.0, MathMin(100.0, trendScore));

    // === 优化的趋势方向和强度判断 ===
    result = DetermineTrendDirectionAndStrength(trendScore);

    result.score = trendScore;

    // === 新增：趋势连续性检测 ===
    result.consecutiveBars = CalculateConsecutiveBars();

    // === 新增：动量评分计算 ===
    result.momentumScore = CalculateSimpleMomentumScore(rsi, macdMain, macdSignal);

    // === 新增：成交量确认 ===
    result.volumeConfirmation = CheckVolumeConfirmation(result.direction);

    // === 新增：回撤深度计算 ===
    result.retracementDepth = CalculateRetracementDepth(high, low, close, result.direction);

    // === 新增：Deepseek优化增强 ===
    result.quality = EvaluateTrendQuality(result, rsi, macdMain, macdSignal);
    result.fibonacciRetrace = CalculateFibonacciRetracement();
    result.macdValue = macdMain[0];
    result.rsiValue = rsi[0];
    result.multiTimeframeAlign = CheckMultiTimeframeAlignment();

    return result;
}

//+------------------------------------------------------------------+
//| 改进的趋势连续性检测算法                                           |
//+------------------------------------------------------------------+
int CalculateConsecutiveBars() {
    int consecutive = 0;
    const int maxBars = 8;  // 最大检测8根K线

    for(int i = 0; i < maxBars; i++) {
        // 检查上涨趋势连续性
        if(iClose(_Symbol, PERIOD_CURRENT, i) > iClose(_Symbol, PERIOD_CURRENT, i+1)) {
            consecutive++;
        }
        // 检查下跌趋势连续性
        else if(iClose(_Symbol, PERIOD_CURRENT, i) < iClose(_Symbol, PERIOD_CURRENT, i+1)) {
            consecutive++;
        } else {
            break; // 趋势中断
        }
    }

    // 添加成交量确认（至少最后2根K线需要成交量支持）
    if(consecutive >= 2) {
        long vol1 = iVolume(_Symbol, PERIOD_CURRENT, 0);
        long vol2 = iVolume(_Symbol, PERIOD_CURRENT, 1);
        double volAvg = 0;

        // 计算5根K线平均成交量
        for(int j = 0; j < 5; j++) {
            volAvg += (double)iVolume(_Symbol, PERIOD_CURRENT, j);
        }
        volAvg /= 5;

        // 如果最近2根K线成交量都低于平均值，降低连续性评分
        if((double)vol1 < volAvg && (double)vol2 < volAvg) {
            consecutive = MathMax(1, consecutive - 1);
        }
    }

    return consecutive;
}

//+------------------------------------------------------------------+
//| 检查成交量确认                                                   |
//+------------------------------------------------------------------+
bool CheckVolumeConfirmation(TREND_DIRECTION direction) {
    long volumes[];
    ArraySetAsSeries(volumes, true);

    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, 3, volumes) < 3) {
        return false;
    }

    // 当前成交量与前一根比较
    double currentVol = (double)volumes[0];
    double prevVol = (double)volumes[1];
    double avgVol = ((double)volumes[0] + (double)volumes[1] + (double)volumes[2]) / 3.0;

    // 成交量确认条件：当前成交量高于平均值
    if(direction == TREND_BULLISH || direction == TREND_BEARISH) {
        return currentVol > avgVol * 1.1; // 成交量至少高于平均值10%
    }

    return false;
}

//+------------------------------------------------------------------+
//| 计算回撤深度                                                     |
//+------------------------------------------------------------------+
double CalculateRetracementDepth(const double &high[], const double &low[],
                                const double &close[], TREND_DIRECTION direction) {
    int lookback = MathMin(10, ArraySize(high));
    if(lookback < 3) return 0.0;

    double maxMove = 0.0;
    double retracement = 0.0;

    if(direction == TREND_BULLISH) {
        // 找到最高点和最低点
        double highestHigh = high[0];
        double lowestLow = low[0];

        for(int i = 1; i < lookback && i < ArraySize(high) && i < ArraySize(low); i++) {
            if(high[i] > highestHigh) highestHigh = high[i];
            if(low[i] < lowestLow) lowestLow = low[i];
        }

        maxMove = highestHigh - lowestLow;
        if(maxMove > 0) {
            retracement = (highestHigh - close[0]) / maxMove;
        }
    }
    else if(direction == TREND_BEARISH) {
        // 找到最高点和最低点
        double highestHigh = high[0];
        double lowestLow = low[0];

        for(int i = 1; i < lookback && i < ArraySize(high) && i < ArraySize(low); i++) {
            if(high[i] > highestHigh) highestHigh = high[i];
            if(low[i] < lowestLow) lowestLow = low[i];
        }

        maxMove = highestHigh - lowestLow;
        if(maxMove > 0) {
            retracement = (close[0] - lowestLow) / maxMove;
        }
    }

    return MathMax(0.0, MathMin(1.0, retracement));
}

//+------------------------------------------------------------------+
//| 趋势质量评估函数（Deepseek优化增强）                               |
//+------------------------------------------------------------------+
TREND_QUALITY EvaluateTrendQuality(const TrendAnalysis &trend, const double &rsi[],
                                  const double &macdMain[], const double &macdSignal[]) {
    int qualityScore = 0;

    // 1. 趋势强度评分
    switch(trend.strength) {
        case STRENGTH_STRONG:   qualityScore += 3; break;
        case STRENGTH_MODERATE: qualityScore += 2; break;
        case STRENGTH_WEAK:     qualityScore += 1; break;
    }

    // 2. 连续性评分
    if(trend.consecutiveBars >= 5) qualityScore += 3;
    else if(trend.consecutiveBars >= 3) qualityScore += 2;
    else if(trend.consecutiveBars >= 2) qualityScore += 1;

    // 3. 动量确认评分
    if(trend.momentumScore > 70) qualityScore += 2;
    else if(trend.momentumScore > 60) qualityScore += 1;

    // 4. 成交量确认评分
    if(trend.volumeConfirmation) qualityScore += 2;

    // 5. MACD确认评分
    if(macdMain[0] > macdSignal[0] && macdMain[0] > 0) qualityScore += 1;
    else if(macdMain[0] < macdSignal[0] && macdMain[0] < 0) qualityScore += 1;

    // 6. RSI健康度评分
    if(rsi[0] > 45 && rsi[0] < 75) qualityScore += 1; // 健康的RSI区间

    // 质量等级判定
    if(qualityScore >= 10) return VERY_STRONG_TREND_QUALITY;
    if(qualityScore >= 7) return STRONG_TREND_QUALITY;
    if(qualityScore >= 4) return EARLY_TREND_QUALITY;

    return NO_TREND_QUALITY;
}

//+------------------------------------------------------------------+
//| 斐波那契回撤计算（增强版）                                         |
//+------------------------------------------------------------------+
double CalculateFibonacciRetracement() {
    // 获取最近20根K线的最高点和最低点
    int highest_idx = iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, 20, 0);
    int lowest_idx = iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, 20, 0);

    if(highest_idx < 0 || lowest_idx < 0) return 1.0;

    double swing_high = iHigh(_Symbol, PERIOD_CURRENT, highest_idx);
    double swing_low = iLow(_Symbol, PERIOD_CURRENT, lowest_idx);
    double current_price = iClose(_Symbol, PERIOD_CURRENT, 0);

    if(swing_high == swing_low) return 1.0; // 避免除零错误

    // 计算回撤比例
    return (current_price - swing_low) / (swing_high - swing_low);
}

//+------------------------------------------------------------------+
//| 多时间框架一致性检查                                               |
//+------------------------------------------------------------------+
bool CheckMultiTimeframeAlignment() {
    // 检查H4和D1时间框架的趋势一致性
    double h4_close = iClose(_Symbol, PERIOD_H4, 0);
    double h4_ma20 = iMA(_Symbol, PERIOD_H4, 20, 0, MODE_SMA, PRICE_CLOSE);

    double d1_close = iClose(_Symbol, PERIOD_D1, 0);
    double d1_ma20 = iMA(_Symbol, PERIOD_D1, 20, 0, MODE_SMA, PRICE_CLOSE);

    // 简单的趋势一致性检查
    bool h4_bullish = h4_close > h4_ma20;
    bool d1_bullish = d1_close > d1_ma20;

    return (h4_bullish == d1_bullish); // 两个时间框架趋势方向一致
}

//+------------------------------------------------------------------+
//| 简化版动量评分计算（用于TrendAnalysis结构）                        |
//+------------------------------------------------------------------+
double CalculateSimpleMomentumScore(const double &rsi[], const double &macdMain[], const double &macdSignal[]) {
    double score = 50.0; // 基础分数50

    // RSI评分 (0-100范围)
    if(rsi[0] > 70) score += 25.0;
    else if(rsi[0] > 60) score += 15.0;
    else if(rsi[0] > 50) score += 5.0;
    else if(rsi[0] < 30) score -= 25.0;
    else if(rsi[0] < 40) score -= 15.0;
    else if(rsi[0] < 50) score -= 5.0;

    // MACD评分
    if(macdMain[0] > macdSignal[0] && macdMain[0] > 0) score += 15.0;
    else if(macdMain[0] < macdSignal[0] && macdMain[0] < 0) score -= 15.0;
    else if(macdMain[0] > macdSignal[0]) score += 7.5;
    else if(macdMain[0] < macdSignal[0]) score -= 7.5;

    // 限制在0-100范围内
    return MathMax(0.0, MathMin(100.0, score));
}

//+------------------------------------------------------------------+
//| 获取趋势方向描述                                                   |
//+------------------------------------------------------------------+
string GetTrendDirectionDescription(TREND_DIRECTION direction) {
    switch(direction) {
        case TREND_BULLISH: return "看涨";
        case TREND_BEARISH: return "看跌";
        default:            return "中性";
    }
}

//+------------------------------------------------------------------+
//| 获取趋势强度描述                                                   |
//+------------------------------------------------------------------+
string GetTrendStrengthDescription(TREND_STRENGTH strength) {
    switch(strength) {
        case STRENGTH_STRONG:   return "强";
        case STRENGTH_MODERATE: return "中等";
        default:                return "弱";
    }
}

//+------------------------------------------------------------------+
//| 获取趋势颜色                                                       |
//+------------------------------------------------------------------+
color GetTrendColor(TREND_DIRECTION direction, TREND_STRENGTH strength) {
    if(direction == TREND_BULLISH) {
        switch(strength) {
            case STRENGTH_STRONG:   return clrLime;
            case STRENGTH_MODERATE: return clrGreen;
            default:                return clrDarkGreen;
        }
    }
    else if(direction == TREND_BEARISH) {
        switch(strength) {
            case STRENGTH_STRONG:   return clrRed;
            case STRENGTH_MODERATE: return clrCrimson;
            default:                return clrDarkRed;
        }
    }
    else {
        return clrGray;
    }
}

//+------------------------------------------------------------------+
//| 高级趋势评分计算函数                                               |
//+------------------------------------------------------------------+
double CalculateAdvancedTrendScore(const double &close[], const double &high[], const double &low[],
                                  const double &ma20[], const double &ma50[], const double &rsi[],
                                  const double &macdMain[], const double &macdSignal[], int period) {
    double totalScore = 0.0;

    // === 1. 移动平均线分析 (权重: 30%) ===
    double maScore = CalculateMAScore(close, ma20, ma50);
    totalScore += maScore * 0.30;

    // === 2. 动量指标分析 (权重: 25%) ===
    double momentumScore = CalculateMomentumScore(rsi, macdMain, macdSignal);
    totalScore += momentumScore * 0.25;

    // === 3. 价格结构分析 (权重: 20%) ===
    double structureScore = CalculatePriceStructureScore(high, low, close, period);
    totalScore += structureScore * 0.20;

    // === 4. 波动率分析 (权重: 15%) ===
    double volatilityScore = CalculateVolatilityScore(high, low, close, period);
    totalScore += volatilityScore * 0.15;

    // === 5. 趋势持续性分析 (权重: 10%) ===
    double persistenceScore = CalculateTrendPersistence(close, ma20, period);
    totalScore += persistenceScore * 0.10;

    return totalScore;
}

//+------------------------------------------------------------------+
//| 移动平均线评分计算                                                 |
//+------------------------------------------------------------------+
double CalculateMAScore(const double &close[], const double &ma20[], const double &ma50[]) {
    double score = 0.0;

    // 价格与MA的关系 (40分)
    if(close[0] > ma20[0]) score += 20.0;
    else if(close[0] < ma20[0]) score -= 20.0;

    if(close[0] > ma50[0]) score += 20.0;
    else if(close[0] < ma50[0]) score -= 20.0;

    // MA排列 (30分)
    if(ma20[0] > ma50[0]) score += 15.0;
    else if(ma20[0] < ma50[0]) score -= 15.0;

    // MA斜率 (30分)
    double ma20Slope = (ma20[0] - ma20[2]) / 2.0;
    double ma50Slope = (ma50[0] - ma50[2]) / 2.0;

    if(ma20Slope > 0 && ma50Slope > 0) score += 15.0;
    else if(ma20Slope < 0 && ma50Slope < 0) score -= 15.0;
    else if(MathAbs(ma20Slope) > MathAbs(ma50Slope)) {
        score += (ma20Slope > 0 ? 7.5 : -7.5);
    }

    return score;
}

//+------------------------------------------------------------------+
//| 动量指标评分计算                                                   |
//+------------------------------------------------------------------+
double CalculateMomentumScore(const double &rsi[], const double &macdMain[], const double &macdSignal[]) {
    double score = 0.0;

    // RSI动态阈值评分 (50分)
    if(rsi[0] > 70) score += 25.0;
    else if(rsi[0] > 60) score += 15.0;
    else if(rsi[0] > 50) score += 5.0;
    else if(rsi[0] < 30) score -= 25.0;
    else if(rsi[0] < 40) score -= 15.0;
    else if(rsi[0] < 50) score -= 5.0;

    // RSI趋势 (额外10分)
    double rsiTrend = rsi[0] - rsi[2];
    if(MathAbs(rsiTrend) > 5) {
        score += (rsiTrend > 0 ? 10.0 : -10.0);
    }

    // MACD评分 (40分)
    if(macdMain[0] > macdSignal[0] && macdMain[0] > 0) score += 20.0;
    else if(macdMain[0] < macdSignal[0] && macdMain[0] < 0) score -= 20.0;
    else if(macdMain[0] > macdSignal[0]) score += 10.0;
    else if(macdMain[0] < macdSignal[0]) score -= 10.0;

    // MACD趋势 (额外20分)
    double macdTrend = macdMain[0] - macdMain[1];
    if(MathAbs(macdTrend) > 0) {
        score += (macdTrend > 0 ? 10.0 : -10.0);
    }

    return score;
}

//+------------------------------------------------------------------+
//| 价格结构评分计算                                                   |
//+------------------------------------------------------------------+
double CalculatePriceStructureScore(const double &high[], const double &low[], const double &close[], int period) {
    double score = 0.0;

    // 安全的数组大小检查
    int highSize = ArraySize(high);
    int lowSize = ArraySize(low);
    int closeSize = ArraySize(close);
    int maxLookback = MathMin(MathMin(highSize, lowSize), closeSize);

    // 限制lookback，确保不超过实际可用数据
    int lookback = MathMin(MathMin(10, period), maxLookback);

    // 如果数据不足，返回中性评分
    if(lookback < 3) {
        return 0.0;
    }

    // 高低点序列分析 (60分)
    int higherHighs = 0, lowerLows = 0, lowerHighs = 0, higherLows = 0;

    for(int i = 1; i < lookback; i++) {
        // 安全检查数组边界
        if(i < highSize && (i-1) < highSize && i < lowSize && (i-1) < lowSize) {
            if(high[i-1] > high[i]) higherHighs++;
            if(low[i-1] < low[i]) lowerLows++;
            if(high[i-1] < high[i]) lowerHighs++;
            if(low[i-1] > low[i]) higherLows++;
        }
    }

    // 上升趋势结构
    if(higherHighs >= lookback * 0.6 && higherLows >= lookback * 0.6) {
        score += 30.0;
    }
    // 下降趋势结构
    else if(lowerHighs >= lookback * 0.6 && lowerLows >= lookback * 0.6) {
        score -= 30.0;
    }
    // 混合结构
    else {
        double upBias = (double)(higherHighs + higherLows) / (lookback * 2);
        double downBias = (double)(lowerHighs + lowerLows) / (lookback * 2);
        score += (upBias - downBias) * 30.0;
    }

    // K线实体大小分析 (40分)
    double avgBodySize = 0.0;
    double avgRange = 0.0;
    int bodyAnalysisCount = MathMin(MathMin(5, period), closeSize - 1); // 确保close[i+1]不越界

    if(bodyAnalysisCount > 0) {
        for(int i = 0; i < bodyAnalysisCount; i++) {
            // 安全检查数组边界
            if(i < closeSize && (i+1) < closeSize && i < highSize && i < lowSize) {
                double bodySize = MathAbs(close[i] - close[i+1]);
                double range = high[i] - low[i];
                avgBodySize += bodySize;
                avgRange += range;
            }
        }

        avgBodySize /= bodyAnalysisCount;
        avgRange /= bodyAnalysisCount;
    }

    // 实体占比越大，趋势越强
    if(avgRange > 0) {
        double bodyRatio = avgBodySize / avgRange;
        if(bodyRatio > 0.7) score += 20.0;
        else if(bodyRatio > 0.5) score += 10.0;
        else if(bodyRatio < 0.3) score -= 10.0;
    }

    return score;
}

//+------------------------------------------------------------------+
//| 波动率评分计算                                                     |
//+------------------------------------------------------------------+
double CalculateVolatilityScore(const double &high[], const double &low[], const double &close[], int period) {
    double score = 0.0;

    // 安全的数组大小检查
    int highSize = ArraySize(high);
    int lowSize = ArraySize(low);
    int maxLookback = MathMin(highSize, lowSize);

    int lookback = MathMin(MathMin(20, period), maxLookback);

    // 如果数据不足，返回中性评分
    if(lookback < 6) {
        return 0.0;
    }

    // 计算当前波动率
    double currentVol = 0.0;
    int currentVolCount = MathMin(MathMin(5, period), maxLookback);

    if(currentVolCount > 0) {
        for(int i = 0; i < currentVolCount; i++) {
            if(i < highSize && i < lowSize) {
                currentVol += high[i] - low[i];
            }
        }
        currentVol /= currentVolCount;
    }

    // 计算历史平均波动率
    double avgVol = 0.0;
    int avgVolCount = 0;

    for(int i = 5; i < lookback; i++) {
        if(i < highSize && i < lowSize) {
            avgVol += high[i] - low[i];
            avgVolCount++;
        }
    }

    if(avgVolCount > 0) {
        avgVol /= avgVolCount;
    }

    if(avgVol > 0) {
        double volRatio = currentVol / avgVol;

        // 波动率适中时趋势更可靠
        if(volRatio > 0.8 && volRatio < 1.5) {
            score += 50.0;
        }
        else if(volRatio > 1.5) {
            score += 25.0; // 高波动率，趋势可能不稳定
        }
        else {
            score += 10.0; // 低波动率，趋势可能较弱
        }
    }

    return score;
}

//+------------------------------------------------------------------+
//| 趋势持续性评分计算                                                 |
//+------------------------------------------------------------------+
double CalculateTrendPersistence(const double &close[], const double &ma20[], int period) {
    double score = 0.0;

    // 安全的数组大小检查
    int closeSize = ArraySize(close);
    int ma20Size = ArraySize(ma20);
    int maxLookback = MathMin(closeSize, ma20Size);

    // 进一步限制lookback，确保不超过实际可用数据
    int lookback = MathMin(MathMin(20, period), maxLookback);

    // 如果数据不足，返回中性评分
    if(lookback < 3) {
        return 0.0;
    }

    // 价格与MA关系的一致性
    int bullishBars = 0, bearishBars = 0;

    for(int i = 0; i < lookback; i++) {
        // 双重安全检查
        if(i < closeSize && i < ma20Size) {
            if(close[i] > ma20[i]) bullishBars++;
            else if(close[i] < ma20[i]) bearishBars++;
        }
    }

    // 避免除零错误
    if(lookback == 0) return 0.0;

    double consistency = (double)MathMax(bullishBars, bearishBars) / lookback;

    if(consistency > 0.8) {
        score += (bullishBars > bearishBars ? 50.0 : -50.0);
    }
    else if(consistency > 0.6) {
        score += (bullishBars > bearishBars ? 30.0 : -30.0);
    }
    else {
        score += (bullishBars > bearishBars ? 10.0 : -10.0);
    }

    return score;
}

//+------------------------------------------------------------------+
//| 优化的趋势方向和强度判断                                           |
//+------------------------------------------------------------------+
TrendAnalysis DetermineTrendDirectionAndStrength(double trendScore) {
    TrendAnalysis result;

    // 动态阈值，基于评分的绝对值
    double absScore = MathAbs(trendScore);

    // 趋势方向判断 - 降低中性区间
    if(trendScore > 15.0) {
        result.direction = TREND_BULLISH;
    }
    else if(trendScore < -15.0) {
        result.direction = TREND_BEARISH;
    }
    else {
        result.direction = TREND_NEUTRAL;
    }

    // 趋势强度判断 - 使用更精细的分级
    if(absScore >= 75.0) {
        result.strength = STRENGTH_STRONG;
    }
    else if(absScore >= 45.0) {
        result.strength = STRENGTH_MODERATE;
    }
    else {
        result.strength = STRENGTH_WEAK;
    }

    // 特殊情况：中性趋势强度调整
    if(result.direction == TREND_NEUTRAL) {
        if(absScore < 5.0) {
            result.strength = STRENGTH_WEAK;
        }
        else {
            result.strength = STRENGTH_WEAK; // 中性趋势最多为弱强度
        }
    }

    return result;
}