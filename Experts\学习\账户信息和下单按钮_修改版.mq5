//+------------------------------------------------------------------+
//|                                           账户信息和下单按钮.mq5 |
//+------------------------------------------------------------------+
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Arrays\ArrayLong.mqh>
#include <Guapit\controls\Button.mqh>

// 全局变量
CTrade                Trade;                   // 交易类
CPositionInfo         *PositionInfo;   // 改为指针类型 
CSymbolInfo           SymbolInfo;             // 品种信息类
CArrayLong            m_arr_tickets;          // 订单号数组

// UI 控件
Button btnBuySell, btnCloseAll;

//--- 全局变量 
double account_equity;         // 账户净值 
double account_balance;        // 账户余额 
double account_profit;         // 当前浮动盈亏 
double point_value;            // 点值 
double long_volume;            // 多单手数 
double short_volume;           // 空单手数 
double total_volume;           // 总手数 
double daily_profit;           // 当日收益（新增）
double daily_start_balance;    // 当日开始时的账户余额（新增）
datetime last_daily_check;     // 上次检查日期的日期（新增）
double default_volume = 0.01;  // 定义默认手数

// 定义RTOTAL和SLEEPTIME (用于平仓循环)
#define RTOTAL 3
#define SLEEPTIME 1000

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化持仓信息对象 
    PositionInfo = new CPositionInfo();
    
    // 设置交易类
    Trade.SetExpertMagicNumber(0);
    Trade.SetMarginMode();
    Trade.SetTypeFillingBySymbol(Symbol());
    Trade.SetDeviationInPoints(10);
    
    // 设置品种信息
    SymbolInfo.Name(Symbol());
    point_value = SymbolInfo.Point();
    
    // 创建UI界面
    CreateUI();
    
    // 创建信息面板
    CreateInfoPanel();
    
    // 初始化当日收益相关变量
    daily_profit = 0.0;
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    last_daily_check = 0;
    
    // 设置按钮对齐方式为右下角
    ObjectSetInteger(0, "gp_button_buysell", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
    ObjectSetInteger(0, "gp_button_close", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
    
    return(INIT_SUCCEEDED);
    
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 删除信息面板和UI界面
    ObjectsDeleteAll(0, "InfoPanel_");
    ObjectsDeleteAll(0, "gp_button_");
    
    // 释放持仓信息对象 
    if(CheckPointer(PositionInfo) == POINTER_DYNAMIC)
        delete PositionInfo;
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理按钮点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "gp_button_buysell")
        {
            // 多空双向下单按钮
            OpenBuyOrder();     // 开多单
            OpenSellOrder();   // 开空单
            btnBuySell.State(false);
            btnBuySell.Update();
        }
        else if(sparam == "gp_button_close")
        {
            // 平仓按钮点击
            CloseAllPositions();
            btnCloseAll.State(false);
            btnCloseAll.Update();
        }
    }
}


//+------------------------------------------------------------------+
//| 创建UI界面                                                       |
//+------------------------------------------------------------------+
void CreateUI()
{
    // 创建多空双向按钮
    btnBuySell = new Button();
    btnBuySell.Create("gp_button_buysell", 150, 250-50);
    btnBuySell.Size(100, 32);
    btnBuySell.Text("多空开仓");
    btnBuySell.Font("极影毁片荧圆", 12);
    btnBuySell.Color(clrWhite);
    btnBuySell.BGColor(clrMediumBlue);
    btnBuySell.BorderColor(clrDarkBlue);
    btnBuySell.State(false);
    btnBuySell.Update();
    
    // 创建平仓按钮
    btnCloseAll = new Button();
    btnCloseAll.Create("gp_button_close", 150, 200-50);
    btnCloseAll.Size(100, 32);
    btnCloseAll.Text("一键平仓");
    btnCloseAll.Font("极影毁片荧圆", 12);
    btnCloseAll.Color(clrWhite);
    btnCloseAll.BGColor(clrCrimson);
    btnCloseAll.BorderColor(clrBrown);
    btnCloseAll.State(false);
    btnCloseAll.Update();
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
    // 创建信息面板背景
    ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 5);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 25);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 220);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 210);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrMediumBlue);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
    
    // 创建标题
    ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 35);
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "账户信息");
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrWhiteSmoke);
    
    // 创建账户余额标签
    ObjectCreate(0, "InfoPanel_Balance", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_YDISTANCE, 60 + 8);
    ObjectSetString(0, "InfoPanel_Balance", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_COLOR, clrWhiteSmoke);
    
    // 创建账户净值标签
    ObjectCreate(0, "InfoPanel_Equity", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_YDISTANCE, 80 + 10);
    ObjectSetString(0, "InfoPanel_Equity", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_COLOR, clrLightYellow);
    
    // 创建浮动盈亏标签
    ObjectCreate(0, "InfoPanel_Profit", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_YDISTANCE, 100 + 13);
    ObjectSetString(0, "InfoPanel_Profit", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, clrBlack);
    
    // 创建多单总量标签
    ObjectCreate(0, "InfoPanel_LongVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_YDISTANCE, 120 + 16);
    ObjectSetString(0, "InfoPanel_LongVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建空单总量标签
    ObjectCreate(0, "InfoPanel_ShortVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_YDISTANCE, 140 + 17);
    ObjectSetString(0, "InfoPanel_ShortVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建总仓位标签
    ObjectCreate(0, "InfoPanel_TotalVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_YDISTANCE, 160 + 19);
    ObjectSetString(0, "InfoPanel_TotalVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建当日收益标签
    ObjectCreate(0, "InfoPanel_DailyProfit", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_XDISTANCE, 15);
    ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_YDISTANCE, 180+ 22); 
    ObjectSetString(0, "InfoPanel_DailyProfit", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_FONTSIZE, 14);
    ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_COLOR, clrBlack);
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
    // 获取账户信息 
    account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
    
    // 计算当日收益（新增）
    CalculateDailyProfit();
 
    // 更新账户余额
    ObjectSetString(0, "InfoPanel_Balance", OBJPROP_TEXT, StringFormat("余额: %.2f", account_balance));
    
    // 更新账户净值
    ObjectSetString(0, "InfoPanel_Equity", OBJPROP_TEXT, StringFormat("净值: %.2f", account_equity));
    
    // 更新浮动盈亏
    string profit_text = StringFormat("盈亏: %.2f", account_profit);
    color profit_color = (account_profit >= 0) ? clrYellow : clrRed;
    ObjectSetString(0, "InfoPanel_Profit", OBJPROP_TEXT, profit_text);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, profit_color);
    
    // 更新多单总量
    ObjectSetString(0, "InfoPanel_LongVolume", OBJPROP_TEXT, StringFormat("多单: %.2f", long_volume));
    
    // 更新空单总量
    ObjectSetString(0, "InfoPanel_ShortVolume", OBJPROP_TEXT, StringFormat("空单: %.2f", short_volume));
    
    // 更新总仓位
    ObjectSetString(0, "InfoPanel_TotalVolume", OBJPROP_TEXT, StringFormat("仓位: %.2f", total_volume));
    
    // 更新当日收益
    string daily_profit_text = StringFormat("当日收益: %.2f", daily_profit);
    color daily_profit_color = (daily_profit >= 0) ? clrYellow : clrRed;
    ObjectSetString(0, "InfoPanel_DailyProfit", OBJPROP_TEXT, daily_profit_text);
    ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_COLOR, daily_profit_color);
}

//+------------------------------------------------------------------+
//| 计算当日收益（新增）                                              |
//+------------------------------------------------------------------+
void CalculateDailyProfit()
{
    // 获取当前服务器时间 
    datetime current_time = TimeCurrent();
    
    // 如果是新的一天，重置当日收益 
    if(last_daily_check == 0 || TimeToString(last_daily_check, TIME_DATE) != TimeToString(current_time, TIME_DATE))
    {
        daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        last_daily_check = current_time;
    }
    
    // 计算当日收益
    daily_profit = account_equity - daily_start_balance;
}

//+------------------------------------------------------------------+
//| 更新市场数据                                                     |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
    // 获取账户信息 
    account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
    
    // 重置手数统计 
    long_volume = 0;
    short_volume = 0;
    
    int total = PositionsTotal();
    for(int i = total - 1; i >= 0; i--)
    {
        if(PositionInfo.SelectByIndex(i) && PositionInfo.Symbol() == Symbol() && PositionInfo.Magic() == 0)
        {
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionInfo.PositionType();
            if(pos_type == POSITION_TYPE_BUY)
            {
                long_volume += PositionInfo.Volume();
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                short_volume += PositionInfo.Volume();
            }
        }
    }
    
    // 计算总手数（绝对值）
    total_volume = MathAbs(long_volume - short_volume);
    
    return true;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 更新市场数据
    if(!UpdateMarketData())
        return;
        
    // 更新信息面板
    UpdateInfoPanel();
}

//+------------------------------------------------------------------+
//| 开仓函数 - 买入                                                  |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    MqlTick last_tick;
    if(!SymbolInfoTick(Symbol(), last_tick))
    {
        Print("获取市场价格失败!");
        return;
    }
    
    if(!Trade.Buy(default_volume, Symbol(), last_tick.ask, 0, 0, "多单"))
    {
        Print("买入订单执行失败! 错误代码: ", GetLastError());
        return;
    }
    
    Print("买入订单执行成功! 订单号: ", Trade.ResultOrder(), ", 成交价: ", Trade.ResultPrice());
}

//+------------------------------------------------------------------+
//| 开仓函数 - 卖出                                                  |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    MqlTick last_tick;
    if(!SymbolInfoTick(Symbol(), last_tick))
    {
        Print("获取市场价格失败!");
        return;
    }
    
    if(!Trade.Sell(default_volume, Symbol(), last_tick.bid, 0, 0, "空单"))
    {
        Print("卖出订单执行失败! 错误代码: ", GetLastError());
        return;
    }
    
    Print("卖出订单执行成功! 订单号: ", Trade.ResultOrder(), ", 成交价: ", Trade.ResultPrice());
}

//+------------------------------------------------------------------+
//| 平仓当前品种所有持仓                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    Trade.SetDeviationInPoints(INT_MAX);
    Trade.SetAsyncMode(true);
    Trade.SetMarginMode();
    Trade.LogLevel(LOG_LEVEL_ERRORS);

    for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
    {
        bool allClosed = true;
        m_arr_tickets.Shutdown();

        int total = PositionsTotal();
        for(int i = total - 1; i >= 0; i--)
        {
            if(PositionInfo.SelectByIndex(i) && PositionInfo.Symbol() == Symbol())
            {
                m_arr_tickets.Add(PositionInfo.Ticket());
            }
        }

        int ticketCount = m_arr_tickets.Total();
        for(int i = 0; i < ticketCount && !IsStopped(); i++)
        {
            ulong ticket = m_arr_tickets.At(i);
            if(PositionInfo.SelectByTicket(ticket)) 
            {
                int freeze_level = (int)SymbolInfoInteger(PositionInfo.Symbol(), SYMBOL_TRADE_FREEZE_LEVEL);
                double point = SymbolInfoDouble(PositionInfo.Symbol(), SYMBOL_POINT);
                bool TP_check = (MathAbs(PositionInfo.PriceCurrent() - PositionInfo.TakeProfit()) > freeze_level * point);
                bool SL_check = (MathAbs(PositionInfo.PriceCurrent() - PositionInfo.StopLoss()) > freeze_level * point);

                if(TP_check && SL_check)
                {
                    Trade.SetExpertMagicNumber(PositionInfo.Magic());
                    Trade.SetTypeFillingBySymbol(PositionInfo.Symbol());
                    
                    if(Trade.PositionClose(ticket) && 
                       (Trade.ResultRetcode() == TRADE_RETCODE_DONE || 
                        Trade.ResultRetcode() == TRADE_RETCODE_PLACED))
                    {
                        PrintFormat("当前品种持仓 #%I64u 已平仓", ticket);
                        PlaySound("expert.wav"); 
                    }
                    else 
                    {
                        PrintFormat("平仓失败 #%I64u: 错误代码=%u (%s)", 
                                   ticket, Trade.ResultRetcode(), Trade.ResultComment());
                        allClosed = false;
                    }
                }
                else 
                {
                    PrintFormat("无法平仓 #%I64u: 止损或止盈距离太近 [冻结]", ticket);
                    allClosed = false;
                }
            }
        }

        if(allClosed)
            break;

        Sleep(SLEEPTIME);
        PlaySound("timeout.wav"); 
    }
}    