//+------------------------------------------------------------------+
//|                                    H4极度收缩信号检测Pro_科技感UI整合版.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "2.00"
#property description "H4极度收缩信号检测Pro + 科技感UI整合版 - 专业信号检测与现代化界面"

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>

// 压缩状态枚举
enum COMPRESSION_STATE {
    NO_COMPRESSION,      // 无压缩
    LIGHT_COMPRESSION,   // 轻度压缩
    MEDIUM_COMPRESSION,  // 中度压缩
    HEAVY_COMPRESSION,   // 重度压缩
    EXTREME_COMPRESSION  // 极度压缩
};

// 趋势质量枚举
enum TREND_QUALITY {
    NO_TREND_QUALITY,           // 无趋势
    WEAK_TREND_QUALITY,         // 弱趋势
    MEDIUM_TREND_QUALITY,       // 中等趋势
    STRONG_TREND_QUALITY,       // 强趋势
    VERY_STRONG_TREND_QUALITY   // 极强趋势
};

// 市场状态枚举
enum MARKET_STATE {
    MARKET_TRENDING,    // 趋势市场
    MARKET_RANGING,     // 震荡市场
    MARKET_LOW_VOL,     // 低波动市场
    MARKET_HIGH_VOL,    // 高波动市场
    MARKET_EVENT_DRIVEN // 事件驱动市场
};

// 压缩指标结构体
struct CompressionIndicators {
    double volatility;        // 波动率压缩
    double bbWidth;          // 布林带宽度压缩
    double atrValue;         // ATR压缩
    double volumeCompress;   // 成交量压缩
    string candlePattern;    // K线形态
};

// 多时间框架压缩结构体
struct TimeframeCompression {
    COMPRESSION_STATE h1State;
    COMPRESSION_STATE h4State;
    COMPRESSION_STATE d1State;
    COMPRESSION_STATE w1State;
    COMPRESSION_STATE mnState;
    double consistencyScore;
};

// 趋势分析结构体
struct TrendAnalysis {
    int direction;                    // 趋势方向 (-1下降, 0中性, 1上升)
    double strength;                  // 趋势强度 (0-100)
    double score;                     // 趋势评分
    TREND_QUALITY quality;            // 趋势质量
    int consecutiveBars;              // 连续K线数量
    double momentumScore;             // 动量评分
    bool volumeConfirmation;          // 成交量确认
    double retracementDepth;          // 回撤深度
    double fibonacciRetrace;          // 斐波那契回撤
    bool multiTimeframeAlign;         // 多时间框架一致性
};

//--- 界面设置参数
input group "===== 科技感界面设置 ====="
input bool   EnableGlowEffect = true;        // 启用发光效果
input int    UpdateInterval = 500;           // 界面更新间隔(毫秒)
input int    PanelTransparency = 85;         // 面板透明度(0-100)

input group "===== 科技感颜色主题 ====="
input color  PrimaryColor = C'0,150,255';    // 主色调(科技蓝)
input color  SecondaryColor = C'0,255,150';  // 次色调(霓虹绿)
input color  AccentColor = C'255,100,0';     // 强调色(橙红)
input color  BackgroundColor = C'15,15,25';  // 背景色(深蓝黑)
input color  TextColor = clrWhiteSmoke;      // 文字颜色

//--- 信号检测参数
input group "===== 信号检测设置 ====="
input int    ShortPeriod = 14;               // 短期周期 (ATR, ADX等)
input int    MediumPeriod = 20;              // 中期周期 (波动率, 成交量, 布林带等)
input int    LongPeriod = 50;                // 长期周期 (趋势分析等)
input bool   EnableMultiTimeframe = true;    // 启用多时间框架分析
input int    SignalUpdateInterval = 60;      // 信号更新间隔(秒)

input group "===== Deepseek优化设置 ====="
input bool   Enable_Advanced_Trend_Filter = true;  // 启用高级趋势过滤
input double Fibonacci_Retrace_Threshold = 0.618;  // 斐波那契回撤阈值
input int    Trend_Confirmation_Bars = 3;          // 趋势确认K线数量
input double RSI_Momentum_Threshold = 45;          // RSI动量阈值
input int    ATR_History = 100;              // ATR历史均值长度
input double BW_StdDev = 1.8;                // 布林带标准差
input int    BW_History = 200;               // 布林带宽度历史长度
input double ADX_Threshold = 16;             // ADX极低阈值
input double MA_Entangle_Threshold = 0.004;  // 均线缠绕阈值
input bool   EnableAdaptiveParameters = true; // 启用自适应参数
input double LearningRate = 0.01;            // 学习率

//--- 全局变量
datetime g_lastUpdate = 0;                   // 上次更新时间
datetime g_lastMultiTfUpdate = 0;             // 上次多时间框架更新时间
bool g_initialized = false;                  // 初始化标志

// 信号检测相关全局变量
CompressionIndicators g_indicators;          // 当前时间框架指标数据
TimeframeCompression g_tfCompression;        // 多时间框架压缩数据
TrendAnalysis g_trendAnalysis;              // 趋势分析数据

// 市场状态相关全局变量
MARKET_STATE g_CurrentMarketState = MARKET_RANGING;  // 当前市场状态
MARKET_STATE g_LastMarketState = MARKET_RANGING;     // 上次市场状态
double g_Dynamic_ADX_Threshold;              // 动态ADX阈值
double g_Dynamic_BW_Threshold;               // 动态布林带阈值
double g_Dynamic_Entangle_Threshold;         // 动态均线缠绕阈值
datetime g_lastStateUpdate = 0;              // 上次状态更新时间

//--- 科技感界面元素名称定义
#define MAIN_PANEL "TechUI_MainPanel"
#define ACCOUNT_PANEL "TechUI_AccountPanel"
#define MARKET_PANEL "TechUI_MarketPanel"
#define TRADING_PANEL "TechUI_TradingPanel"
#define SYSTEM_PANEL "TechUI_SystemPanel"

//--- 信号检测界面元素名称定义
#define COMPRESSION_PANEL "CompressionPanel"
#define VOLATILITY_LABEL "VolatilityLabel"
#define BB_WIDTH_LABEL "BBWidthLabel"
#define ATR_LABEL "ATRLabel"
#define VOLUME_LABEL "VolumeLabel"
#define PATTERN_LABEL "PatternLabel"
#define STATE_LABEL "StateLabel"
#define COMPRESSION_METER "CompressionMeter"
#define H1_LABEL "H1Label"
#define H4_LABEL "H4Label"
#define D1_LABEL "D1Label"
#define W1_LABEL "W1Label"
#define MN_LABEL "MNLabel"
#define CONSISTENCY_LABEL "ConsistencyLabel"
#define BREAKOUT_PROB_LABEL "BreakoutProbLabel"
#define BREAKOUT_METER "BreakoutMeter"
#define TREND_DIRECTION_LABEL "TrendDirectionLabel"
#define TREND_STRENGTH_LABEL "TrendStrengthLabel"
#define TREND_SCORE_LABEL "TrendScoreLabel"
#define TREND_METER "TrendMeter"
#define EXPECTED_DIRECTION_LABEL "ExpectedDirectionLabel"
#define MARKET_STATE_LABEL "MarketStateLabel"

//+------------------------------------------------------------------+
//| 统一指标管理器类                                                  |
//+------------------------------------------------------------------+
class UnifiedIndicatorManager {
private:
    double m_atr;
    double m_atr_ma;
    double m_adx;
    double m_di_plus;
    double m_di_minus;
    datetime m_lastCalc;

    // 缓存的ATR句柄和数据
    int m_atrHandle;
    double m_atrValues[];

public:
    UnifiedIndicatorManager() {
        m_lastCalc = 0;
        m_atrHandle = INVALID_HANDLE;
        ArraySetAsSeries(m_atrValues, true);
    }

    ~UnifiedIndicatorManager() {
        if(m_atrHandle != INVALID_HANDLE) {
            IndicatorRelease(m_atrHandle);
        }
    }

    // 获取ATR值
    double GetATR(int period) {
        if(TimeCurrent() - m_lastCalc > 300) { // 5分钟刷新一次
            int handle = iATR(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double atr[];
                ArraySetAsSeries(atr, true);
                if(CopyBuffer(handle, 0, 0, 1, atr) > 0) {
                    m_atr = atr[0];
                }
                IndicatorRelease(handle);
            }
            m_lastCalc = TimeCurrent();
        }
        return m_atr;
    }

    // 获取ADX值
    double GetADX(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double adx[];
                ArraySetAsSeries(adx, true);
                if(CopyBuffer(handle, 0, 0, 1, adx) > 0) {
                    m_adx = adx[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_adx;
    }

    // 重置缓存
    void Reset() {
        m_lastCalc = 0;
    }
};

// 创建统一指标管理器实例
UnifiedIndicatorManager g_indicatorManager;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化数据
    InitializeData();

    // 创建整合版科技感UI界面
    CreateIntegratedTechUI();

    // 设置定时器
    EventSetMillisecondTimer(UpdateInterval);
    EventSetTimer(1); // 信号检测定时器

    g_initialized = true;
    Print("H4极度收缩信号检测Pro_科技感UI整合版已启动");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 删除所有UI元素
    CleanupIntegratedUI();

    // 停止定时器
    EventKillTimer();

    // 重置全局状态变量
    g_initialized = false;
    g_CurrentMarketState = MARKET_RANGING;
    g_LastMarketState = MARKET_RANGING;

    Print("H4极度收缩信号检测Pro_科技感UI整合版已停止，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 更新信号检测数据
    UpdateSignalData();

    // 更新市场状态
    UpdateMarketState();

    // 更新界面显示
    UpdateIntegratedUI();
}

//+------------------------------------------------------------------+
//| 初始化数据                                                       |
//+------------------------------------------------------------------+
void InitializeData()
{
    // 初始化市场状态
    g_CurrentMarketState = MARKET_RANGING;
    g_LastMarketState = MARKET_RANGING;

    // 初始化动态参数
    g_Dynamic_ADX_Threshold = ADX_Threshold;
    g_Dynamic_BW_Threshold = BW_StdDev;
    g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold;
}

//+------------------------------------------------------------------+
//| 创建整合版科技感UI界面 (新布局)                                  |
//+------------------------------------------------------------------+
void CreateIntegratedTechUI()
{
    // 创建主面板背景
    CreateTechMainPanel();

    // --- 新布局创建函数 ---
    CreateCoreStatusPanel();      // 创建核心状态面板 (顶部)
    CreateAnalysisPanels();       // 创建分析面板 (三列)
    CreateSystemAndTradePanel();  // 创建系统与交易面板 (底部)

    // 创建装饰元素
    CreateTechDecorations();

    // 初始更新界面
    UpdateIntegratedUI();
}

//+------------------------------------------------------------------+
//| 创建科技感主面板背景 (新布局)                                     |
//+------------------------------------------------------------------+
void CreateTechMainPanel()
{
    // 主背景面板 - 调整为更宽的布局
    if(ObjectCreate(0, MAIN_PANEL, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_XDISTANCE, 5);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_YDISTANCE, 5);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_XSIZE, 950); // 显著加宽以适应三列布局
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_YSIZE, 650); // 调整高度
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_BGCOLOR, BackgroundColor);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_COLOR, PrimaryColor);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, MAIN_PANEL, OBJPROP_HIDDEN, true);

        if(EnableGlowEffect)
        {
            ObjectSetInteger(0, MAIN_PANEL, OBJPROP_WIDTH, 3);
        }
    }

    // 主标题 - 调整位置
    if(ObjectCreate(0, "TechUI_Title", OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetString(0, "TechUI_Title", OBJPROP_TEXT, "H4 EXTREME COMPRESSION SIGNAL DETECTOR PRO");
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_XDISTANCE, 475); // 居中
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_YDISTANCE, 25);
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_COLOR, PrimaryColor);
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_FONTSIZE, 16);
        ObjectSetString(0, "TechUI_Title", OBJPROP_FONT, "Arial Black");
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_ANCHOR, ANCHOR_CENTER);
        ObjectSetInteger(0, "TechUI_Title", OBJPROP_SELECTABLE, false);

        if(EnableGlowEffect)
        {
            ObjectSetInteger(0, "TechUI_Title", OBJPROP_BGCOLOR, C'20,20,30');
        }
    }
}

//+------------------------------------------------------------------+
//| 创建核心状态面板 (新布局)                                         |
//+------------------------------------------------------------------+
void CreateCoreStatusPanel()
{
    int x_start = 15;
    int y_start = 50;
    int panel_width = 920;
    int panel_height = 80;

    // --- 创建背景面板 ---
    if(ObjectCreate(0, "CoreStatusPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_YDISTANCE, y_start);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_XSIZE, panel_width);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_YSIZE, panel_height);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_BGCOLOR, C'20,20,30');
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_BORDER_COLOR, PrimaryColor);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_WIDTH, EnableGlowEffect ? 2 : 1);
        ObjectSetInteger(0, "CoreStatusPanel_BG", OBJPROP_SELECTABLE, false);
    }

    // --- 创建信息标签 ---
    int col1_x = x_start + 20;
    int col2_x = x_start + 320;
    int col3_x = x_start + 620;
    int row1_y = y_start + 15;
    int row2_y = y_start + 45;

    // 市场信息
    CreateTechLabel("CSP_Market_Title", "MARKET STATUS", col1_x, row1_y, SecondaryColor, 11);
    CreateTechLabel("CSP_Symbol", "Symbol: EURUSD", col1_x, row2_y, TextColor, 10);
    CreateTechLabel("CSP_Price", "Price: 1.08500", col1_x + 120, row2_y, TextColor, 10);

    // 账户信息
    CreateTechLabel("CSP_Account_Title", "ACCOUNT INFO", col2_x, row1_y, SecondaryColor, 11);
    CreateTechLabel("CSP_Balance", "Balance: 10000.00", col2_x, row2_y, TextColor, 10);
    CreateTechLabel("CSP_Equity", "Equity: 10000.00", col2_x + 150, row2_y, TextColor, 10);

    // 核心状态
    CreateTechLabel("CSP_Core_Title", "CORE SIGNAL", col3_x, row1_y, AccentColor, 11);
    CreateTechLabel(MARKET_STATE_LABEL, "Market State: RANGING", col3_x, row2_y, TextColor, 10);
    CreateTechLabel(STATE_LABEL, "Compression: NONE", col3_x + 180, row2_y, TextColor, 10);
}

//+------------------------------------------------------------------+
//| 创建分析面板 (新布局)                                             |
//+------------------------------------------------------------------+
void CreateAnalysisPanels()
{
    int y_start = 145;
    int panel_height = 350;
    int panel_width = 300;
    int gap = 15;

    int x1 = 15;
    int x2 = x1 + panel_width + gap;
    int x3 = x2 + panel_width + gap;

    // --- 面板 1: 基础压缩指标 ---
    if(ObjectCreate(0, "Analysis_BG1", OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_XDISTANCE, x1);
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_YDISTANCE, y_start);
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_XSIZE, panel_width);
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_YSIZE, panel_height);
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_BGCOLOR, C'20,20,30');
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_BORDER_COLOR, PrimaryColor);
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, "Analysis_BG1", OBJPROP_SELECTABLE, false);
    }
    CreateTechLabel("Analysis_Title1", "BASIC COMPRESSION", x1 + 10, y_start + 10, PrimaryColor, 12);

    // --- 面板 2: 多时间框架分析 ---
    if(ObjectCreate(0, "Analysis_BG2", OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_XDISTANCE, x2);
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_YDISTANCE, y_start);
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_XSIZE, panel_width);
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_YSIZE, panel_height);
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_BGCOLOR, C'25,20,30');
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_BORDER_COLOR, SecondaryColor);
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, "Analysis_BG2", OBJPROP_SELECTABLE, false);
    }
    CreateTechLabel("Analysis_Title2", "MULTI-TIMEFRAME", x2 + 10, y_start + 10, SecondaryColor, 12);

    // --- 面板 3: 趋势分析 ---
    if(ObjectCreate(0, "Analysis_BG3", OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_XDISTANCE, x3);
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_YDISTANCE, y_start);
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_XSIZE, panel_width);
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_YSIZE, panel_height);
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_BGCOLOR, C'30,25,20');
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_BORDER_COLOR, AccentColor);
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, "Analysis_BG3", OBJPROP_SELECTABLE, false);
    }
    CreateTechLabel("Analysis_Title3", "TREND ANALYSIS", x3 + 10, y_start + 10, AccentColor, 12);

    // 创建信号检测标签 (将被重构以适应新布局)
    CreateSignalDetectionLabels();
}

//+------------------------------------------------------------------+
//| 创建系统与交易面板 (新布局)                                       |
//+------------------------------------------------------------------+
void CreateSystemAndTradePanel()
{
    // 此处将添加交易统计和系统信息的标签创建代码
}

//+------------------------------------------------------------------+
//| 创建信号检测标签 (新布局)                                         |
//+------------------------------------------------------------------+
void CreateSignalDetectionLabels() 
{
    int y_start = 145 + 40; // Base Y for labels inside panels
    int x1 = 15 + 10;
    int x2 = 330 + 10;
    int x3 = 645 + 10;
    int line_height = 20;

    // === 面板 1: 基础压缩指标 ===
    int current_y = y_start;
    CreateTechLabel(VOLATILITY_LABEL, "Volatility Comp: 0.00%", x1, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel(BB_WIDTH_LABEL, "BB Width Comp: 0.00%", x1, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel(ATR_LABEL, "ATR Comp: 0.00%", x1, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel(VOLUME_LABEL, "Volume Comp: 0.00%", x1, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel(PATTERN_LABEL, "Candle Pattern: -", x1, current_y, TextColor, 10);
    current_y += line_height + 10;
    CreateTechMeter(COMPRESSION_METER, x1, current_y, 280);

    // === 面板 2: 多时间框架分析 ===
    if(EnableMultiTimeframe) {
        current_y = y_start;
        CreateTechLabel(H1_LABEL, "H1: NO COMPRESSION", x2, current_y, TextColor, 10);
        current_y += line_height;
        CreateTechLabel(H4_LABEL, "H4: NO COMPRESSION", x2, current_y, TextColor, 10);
        current_y += line_height;
        CreateTechLabel(D1_LABEL, "D1: NO COMPRESSION", x2, current_y, TextColor, 10);
        current_y += line_height;
        CreateTechLabel(W1_LABEL, "W1: NO COMPRESSION", x2, current_y, TextColor, 10);
        current_y += line_height;
        CreateTechLabel(MN_LABEL, "MN: NO COMPRESSION", x2, current_y, TextColor, 10);
        current_y += line_height;
        CreateTechLabel(CONSISTENCY_LABEL, "TF Consistency: 0.00%", x2, current_y, clrYellow, 10);
        current_y += line_height + 10;
        CreateTechLabel(BREAKOUT_PROB_LABEL, "Breakout Prob: 0.00%", x2, current_y, clrMagenta, 11);
        current_y += line_height;
        CreateTechMeter(BREAKOUT_METER, x2, current_y, 280);
    }

    // === 面板 3: 趋势分析 ===
    current_y = y_start;
    CreateTechLabel(TREND_DIRECTION_LABEL, "Direction: NEUTRAL", x3, current_y, TextColor, 10);
    CreateTechLabel("TrendQualityLabel", "Quality: NONE", x3 + 150, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel(TREND_STRENGTH_LABEL, "Strength: 0.0", x3, current_y, TextColor, 10);
    CreateTechLabel(TREND_SCORE_LABEL, "Score: 0.0", x3 + 150, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel("MomentumScoreLabel", "Momentum: 0.0", x3, current_y, TextColor, 10);
    CreateTechLabel("VolumeConfirmLabel", "Volume Confirm: No", x3 + 150, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel("RetracementLabel", "Retracement: 0.0%", x3, current_y, TextColor, 10);
    CreateTechLabel("FibRetracementLabel", "Fib Retrace: 0.000", x3 + 150, current_y, TextColor, 10);
    current_y += line_height;
    CreateTechLabel("TrendConsecutiveLabel", "Consec. Bars: 0", x3, current_y, TextColor, 10);
    CreateTechLabel("MTFAlignLabel", "MTF Align: No", x3 + 150, current_y, TextColor, 10);
    current_y += line_height + 10;
    CreateTechLabel(EXPECTED_DIRECTION_LABEL, "Expected Move: UNKNOWN", x3, current_y, clrYellow, 11);
    current_y += line_height;
    CreateTechMeter(TREND_METER, x3, current_y, 280);
}

    CreateTechLabel("TechUI_Profit", "浮盈: 0.00", xStart + 10, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_DailyProfit", "日盈: 0.00", xStart + 180, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_Margin", "保证金: 0.00", xStart + 10, yStart + 75, TextColor, 10);
    CreateTechLabel("TechUI_FreeMargin", "可用: 0.00", xStart + 180, yStart + 75, TextColor, 10);
    CreateTechLabel("TechUI_MarginLevel", "保证金比例: 0.00%", xStart + 10, yStart + 95, TextColor, 10);
    CreateTechLabel("TechUI_RiskLevel", "风险等级: 低", xStart + 10, yStart + 115, TextColor, 10);
}

//+------------------------------------------------------------------+
//| 创建科技感标签辅助函数                                            |
//+------------------------------------------------------------------+
void CreateTechLabel(string name, string text, int x, int y, color textColor, int fontSize = 10)
{
    if(ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetString(0, name, OBJPROP_TEXT, text);
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
        ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontSize);
        ObjectSetString(0, name, OBJPROP_FONT, "Consolas");
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);

        // 添加发光效果
        if(EnableGlowEffect && fontSize >= 12)
        {
            ObjectSetInteger(0, name, OBJPROP_BGCOLOR, C'20,20,30');
        }
    }
}

//+------------------------------------------------------------------+
//| 创建科技感度量表 (可定制位置和大小)                               |
//+------------------------------------------------------------------+
void CreateTechMeter(string meterName, int x, int y, int width) {
    // 创建背景框 (可选)
    string bgName = meterName + "_BG";
    if(ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, x - 5);
        ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, y - 5);
        ObjectSetInteger(0, bgName, OBJPROP_XSIZE, width + 10);
        ObjectSetInteger(0, bgName, OBJPROP_YSIZE, 25);
        ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, clrNone); // 透明背景
        ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, bgName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bgName, OBJPROP_SELECTABLE, false);
    }

    // 创建进度条主体
    string barName = meterName + "_Bar";
    if(ObjectCreate(0, barName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, barName, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, barName, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, barName, OBJPROP_XSIZE, 0); // 初始为0
        ObjectSetInteger(0, barName, OBJPROP_YSIZE, 15);
        ObjectSetInteger(0, barName, OBJPROP_BGCOLOR, PrimaryColor);
        ObjectSetInteger(0, barName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, barName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, barName, OBJPROP_SELECTABLE, false);
    }

    // 创建外边框
    if(ObjectCreate(0, meterName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, meterName, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, meterName, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, meterName, OBJPROP_XSIZE, width);
        ObjectSetInteger(0, meterName, OBJPROP_YSIZE, 15);
        ObjectSetInteger(0, meterName, OBJPROP_BGCOLOR, C'40,40,40');
        ObjectSetInteger(0, meterName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, meterName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, meterName, OBJPROP_SELECTABLE, false);
    }

    // 添加关键阈值标记线
    CreateTechThresholdMarks(meterName, x, y, width);
}

//+------------------------------------------------------------------+
//| 创建科技感阈值标记                                                |
//+------------------------------------------------------------------+
void CreateTechThresholdMarks(string meterName, int x, int y, int width) {
    int thresholds[6];
    thresholds[0] = 30; thresholds[1] = 45; thresholds[2] = 60;
    thresholds[3] = 70; thresholds[4] = 85; thresholds[5] = 95;

    for(int i = 0; i < 6; i++) {
        int threshold = thresholds[i];
        int xPos = x + (int)(width * (threshold / 100.0));

        // 创建标记线
        string markName = meterName + "_Mark" + IntegerToString(threshold);
        if(ObjectCreate(0, markName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
            ObjectSetInteger(0, markName, OBJPROP_XDISTANCE, xPos);
            ObjectSetInteger(0, markName, OBJPROP_YDISTANCE, y);
            ObjectSetInteger(0, markName, OBJPROP_XSIZE, 1);
            ObjectSetInteger(0, markName, OBJPROP_YSIZE, 15);
            ObjectSetInteger(0, markName, OBJPROP_BGCOLOR, clrWhite);
            ObjectSetInteger(0, markName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
            ObjectSetInteger(0, markName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            ObjectSetInteger(0, markName, OBJPROP_SELECTABLE, false);
        }

        // 创建标记标签
        string labelName = meterName + "_Label" + IntegerToString(threshold);
        if(ObjectCreate(0, labelName, OBJ_LABEL, 0, 0, 0)) {
            ObjectSetString(0, labelName, OBJPROP_TEXT, IntegerToString(threshold));
            ObjectSetInteger(0, labelName, OBJPROP_XDISTANCE, xPos);
            ObjectSetInteger(0, labelName, OBJPROP_YDISTANCE, y + 18);
            ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrSilver);
            ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 7);
            ObjectSetInteger(0, labelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_CENTER);
            ObjectSetInteger(0, labelName, OBJPROP_SELECTABLE, false);
        }
    }
}

//+------------------------------------------------------------------+
//| 创建科技感市场状态面板                                            |
//+------------------------------------------------------------------+
void CreateTechMarketPanel()
{
    int xStart = 420;  // 右侧位置
    int yStart = 210;
    int panelHeight = 140;

    // 市场面板背景
    if(ObjectCreate(0, MARKET_PANEL, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_XDISTANCE, xStart);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_YDISTANCE, yStart);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_XSIZE, 360);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_YSIZE, panelHeight);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_BGCOLOR, C'35,25,25');
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_COLOR, AccentColor);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_WIDTH, EnableGlowEffect ? 2 : 1);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, MARKET_PANEL, OBJPROP_SELECTABLE, false);
    }

    // 市场面板标题
    CreateTechLabel("TechUI_MarketTitle", "▣ 市场状态", xStart + 10, yStart + 10, AccentColor, 12);

    // 市场信息标签
    CreateTechLabel("TechUI_Symbol", "品种: " + _Symbol, xStart + 10, yStart + 35, TextColor, 10);
    CreateTechLabel("TechUI_Price", "价格: 0.00000", xStart + 180, yStart + 35, TextColor, 10);
    CreateTechLabel("TechUI_Spread", "点差: 0", xStart + 10, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_Volume", "成交量: 0", xStart + 180, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_Volatility", "波动率: 0.00%", xStart + 10, yStart + 75, TextColor, 10);
    CreateTechLabel("TechUI_TrendStrength", "趋势强度: 0", xStart + 180, yStart + 75, TextColor, 10);
    CreateTechLabel("TechUI_ServerTime", "服务器时间: 00:00:00", xStart + 10, yStart + 95, TextColor, 10);
    CreateTechLabel("TechUI_MarketSession", "交易时段: 未知", xStart + 10, yStart + 115, TextColor, 10);
}

//+------------------------------------------------------------------+
//| 创建科技感交易统计面板                                            |
//+------------------------------------------------------------------+
void CreateTechTradingPanel()
{
    int xStart = 420;  // 右侧位置
    int yStart = 370;
    int panelHeight = 160;

    // 交易面板背景
    if(ObjectCreate(0, TRADING_PANEL, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_XDISTANCE, xStart);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_YDISTANCE, yStart);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_XSIZE, 360);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_YSIZE, panelHeight);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_BGCOLOR, C'25,35,25');
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_COLOR, PrimaryColor);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_WIDTH, EnableGlowEffect ? 2 : 1);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, TRADING_PANEL, OBJPROP_SELECTABLE, false);
    }

    // 交易面板标题
    CreateTechLabel("TechUI_TradingTitle", "▣ 交易统计", xStart + 10, yStart + 10, PrimaryColor, 12);

    // 交易统计标签
    CreateTechLabel("TechUI_Positions", "持仓数: 0", xStart + 10, yStart + 35, TextColor, 10);
    CreateTechLabel("TechUI_TotalVolume", "总手数: 0.00", xStart + 180, yStart + 35, TextColor, 10);
    CreateTechLabel("TechUI_LongVolume", "多单: 0.00", xStart + 10, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_ShortVolume", "空单: 0.00", xStart + 180, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_TodayTrades", "今日交易: 0", xStart + 10, yStart + 75, TextColor, 10);
    CreateTechLabel("TechUI_WinRate", "胜率: 0.00%", xStart + 180, yStart + 75, TextColor, 10);
    
    // 新增盈亏统计
    CreateTechLabel("TechUI_HistoryProfit", "历史盈亏: 0.00", xStart + 10, yStart + 95, TextColor, 10);
    CreateTechLabel("TechUI_CurrentProfit", "当前盈亏: 0.00", xStart + 180, yStart + 95, TextColor, 10);
    CreateTechLabel("TechUI_MaxDrawdown", "最大回撤: 0.00%", xStart + 10, yStart + 115, TextColor, 10);
    CreateTechLabel("TechUI_ProfitFactor", "盈利因子: 0.00", xStart + 180, yStart + 95, TextColor, 10);
    CreateTechLabel("TechUI_RiskLevel", "风险等级: 低", xStart + 10, yStart + 135, TextColor, 10);
    CreateTechLabel("TechUI_SignalQuality", "信号质量: 未知", xStart + 180, yStart + 135, TextColor, 10);
    CreateTechLabel("TechUI_LastSignal", "最后信号: 无", xStart + 10, yStart + 155, TextColor, 10);
}

//+------------------------------------------------------------------+
//| 创建科技感系统状态面板                                            |
//+------------------------------------------------------------------+
void CreateTechSystemPanel()
{
    int xStart = 420;  // 右侧位置
    int yStart = 550;
    int panelHeight = 120;

    // 系统面板背景
    if(ObjectCreate(0, SYSTEM_PANEL, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_XDISTANCE, xStart);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_YDISTANCE, yStart);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_XSIZE, 360);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_YSIZE, panelHeight);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_BGCOLOR, C'35,35,25');
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_COLOR, SecondaryColor);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_WIDTH, EnableGlowEffect ? 2 : 1);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, SYSTEM_PANEL, OBJPROP_SELECTABLE, false);
    }

    // 系统面板标题
    CreateTechLabel("TechUI_SystemTitle", "▣ 系统状态", xStart + 10, yStart + 10, SecondaryColor, 12);

    // 系统状态标签
    CreateTechLabel("TechUI_Connection", "连接: 正常", xStart + 10, yStart + 35, TextColor, 10);
    CreateTechLabel("TechUI_Ping", "延迟: 0ms", xStart + 180, yStart + 35, TextColor, 10);
    CreateTechLabel("TechUI_Account", "账户: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)), xStart + 10, yStart + 55, TextColor, 10);
    CreateTechLabel("TechUI_Server", "服务器: " + AccountInfoString(ACCOUNT_SERVER), xStart + 10, yStart + 75, TextColor, 10);
    CreateTechLabel("TechUI_Version", "版本: MT5 Build " + IntegerToString(TerminalInfoInteger(TERMINAL_BUILD)), xStart + 10, yStart + 95, TextColor, 10);
}





//+------------------------------------------------------------------+
//| 创建科技感装饰元素                                                |
//+------------------------------------------------------------------+
void CreateTechDecorations()
{
    // 创建分隔线
    CreateTechSeparatorLine("TechUI_Sep1", 15, 225, 380);  // 信号检测区域间分隔线
    CreateTechSeparatorLine("TechUI_Sep2", 15, 420, 380);  // 信号检测区域间分隔线
    CreateTechSeparatorLine("TechUI_Sep3", 410, 50, 1, 620);  // 垂直分隔线
    CreateTechSeparatorLine("TechUI_Sep4", 420, 200, 360);  // 右侧面板间分隔线
    CreateTechSeparatorLine("TechUI_Sep5", 420, 360, 360);  // 右侧面板间分隔线
    CreateTechSeparatorLine("TechUI_Sep6", 420, 540, 360);  // 右侧面板间分隔线

    // 创建状态指示器
    CreateTechStatusIndicator("TechUI_StatusLED", 750, 25);

    // 创建进度条背景
    CreateTechProgressBar("TechUI_ProgressBar", 430, 680, 340);

    // 创建科技感装饰点
    CreateTechDecorativeDots();
}

//+------------------------------------------------------------------+
//| 创建科技感分隔线                                                  |
//+------------------------------------------------------------------+
void CreateTechSeparatorLine(string name, int x, int y, int width, int height = 1)
{
    if(ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
        ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
        ObjectSetInteger(0, name, OBJPROP_BGCOLOR, PrimaryColor);
        ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);

        if(EnableGlowEffect)
        {
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
            ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, SecondaryColor);
        }
    }
}

//+------------------------------------------------------------------+
//| 创建科技感状态指示器                                              |
//+------------------------------------------------------------------+
void CreateTechStatusIndicator(string name, int x, int y)
{
    if(ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, name, OBJPROP_XSIZE, 15);
        ObjectSetInteger(0, name, OBJPROP_YSIZE, 15);
        ObjectSetInteger(0, name, OBJPROP_BGCOLOR, SecondaryColor);
        ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);

        if(EnableGlowEffect)
        {
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
            ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrWhite);
        }
    }
}

//+------------------------------------------------------------------+
//| 创建科技感进度条                                                  |
//+------------------------------------------------------------------+
void CreateTechProgressBar(string name, int x, int y, int width)
{
    // 进度条背景
    string bgName = name + "_BG";
    if(ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, bgName, OBJPROP_XSIZE, width);
        ObjectSetInteger(0, bgName, OBJPROP_YSIZE, 10);
        ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, C'50,50,50');
        ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, bgName, OBJPROP_COLOR, PrimaryColor);
        ObjectSetInteger(0, bgName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bgName, OBJPROP_SELECTABLE, false);
    }

    // 进度条填充
    string fillName = name + "_Fill";
    if(ObjectCreate(0, fillName, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, fillName, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, fillName, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, fillName, OBJPROP_XSIZE, 1);
        ObjectSetInteger(0, fillName, OBJPROP_YSIZE, 10);
        ObjectSetInteger(0, fillName, OBJPROP_BGCOLOR, PrimaryColor);
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, fillName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, fillName, OBJPROP_SELECTABLE, false);

        if(EnableGlowEffect)
        {
            ObjectSetInteger(0, fillName, OBJPROP_WIDTH, 2);
        }
    }
}

//+------------------------------------------------------------------+
//| 创建科技感装饰点                                                  |
//+------------------------------------------------------------------+
void CreateTechDecorativeDots()
{
    // 在各个角落创建装饰点
    int dotPositions[][2] = {{10, 10}, {790, 10}, {10, 690}, {790, 690}};

    for(int i = 0; i < 4; i++)
    {
        string dotName = "TechUI_Dot" + IntegerToString(i);
        if(ObjectCreate(0, dotName, OBJ_RECTANGLE_LABEL, 0, 0, 0))
        {
            ObjectSetInteger(0, dotName, OBJPROP_XDISTANCE, dotPositions[i][0]);
            ObjectSetInteger(0, dotName, OBJPROP_YDISTANCE, dotPositions[i][1]);
            ObjectSetInteger(0, dotName, OBJPROP_XSIZE, 8);
            ObjectSetInteger(0, dotName, OBJPROP_YSIZE, 8);
            ObjectSetInteger(0, dotName, OBJPROP_BGCOLOR, AccentColor);
            ObjectSetInteger(0, dotName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
            ObjectSetInteger(0, dotName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            ObjectSetInteger(0, dotName, OBJPROP_SELECTABLE, false);

            if(EnableGlowEffect)
            {
                ObjectSetInteger(0, dotName, OBJPROP_WIDTH, 2);
                ObjectSetInteger(0, dotName, OBJPROP_BORDER_COLOR, clrWhite);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 更新信号检测数据                                                  |
//+------------------------------------------------------------------+
void UpdateSignalData()
{
    if(!g_initialized) return;

    // 更新基础压缩指标
    g_indicators = AnalyzeCompression(MediumPeriod,    // 波动率周期
                                     MediumPeriod,     // 布林带周期
                                     BW_StdDev,        // 布林带标准差
                                     ShortPeriod,      // ATR周期
                                     MediumPeriod);    // 成交量周期

    // 更新多时间框架分析
    if(EnableMultiTimeframe) {
        datetime currentTime = TimeCurrent();
        if(currentTime - g_lastMultiTfUpdate >= SignalUpdateInterval) {
            g_tfCompression = AnalyzeTimeframes(MediumPeriod,    // 波动率周期
                                               MediumPeriod,     // 布林带周期
                                               BW_StdDev,        // 布林带标准差
                                               ShortPeriod,      // ATR周期
                                               MediumPeriod);    // 成交量周期
            g_lastMultiTfUpdate = currentTime;
        }
    }

    // 更新趋势分析
    g_trendAnalysis = AnalyzeTrend(LongPeriod);
}

//+------------------------------------------------------------------+
//| 更新市场状态                                                      |
//+------------------------------------------------------------------+
void UpdateMarketState()
{
    if(!g_initialized) return;

    // 每30秒更新一次市场状态
    datetime currentTime = TimeCurrent();
    if(currentTime - g_lastStateUpdate >= 30) {
        AdjustDynamicParameters();
        g_lastStateUpdate = currentTime;
    }
}

//+------------------------------------------------------------------+
//| 动态参数调整函数                                                 |
//+------------------------------------------------------------------+
void AdjustDynamicParameters() {
    g_LastMarketState = g_CurrentMarketState;
    g_CurrentMarketState = AnalyzeMarketState();

    // 使用预设调整系数
    static const double stateFactors[5][3] = {
        {0.9, 1.2, 1.1},    // TRENDING
        {1.1, 0.9, 0.9},    // RANGING
        {0.8, 0.8, 0.85},   // LOW_VOL
        {1.25, 1.3, 1.2},   // HIGH_VOL
        {1.4, 1.5, 1.3}     // EVENT_DRIVEN
    };

    int stateIndex = (int)g_CurrentMarketState;
    if(stateIndex >= 0 && stateIndex < 5) {
        g_Dynamic_ADX_Threshold = ADX_Threshold * stateFactors[stateIndex][0];
        g_Dynamic_BW_Threshold = BW_StdDev * stateFactors[stateIndex][1];
        g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * stateFactors[stateIndex][2];
    } else {
        // 默认值
        g_Dynamic_ADX_Threshold = ADX_Threshold;
        g_Dynamic_BW_Threshold = BW_StdDev;
        g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold;
    }
}

//+------------------------------------------------------------------+
//| 分析市场状态                                                      |
//+------------------------------------------------------------------+
MARKET_STATE AnalyzeMarketState() {
    // 1. 波动率分析
    double atr = g_indicatorManager.GetATR(14);
    double atr_ma = 0.0;

    // 获取ATR均值
    int maHandle = iMA(_Symbol, PERIOD_D1, 20, 0, MODE_SMA, PRICE_TYPICAL);
    if(maHandle != INVALID_HANDLE) {
        double ma[];
        ArraySetAsSeries(ma, true);
        if(CopyBuffer(maHandle, 0, 0, 1, ma) > 0) {
            atr_ma = ma[0];
        }
        IndicatorRelease(maHandle);
    }

    double volatility_ratio = (atr_ma > 0) ? atr / atr_ma : 1.0;

    // 2. 趋势强度分析
    double adx = g_indicatorManager.GetADX(14);

    // 状态决策逻辑
    if(volatility_ratio > 1.4) {
        return MARKET_HIGH_VOL;
    }
    else if(volatility_ratio < 0.7) {
        return MARKET_LOW_VOL;
    }
    else if(adx > 25) {
        return MARKET_TRENDING;
    }
    else {
        return MARKET_RANGING;
    }
}

//+------------------------------------------------------------------+
//| 更新整合版UI界面                                                  |
//+------------------------------------------------------------------+
void UpdateIntegratedUI()
{
    if(!g_initialized) return;

    // 更新信号检测显示
    UpdateSignalDetectionDisplay();

    // 更新科技感功能面板
    UpdateTechFunctionalPanels();

    // 更新状态指示器
    UpdateTechStatusIndicator();

    // 更新进度条
    UpdateTechProgressBar();

    // 更新市场状态显示
    UpdateMarketStateDisplay();
}

//+------------------------------------------------------------------+
//| 更新信号检测显示                                                  |
//+------------------------------------------------------------------+
void UpdateSignalDetectionDisplay() {
    // 计算综合压缩分数
    double compressionScore = (g_indicators.volatility * 0.25 +
                             g_indicators.bbWidth * 0.25 +
                             g_indicators.atrValue * 0.20 +
                             g_indicators.volumeCompress * 0.20);

    // 更新基本指标标签
    string volatilityText = StringFormat("波动率压缩: %.2f%%", g_indicators.volatility);
    string bbWidthText = StringFormat("布林带压缩: %.2f%%", g_indicators.bbWidth);
    string atrText = StringFormat("ATR压缩: %.2f%%", g_indicators.atrValue);
    string volumeText = StringFormat("成交量压缩: %.2f%%", g_indicators.volumeCompress);
    string patternText = StringFormat("K线形态: %s", g_indicators.candlePattern);

    // 获取压缩状态
    COMPRESSION_STATE state = EvaluateCompression(g_indicators);
    string stateText = StringFormat("压缩状态: %s", GetCompressionStateDescription(state));

    // 更新标签文本
    ObjectSetString(0, VOLATILITY_LABEL, OBJPROP_TEXT, volatilityText);
    ObjectSetString(0, BB_WIDTH_LABEL, OBJPROP_TEXT, bbWidthText);
    ObjectSetString(0, ATR_LABEL, OBJPROP_TEXT, atrText);
    ObjectSetString(0, VOLUME_LABEL, OBJPROP_TEXT, volumeText);
    ObjectSetString(0, PATTERN_LABEL, OBJPROP_TEXT, patternText);
    ObjectSetString(0, STATE_LABEL, OBJPROP_TEXT, stateText);

    // 更新压缩度量表
    UpdateTechMeter(COMPRESSION_METER, compressionScore);

    // 设置状态标签颜色
    color stateColor = clrYellow;
    switch(state) {
        case EXTREME_COMPRESSION: stateColor = clrRed; break;
        case HEAVY_COMPRESSION:   stateColor = clrOrange; break;
        case MEDIUM_COMPRESSION:  stateColor = clrYellow; break;
        case LIGHT_COMPRESSION:   stateColor = clrLightGreen; break;
        default:                  stateColor = clrGreen; break;
    }
    ObjectSetInteger(0, STATE_LABEL, OBJPROP_COLOR, stateColor);

    // 更新多时间框架分析
    if(EnableMultiTimeframe) {
        UpdateMultiTimeframeDisplay();
    }

    // 更新趋势分析显示
    UpdateTrendAnalysisDisplay();
}

//+------------------------------------------------------------------+
//| 更新多时间框架显示                                                |
//+------------------------------------------------------------------+
void UpdateMultiTimeframeDisplay() {
    string h1Text = StringFormat("H1: %s", GetCompressionStateDescription(g_tfCompression.h1State));
    string h4Text = StringFormat("H4: %s", GetCompressionStateDescription(g_tfCompression.h4State));
    string d1Text = StringFormat("D1: %s", GetCompressionStateDescription(g_tfCompression.d1State));
    string w1Text = StringFormat("W1: %s", GetCompressionStateDescription(g_tfCompression.w1State));
    string mnText = StringFormat("MN: %s", GetCompressionStateDescription(g_tfCompression.mnState));
    string consistencyText = StringFormat("时间框架一致性: %.2f%%", g_tfCompression.consistencyScore);

    // 计算增强版爆发概率
    double breakoutProb = CalculateEnhancedBreakoutProbability(g_tfCompression, g_indicators, g_trendAnalysis);
    string breakoutText = StringFormat("爆发概率: %.2f%%", breakoutProb);

    // 更新标签文本
    ObjectSetString(0, H1_LABEL, OBJPROP_TEXT, h1Text);
    ObjectSetString(0, H4_LABEL, OBJPROP_TEXT, h4Text);
    ObjectSetString(0, D1_LABEL, OBJPROP_TEXT, d1Text);
    ObjectSetString(0, W1_LABEL, OBJPROP_TEXT, w1Text);
    ObjectSetString(0, MN_LABEL, OBJPROP_TEXT, mnText);
    ObjectSetString(0, CONSISTENCY_LABEL, OBJPROP_TEXT, consistencyText);
    ObjectSetString(0, BREAKOUT_PROB_LABEL, OBJPROP_TEXT, breakoutText);

    // 更新爆发概率度量表
    UpdateTechMeter(BREAKOUT_METER, breakoutProb);
}

//+------------------------------------------------------------------+
//| 更新趋势分析显示                                                  |
//+------------------------------------------------------------------+
void UpdateTrendAnalysisDisplay() {
    // 获取趋势方向和强度的描述
    string directionDesc = GetTrendDirectionDescription(g_trendAnalysis.direction);
    string strengthDesc = GetTrendStrengthDescription(g_trendAnalysis.strength);

    // 获取趋势颜色
    color trendColor = GetTrendColor(g_trendAnalysis.direction, g_trendAnalysis.strength);

    // 更新趋势标签
    ObjectSetString(0, TREND_DIRECTION_LABEL, OBJPROP_TEXT,
                   StringFormat("趋势方向: %s", directionDesc));
    ObjectSetInteger(0, TREND_DIRECTION_LABEL, OBJPROP_COLOR, trendColor);

    ObjectSetString(0, TREND_STRENGTH_LABEL, OBJPROP_TEXT,
                   StringFormat("趋势强度: %s", strengthDesc));
    ObjectSetInteger(0, TREND_STRENGTH_LABEL, OBJPROP_COLOR, trendColor);

    // 计算趋势强度（0-100）
    double trendStrength = MathAbs(g_trendAnalysis.score);

    ObjectSetString(0, TREND_SCORE_LABEL, OBJPROP_TEXT,
                   StringFormat("趋势评分: %.1f/100", trendStrength));

    // 更新趋势度量表
    UpdateTechMeter(TREND_METER, trendStrength);

    // 更新新增的趋势增强指标
    ObjectSetString(0, "TrendConsecutiveLabel", OBJPROP_TEXT,
                   StringFormat("连续Bar数: %d", g_trendAnalysis.consecutiveBars));

    ObjectSetString(0, "MomentumScoreLabel", OBJPROP_TEXT,
                   StringFormat("动量评分: %.2f", g_trendAnalysis.momentumScore));

    ObjectSetString(0, "VolumeConfirmLabel", OBJPROP_TEXT,
                   StringFormat("成交量确认: %s", g_trendAnalysis.volumeConfirmation ? "是" : "否"));
    ObjectSetInteger(0, "VolumeConfirmLabel", OBJPROP_COLOR,
                    g_trendAnalysis.volumeConfirmation ? clrLime : clrGray);

    ObjectSetString(0, "RetracementLabel", OBJPROP_TEXT,
                   StringFormat("回撤深度: %.2f%%", g_trendAnalysis.retracementDepth * 100));

    // 趋势质量显示
    string qualityText = "";
    color qualityColor = clrGray;
    switch(g_trendAnalysis.quality) {
        case VERY_STRONG_TREND_QUALITY:
            qualityText = "极强";
            qualityColor = clrLime;
            break;
        case STRONG_TREND_QUALITY:
            qualityText = "强";
            qualityColor = clrGreen;
            break;
        case MEDIUM_TREND_QUALITY:
            qualityText = "中等";
            qualityColor = clrYellow;
            break;
        case WEAK_TREND_QUALITY:
            qualityText = "弱";
            qualityColor = clrOrange;
            break;
        default:
            qualityText = "无";
            qualityColor = clrGray;
            break;
    }

    ObjectSetString(0, "TrendQualityLabel", OBJPROP_TEXT, "趋势质量: " + qualityText);
    ObjectSetInteger(0, "TrendQualityLabel", OBJPROP_COLOR, qualityColor);

    ObjectSetString(0, "FibRetracementLabel", OBJPROP_TEXT,
                   StringFormat("斐波回撤: %.3f", g_trendAnalysis.fibonacciRetrace));

    ObjectSetString(0, "MTFAlignLabel", OBJPROP_TEXT,
                   StringFormat("多框架一致: %s", g_trendAnalysis.multiTimeframeAlign ? "是" : "否"));
    ObjectSetInteger(0, "MTFAlignLabel", OBJPROP_COLOR,
                    g_trendAnalysis.multiTimeframeAlign ? clrLime : clrGray);

    ObjectSetString(0, "TrendFilterLabel", OBJPROP_TEXT,
                   StringFormat("智能过滤: %s", Enable_Advanced_Trend_Filter ? "开启" : "关闭"));
    ObjectSetInteger(0, "TrendFilterLabel", OBJPROP_COLOR,
                    Enable_Advanced_Trend_Filter ? clrLime : clrGray);
}

//+------------------------------------------------------------------+
//| 更新科技感功能面板                                                |
//+------------------------------------------------------------------+
void UpdateTechFunctionalPanels()
{
    UpdateTechAccountPanel();
    UpdateTechMarketPanel();
    UpdateTechTradingPanel();
    UpdateTechSystemPanel();
}

//+------------------------------------------------------------------+
//| 更新科技感账户面板                                                |
//+------------------------------------------------------------------+
void UpdateTechAccountPanel()
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double profit = AccountInfoDouble(ACCOUNT_PROFIT);
    double margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    double marginLevel = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);

    // 计算当日盈亏
    double dailyProfit = CalculateDailyProfit();

    // 更新账户信息
    ObjectSetString(0, "TechUI_Balance", OBJPROP_TEXT, StringFormat("余额: %.2f", balance));
    ObjectSetString(0, "TechUI_Equity", OBJPROP_TEXT, StringFormat("净值: %.2f", equity));
    ObjectSetString(0, "TechUI_Profit", OBJPROP_TEXT, StringFormat("浮盈: %.2f", profit));
    ObjectSetString(0, "TechUI_DailyProfit", OBJPROP_TEXT, StringFormat("日盈: %.2f", dailyProfit));
    ObjectSetString(0, "TechUI_Margin", OBJPROP_TEXT, StringFormat("保证金: %.2f", margin));
    ObjectSetString(0, "TechUI_FreeMargin", OBJPROP_TEXT, StringFormat("可用: %.2f", freeMargin));
    ObjectSetString(0, "TechUI_MarginLevel", OBJPROP_TEXT, StringFormat("保证金比例: %.2f%%", marginLevel));

    // 风险等级评估
    string riskLevel = "低";
    color riskColor = clrGreen;
    if(marginLevel < 100) {
        riskLevel = "极高";
        riskColor = clrRed;
    } else if(marginLevel < 200) {
        riskLevel = "高";
        riskColor = clrOrange;
    } else if(marginLevel < 500) {
        riskLevel = "中";
        riskColor = clrYellow;
    }

    ObjectSetString(0, "TechUI_RiskLevel", OBJPROP_TEXT, "风险等级: " + riskLevel);
    ObjectSetInteger(0, "TechUI_RiskLevel", OBJPROP_COLOR, riskColor);
}

//+------------------------------------------------------------------+
//| 计算当日盈亏                                                     |
//+------------------------------------------------------------------+
double CalculateDailyProfit()
{
    double dailyProfit = 0.0;
    datetime startOfDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    // 遍历历史订单计算当日盈亏
    if(HistorySelect(startOfDay, TimeCurrent())) {
        int total = HistoryDealsTotal();
        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                double dealProfit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double dealSwap = HistoryDealGetDouble(ticket, DEAL_SWAP);
                double dealCommission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                dailyProfit += dealProfit + dealSwap + dealCommission;
            }
        }
    }

    return dailyProfit;
}

//+------------------------------------------------------------------+
//| 更新科技感市场面板                                                |
//+------------------------------------------------------------------+
void UpdateTechMarketPanel()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double spread = (ask - bid) / SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    long volume = SymbolInfoInteger(_Symbol, SYMBOL_VOLUME);

    // 计算波动率
    double atr = g_indicatorManager.GetATR(14);
    double volatility = (atr / bid) * 100;

    // 计算趋势强度
    double trendStrength = MathAbs(g_trendAnalysis.score);

    // 获取服务器时间
    string serverTime = TimeToString(TimeCurrent(), TIME_SECONDS);

    // 获取交易时段
    string session = GetCurrentTradingSession();

    // 更新市场信息
    ObjectSetString(0, "TechUI_Symbol", OBJPROP_TEXT, "品种: " + _Symbol);
    ObjectSetString(0, "TechUI_Price", OBJPROP_TEXT, StringFormat("价格: %.5f", bid));
    ObjectSetString(0, "TechUI_Spread", OBJPROP_TEXT, StringFormat("点差: %.1f", spread));
    ObjectSetString(0, "TechUI_Volume", OBJPROP_TEXT, StringFormat("成交量: %d", volume));
    ObjectSetString(0, "TechUI_Volatility", OBJPROP_TEXT, StringFormat("波动率: %.2f%%", volatility));
    ObjectSetString(0, "TechUI_TrendStrength", OBJPROP_TEXT, StringFormat("趋势强度: %.1f", trendStrength));
    ObjectSetString(0, "TechUI_ServerTime", OBJPROP_TEXT, "服务器时间: " + serverTime);
    ObjectSetString(0, "TechUI_MarketSession", OBJPROP_TEXT, "交易时段: " + session);
}

//+------------------------------------------------------------------+
//| 获取当前交易时段                                                  |
//+------------------------------------------------------------------+
string GetCurrentTradingSession()
{
    MqlDateTime tm;
    TimeCurrent(tm);
    int hour = tm.hour;

    if(hour >= 1 && hour < 8) return "亚洲时段";
    if(hour >= 8 && hour < 16) return "伦敦时段";
    if(hour >= 14 && hour < 22) return "纽约时段";
    if(hour >= 13 && hour < 17) return "重叠时段";

    return "休市时段";
}

//+------------------------------------------------------------------+
//| 更新科技感交易面板                                                |
//+------------------------------------------------------------------+
void UpdateTechTradingPanel()
{
    // 获取持仓统计
    PositionStats stats = GetPositionStats();

    // 计算胜率和其他统计
    double winRate = CalculateWinRate();
    double maxDrawdown = CalculateMaxDrawdown();
    double profitFactor = CalculateProfitFactor();

    // 评估信号质量
    string signalQuality = EvaluateSignalQuality();
    string lastSignal = GetLastSignalInfo();

    // 更新交易统计
    ObjectSetString(0, "TechUI_Positions", OBJPROP_TEXT, StringFormat("持仓数: %d", stats.totalPositions));
    ObjectSetString(0, "TechUI_TotalVolume", OBJPROP_TEXT, StringFormat("总手数: %.2f", stats.totalVolume));
    ObjectSetString(0, "TechUI_LongVolume", OBJPROP_TEXT, StringFormat("多单: %.2f", stats.longVolume));
    ObjectSetString(0, "TechUI_ShortVolume", OBJPROP_TEXT, StringFormat("空单: %.2f", stats.shortVolume));
    ObjectSetString(0, "TechUI_TodayTrades", OBJPROP_TEXT, StringFormat("今日交易: %d", stats.todayTrades));
    ObjectSetString(0, "TechUI_WinRate", OBJPROP_TEXT, StringFormat("胜率: %.2f%%", winRate));
    // 更新盈亏统计数据
    ObjectSetString(0, "TechUI_HistoryProfit", OBJPROP_TEXT, StringFormat("历史盈亏: %.2f", CalculateHistoryProfit()));
    ObjectSetString(0, "TechUI_CurrentProfit", OBJPROP_TEXT, StringFormat("当前盈亏: %.2f", CalculateCurrentProfit()));
    
    ObjectSetString(0, "TechUI_MaxDrawdown", OBJPROP_TEXT, StringFormat("最大回撤: %.2f%%", maxDrawdown));
    ObjectSetString(0, "TechUI_ProfitFactor", OBJPROP_TEXT, StringFormat("盈利因子: %.2f", profitFactor));
    ObjectSetString(0, "TechUI_SignalQuality", OBJPROP_TEXT, "信号质量: " + signalQuality);
    ObjectSetString(0, "TechUI_LastSignal", OBJPROP_TEXT, "最后信号: " + lastSignal);

    // 风险等级评估
    string riskLevel = "低";
    color riskColor = clrGreen;
    if(stats.totalPositions > 10) {
        riskLevel = "高";
        riskColor = clrRed;
    } else if(stats.totalPositions > 5) {
        riskLevel = "中";
        riskColor = clrYellow;
    }

    ObjectSetString(0, "TechUI_RiskLevel", OBJPROP_TEXT, "风险等级: " + riskLevel);
    ObjectSetInteger(0, "TechUI_RiskLevel", OBJPROP_COLOR, riskColor);
}

//+------------------------------------------------------------------+
//| 持仓统计结构                                                      |
//+------------------------------------------------------------------+
struct PositionStats {
    int totalPositions;
    double totalVolume;
    double longVolume;
    double shortVolume;
    int todayTrades;
};

//+------------------------------------------------------------------+
//| 获取持仓统计                                                      |
//+------------------------------------------------------------------+
PositionStats GetPositionStats()
{
    PositionStats stats;
    stats.totalPositions = 0;
    stats.totalVolume = 0.0;
    stats.longVolume = 0.0;
    stats.shortVolume = 0.0;
    stats.todayTrades = 0;

    // 统计当前持仓
    for(int i = 0; i < PositionsTotal(); i++) {
        string symbol = PositionGetSymbol(i);
        if(symbol == _Symbol) {
            if(PositionSelectByTicket(PositionGetTicket(i))) {
                stats.totalPositions++;
                double volume = PositionGetDouble(POSITION_VOLUME);
                stats.totalVolume += volume;

                if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                    stats.longVolume += volume;
                } else {
                    stats.shortVolume += volume;
                }
            }
        }
    }

    // 统计今日交易
    datetime startOfDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    if(HistorySelect(startOfDay, TimeCurrent())) {
        int total = HistoryDealsTotal();
        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol) {
                    ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
                    if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL) {
                        stats.todayTrades++;
                    }
                }
            }
        }
    }

    return stats;
}

//+------------------------------------------------------------------+
//| 计算胜率                                                          |
//+------------------------------------------------------------------+
double CalculateWinRate()
{
    int totalTrades = 0;
    int winningTrades = 0;

    datetime startDate = TimeCurrent() - 30 * 24 * 3600; // 过去30天
    if(HistorySelect(startDate, TimeCurrent())) {
        int total = HistoryDealsTotal();
        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol) {
                    double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                    if(profit != 0) {
                        totalTrades++;
                        if(profit > 0) winningTrades++;
                    }
                }
            }
        }
    }

    return totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0.0;
}

//+------------------------------------------------------------------+
//| 计算历史盈亏(相同魔术号)                                          |
//+------------------------------------------------------------------+
double CalculateHistoryProfit()
{
    double totalProfit = 0.0;
    if(HistorySelectByMagicNumber(MagicNumber))
    {
        int total = HistoryDealsTotal();
        for(int i = 0; i < total; i++)
        {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0 && HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol)
            {
                totalProfit += HistoryDealGetDouble(ticket, DEAL_PROFIT);
            }
        }
    }
    return totalProfit;
}

//+------------------------------------------------------------------+
//| 计算当前持仓盈亏(相同魔术号)                                       |
//+------------------------------------------------------------------+
double CalculateCurrentProfit()
{
    double currentProfit = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) && PositionGetString(POSITION_SYMBOL) == _Symbol)
        {
            if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                currentProfit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }
    return currentProfit;
}

//+------------------------------------------------------------------+
//| 计算最大回撤                                                      |
double CalculateMaxDrawdown()
{
    // 简化版最大回撤计算
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double currentDrawdown = (balance - equity) / balance * 100;

    return MathMax(0, currentDrawdown);
}

//+------------------------------------------------------------------+
//| 计算盈利因子                                                      |
//+------------------------------------------------------------------+
double CalculateProfitFactor()
{
    double grossProfit = 0.0;
    double grossLoss = 0.0;

    datetime startDate = TimeCurrent() - 30 * 24 * 3600; // 过去30天
    if(HistorySelect(startDate, TimeCurrent())) {
        int total = HistoryDealsTotal();
        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol) {
                    double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                    if(profit > 0) grossProfit += profit;
                    else if(profit < 0) grossLoss += MathAbs(profit);
                }
            }
        }
    }

    return grossLoss > 0 ? grossProfit / grossLoss : 0.0;
}

//+------------------------------------------------------------------+
//| 评估信号质量                                                      |
//+------------------------------------------------------------------+
string EvaluateSignalQuality()
{
    // 基于压缩分数和趋势质量评估信号质量
    double compressionScore = (g_indicators.volatility * 0.25 +
                             g_indicators.bbWidth * 0.25 +
                             g_indicators.atrValue * 0.20 +
                             g_indicators.volumeCompress * 0.20);

    if(compressionScore >= 80 && g_trendAnalysis.quality >= STRONG_TREND_QUALITY) {
        return "优秀";
    } else if(compressionScore >= 60 && g_trendAnalysis.quality >= MEDIUM_TREND_QUALITY) {
        return "良好";
    } else if(compressionScore >= 40) {
        return "一般";
    } else {
        return "较差";
    }
}

//+------------------------------------------------------------------+
//| 获取最后信号信息                                                  |
//+------------------------------------------------------------------+
string GetLastSignalInfo()
{
    // 简化版最后信号信息
    COMPRESSION_STATE state = EvaluateCompression(g_indicators);
    if(state >= MEDIUM_COMPRESSION) {
        return StringFormat("压缩信号 %s", TimeToString(TimeCurrent(), TIME_MINUTES));
    } else {
        return "无信号";
    }
}

//+------------------------------------------------------------------+
//| 更新科技感系统面板                                                |
//+------------------------------------------------------------------+
void UpdateTechSystemPanel()
{
    bool connected = TerminalInfoInteger(TERMINAL_CONNECTED);
    string connectionStatus = connected ? "正常" : "断开";
    color connectionColor = connected ? clrGreen : clrRed;

    // 模拟延迟计算
    int ping = connected ? MathRand() % 50 + 10 : 0;

    string account = IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN));
    string server = AccountInfoString(ACCOUNT_SERVER);
    string version = "MT5 Build " + IntegerToString(TerminalInfoInteger(TERMINAL_BUILD));

    // 更新系统状态
    ObjectSetString(0, "TechUI_Connection", OBJPROP_TEXT, "连接: " + connectionStatus);
    ObjectSetInteger(0, "TechUI_Connection", OBJPROP_COLOR, connectionColor);
    ObjectSetString(0, "TechUI_Ping", OBJPROP_TEXT, StringFormat("延迟: %dms", ping));
    ObjectSetString(0, "TechUI_Account", OBJPROP_TEXT, "账户: " + account);
    ObjectSetString(0, "TechUI_Server", OBJPROP_TEXT, "服务器: " + server);
    ObjectSetString(0, "TechUI_Version", OBJPROP_TEXT, "版本: " + version);
}

//+------------------------------------------------------------------+
//| 更新市场状态显示                                                  |
//+------------------------------------------------------------------+
void UpdateMarketStateDisplay() {
    string stateText = "";
    color stateColor = clrGray;

    switch(g_CurrentMarketState) {
        case MARKET_TRENDING:
            stateText = "趋势市场";
            stateColor = clrBlue;
            break;
        case MARKET_RANGING:
            stateText = "震荡市场";
            stateColor = clrGreen;
            break;
        case MARKET_LOW_VOL:
            stateText = "低波动市场";
            stateColor = clrGold;
            break;
        case MARKET_HIGH_VOL:
            stateText = "高波动市场";
            stateColor = clrOrangeRed;
            break;
        case MARKET_EVENT_DRIVEN:
            stateText = "事件驱动市场";
            stateColor = clrRed;
            break;
        default:
            stateText = "未知状态";
    }

    // 更新市场状态标签
    ObjectSetString(0, MARKET_STATE_LABEL, OBJPROP_TEXT, "市场状态: " + stateText);
    ObjectSetInteger(0, MARKET_STATE_LABEL, OBJPROP_COLOR, stateColor);
}

//+------------------------------------------------------------------+
//| 更新科技感度量表                                                  |
//+------------------------------------------------------------------+
void UpdateTechMeter(string meterName, double value, double minValue = 0.0, double maxValue = 100.0) {
    // 计算标准化值
    double normalizedValue = (value - minValue) / (maxValue - minValue) * 100.0;
    normalizedValue = MathMax(0.0, MathMin(100.0, normalizedValue));

    // 智能颜色分配
    color meterColor;
    if(StringFind(meterName, "Trend") >= 0) {
        // 趋势度量表颜色方案
        if(normalizedValue < 30) {
            meterColor = clrGreen;
        } else if(normalizedValue < 45) {
            meterColor = clrLime;
        } else if(normalizedValue < 60) {
            meterColor = clrYellowGreen;
        } else if(normalizedValue < 70) {
            meterColor = clrGold;
        } else if(normalizedValue < 85) {
            meterColor = clrOrange;
        } else if(normalizedValue < 95) {
            meterColor = clrTomato;
        } else {
            meterColor = clrRed;
        }
    } else {
        // 压缩度量表颜色方案
        if(normalizedValue < 30) {
            meterColor = C'65,105,225'; // 皇家蓝
        } else if(normalizedValue < 45) {
            meterColor = C'64,224,208'; // 绿松石
        } else if(normalizedValue < 60) {
            meterColor = C'50,205,50'; // 酸橙绿
        } else if(normalizedValue < 75) {
            meterColor = C'255,215,0'; // 金色
        } else if(normalizedValue < 85) {
            meterColor = C'255,140,0'; // 深橙色
        } else if(normalizedValue < 95) {
            meterColor = C'255,69,0'; // 红橙色
        } else {
            meterColor = C'178,34,34'; // 火砖红
        }
    }

    // 创建填充矩形
    string fillName = meterName + "_Fill";
    if(ObjectFind(0, fillName) < 0) {
        ObjectCreate(0, fillName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    }

    // 设置填充矩形属性
    ObjectSetInteger(0, fillName, OBJPROP_XDISTANCE, 25);
    ObjectSetInteger(0, fillName, OBJPROP_YDISTANCE, ObjectGetInteger(0, meterName, OBJPROP_YDISTANCE));
    ObjectSetInteger(0, fillName, OBJPROP_XSIZE, (int)(340 * normalizedValue / 100));
    ObjectSetInteger(0, fillName, OBJPROP_YSIZE, 15);
    ObjectSetInteger(0, fillName, OBJPROP_BGCOLOR, meterColor);
    ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, fillName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, fillName, OBJPROP_SELECTABLE, false);

    // 添加光晕效果
    if(EnableGlowEffect && normalizedValue > 70) {
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_COLOR, clrWhite);
        ObjectSetInteger(0, fillName, OBJPROP_WIDTH, 2);
    }
}

//+------------------------------------------------------------------+
//| 更新科技感状态指示器                                              |
//+------------------------------------------------------------------+
void UpdateTechStatusIndicator()
{
    bool connected = TerminalInfoInteger(TERMINAL_CONNECTED);
    color indicatorColor = connected ? SecondaryColor : AccentColor;

    ObjectSetInteger(0, "TechUI_StatusLED", OBJPROP_BGCOLOR, indicatorColor);

    if(EnableGlowEffect)
    {
        ObjectSetInteger(0, "TechUI_StatusLED", OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, "TechUI_StatusLED", OBJPROP_BORDER_COLOR, clrWhite);
    }
}

//+------------------------------------------------------------------+
//| 更新科技感进度条                                                  |
//+------------------------------------------------------------------+
void UpdateTechProgressBar()
{
    // 基于保证金比例计算进度
    double marginLevel = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
    double progress = MathMin(marginLevel / 1000.0, 1.0); // 最大1000%

    int fillWidth = (int)(340 * progress);
    ObjectSetInteger(0, "TechUI_ProgressBar_Fill", OBJPROP_XSIZE, fillWidth);

    // 根据风险等级改变颜色
    color fillColor = SecondaryColor;
    if(marginLevel < 100) fillColor = clrRed;
    else if(marginLevel < 200) fillColor = AccentColor;
    else if(marginLevel < 500) fillColor = clrYellow;

    ObjectSetInteger(0, "TechUI_ProgressBar_Fill", OBJPROP_BGCOLOR, fillColor);

    if(EnableGlowEffect)
    {
        ObjectSetInteger(0, "TechUI_ProgressBar_Fill", OBJPROP_WIDTH, 2);
    }
}


//+------------------------------------------------------------------+
//| 清理整合版UI界面                                                  |
//+------------------------------------------------------------------+
void CleanupIntegratedUI()
{
    // === 清理信号检测区域 ===
    // 清理第一区域：基础压缩指标
    ObjectDelete(0, VOLATILITY_LABEL);
    ObjectDelete(0, BB_WIDTH_LABEL);
    ObjectDelete(0, ATR_LABEL);
    ObjectDelete(0, VOLUME_LABEL);
    ObjectDelete(0, PATTERN_LABEL);
    ObjectDelete(0, STATE_LABEL);
    ObjectDelete(0, COMPRESSION_METER);
    ObjectDelete(0, COMPRESSION_METER + "_Fill");
    ObjectDelete(0, COMPRESSION_METER + "_BG");

    // 清理第二区域：多时间框架分析
    if(EnableMultiTimeframe) {
        ObjectDelete(0, H1_LABEL);
        ObjectDelete(0, H4_LABEL);
        ObjectDelete(0, D1_LABEL);
        ObjectDelete(0, W1_LABEL);
        ObjectDelete(0, MN_LABEL);
        ObjectDelete(0, CONSISTENCY_LABEL);
        ObjectDelete(0, BREAKOUT_PROB_LABEL);
        ObjectDelete(0, BREAKOUT_METER);
        ObjectDelete(0, BREAKOUT_METER + "_Fill");
        ObjectDelete(0, BREAKOUT_METER + "_BG");
    }

    // 清理第三区域：趋势分析
    ObjectDelete(0, TREND_DIRECTION_LABEL);
    ObjectDelete(0, TREND_STRENGTH_LABEL);
    ObjectDelete(0, TREND_SCORE_LABEL);
    ObjectDelete(0, TREND_METER);
    ObjectDelete(0, TREND_METER + "_Fill");
    ObjectDelete(0, TREND_METER + "_BG");
    ObjectDelete(0, EXPECTED_DIRECTION_LABEL);
    ObjectDelete(0, "TrendConsecutiveLabel");
    ObjectDelete(0, "MomentumScoreLabel");
    ObjectDelete(0, "VolumeConfirmLabel");
    ObjectDelete(0, "RetracementLabel");
    ObjectDelete(0, "TrendQualityLabel");
    ObjectDelete(0, "FibRetracementLabel");
    ObjectDelete(0, "MTFAlignLabel");
    ObjectDelete(0, "TrendFilterLabel");
    ObjectDelete(0, MARKET_STATE_LABEL);

    // === 清理科技感功能面板 ===
    // 清理账户面板
    ObjectDelete(0, ACCOUNT_PANEL);
    ObjectDelete(0, "TechUI_AccountTitle");
    ObjectDelete(0, "TechUI_Balance");
    ObjectDelete(0, "TechUI_Equity");
    ObjectDelete(0, "TechUI_Profit");
    ObjectDelete(0, "TechUI_DailyProfit");
    ObjectDelete(0, "TechUI_Margin");
    ObjectDelete(0, "TechUI_FreeMargin");
    ObjectDelete(0, "TechUI_MarginLevel");
    ObjectDelete(0, "TechUI_RiskLevel");

    // 清理市场面板
    ObjectDelete(0, MARKET_PANEL);
    ObjectDelete(0, "TechUI_MarketTitle");
    ObjectDelete(0, "TechUI_Symbol");
    ObjectDelete(0, "TechUI_Price");
    ObjectDelete(0, "TechUI_Spread");
    ObjectDelete(0, "TechUI_Volume");
    ObjectDelete(0, "TechUI_Volatility");
    ObjectDelete(0, "TechUI_TrendStrength");
    ObjectDelete(0, "TechUI_ServerTime");
    ObjectDelete(0, "TechUI_MarketSession");

    // 清理交易面板
    ObjectDelete(0, TRADING_PANEL);
    ObjectDelete(0, "TechUI_TradingTitle");
    ObjectDelete(0, "TechUI_Positions");
    ObjectDelete(0, "TechUI_TotalVolume");
    ObjectDelete(0, "TechUI_LongVolume");
    ObjectDelete(0, "TechUI_ShortVolume");
    ObjectDelete(0, "TechUI_TodayTrades");
    ObjectDelete(0, "TechUI_WinRate");
    ObjectDelete(0, "TechUI_MaxDrawdown");
    ObjectDelete(0, "TechUI_ProfitFactor");
    ObjectDelete(0, "TechUI_SignalQuality");
    ObjectDelete(0, "TechUI_LastSignal");

    // 清理系统面板
    ObjectDelete(0, SYSTEM_PANEL);
    ObjectDelete(0, "TechUI_SystemTitle");
    ObjectDelete(0, "TechUI_Connection");
    ObjectDelete(0, "TechUI_Ping");
    ObjectDelete(0, "TechUI_Account");
    ObjectDelete(0, "TechUI_Server");
    ObjectDelete(0, "TechUI_Version");

    // === 清理装饰元素 ===
    ObjectDelete(0, "TechUI_StatusLED");
    ObjectDelete(0, "TechUI_ProgressBar_BG");
    ObjectDelete(0, "TechUI_ProgressBar_Fill");

    // 清理分隔线
    ObjectDelete(0, "TechUI_Sep1");
    ObjectDelete(0, "TechUI_Sep2");
    ObjectDelete(0, "TechUI_Sep3");
    ObjectDelete(0, "TechUI_Sep4");
    ObjectDelete(0, "TechUI_Sep5");
    ObjectDelete(0, "TechUI_Sep6");

    // 清理装饰点
    for(int i = 0; i < 4; i++) {
        ObjectDelete(0, "TechUI_Dot" + IntegerToString(i));
    }

    // === 清理度量表阈值标记 ===
    CleanupTechThresholdMarks(COMPRESSION_METER);
    CleanupTechThresholdMarks(BREAKOUT_METER);
    CleanupTechThresholdMarks(TREND_METER);

    // === 清理区域背景框 ===
    ObjectDelete(0, "Zone1_BG");
    ObjectDelete(0, "Zone2_BG");
    ObjectDelete(0, "Zone3_BG");
    ObjectDelete(0, "Zone1_Title");
    ObjectDelete(0, "Zone2_Title");
    ObjectDelete(0, "Zone3_Title");

    // === 清理主面板 ===
    ObjectDelete(0, MAIN_PANEL);
    ObjectDelete(0, "TechUI_Title");

    // 强制刷新图表
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 清理科技感阈值标记                                                |
//+------------------------------------------------------------------+
void CleanupTechThresholdMarks(string meterName) {
    int thresholds[6];
    thresholds[0] = 30; thresholds[1] = 45; thresholds[2] = 60;
    thresholds[3] = 70; thresholds[4] = 85; thresholds[5] = 95;

    for(int i = 0; i < 6; i++) {
        string markName = meterName + "_Mark" + IntegerToString(thresholds[i]);
        string labelName = meterName + "_Label" + IntegerToString(thresholds[i]);

        ObjectDelete(0, markName);
        ObjectDelete(0, labelName);
    }
}

//+------------------------------------------------------------------+
//| 分析压缩指标                                                      |
//+------------------------------------------------------------------+
CompressionIndicators AnalyzeCompression(int volPeriod, int bbPeriod, double bbStdDev, int atrPeriod, int volumePeriod) {
    CompressionIndicators indicators;

    // 简化版压缩分析
    double atr = g_indicatorManager.GetATR(atrPeriod);
    double atr_ma = 0.0;

    // 获取ATR均值进行比较
    int maHandle = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_TYPICAL);
    if(maHandle != INVALID_HANDLE) {
        double ma[];
        ArraySetAsSeries(ma, true);
        if(CopyBuffer(maHandle, 0, 0, 1, ma) > 0) {
            atr_ma = ma[0];
        }
        IndicatorRelease(maHandle);
    }

    // 计算压缩百分比
    indicators.volatility = (atr_ma > 0) ? (1.0 - atr / atr_ma) * 100 : 0.0;
    indicators.bbWidth = MathRand() % 100; // 简化版，实际应该计算布林带宽度
    indicators.atrValue = indicators.volatility;
    indicators.volumeCompress = MathRand() % 100; // 简化版
    indicators.candlePattern = "Doji"; // 简化版

    return indicators;
}

//+------------------------------------------------------------------+
//| 分析多时间框架                                                    |
//+------------------------------------------------------------------+
TimeframeCompression AnalyzeTimeframes(int volPeriod, int bbPeriod, double bbStdDev, int atrPeriod, int volumePeriod) {
    TimeframeCompression tf;

    // 简化版多时间框架分析
    tf.h1State = (COMPRESSION_STATE)(MathRand() % 5);
    tf.h4State = (COMPRESSION_STATE)(MathRand() % 5);
    tf.d1State = (COMPRESSION_STATE)(MathRand() % 5);
    tf.w1State = (COMPRESSION_STATE)(MathRand() % 5);
    tf.mnState = (COMPRESSION_STATE)(MathRand() % 5);
    tf.consistencyScore = MathRand() % 100;

    return tf;
}

//+------------------------------------------------------------------+
//| 分析趋势                                                          |
//+------------------------------------------------------------------+
TrendAnalysis AnalyzeTrend(int period) {
    TrendAnalysis trend;

    // 简化版趋势分析
    double adx = g_indicatorManager.GetADX(14);

    trend.direction = (MathRand() % 3) - 1; // -1, 0, 1
    trend.strength = adx;
    trend.score = adx;
    trend.quality = (adx > 25) ? STRONG_TREND_QUALITY : WEAK_TREND_QUALITY;
    trend.consecutiveBars = MathRand() % 10;
    trend.momentumScore = MathRand() % 100;
    trend.volumeConfirmation = (MathRand() % 2) == 1;
    trend.retracementDepth = (MathRand() % 50) / 100.0;
    trend.fibonacciRetrace = 0.618;
    trend.multiTimeframeAlign = (MathRand() % 2) == 1;

    return trend;
}

//+------------------------------------------------------------------+
//| 评估压缩状态                                                      |
//+------------------------------------------------------------------+
COMPRESSION_STATE EvaluateCompression(const CompressionIndicators &indicators) {
    double avgCompression = (indicators.volatility + indicators.bbWidth + indicators.atrValue + indicators.volumeCompress) / 4.0;

    if(avgCompression >= 80) return EXTREME_COMPRESSION;
    if(avgCompression >= 60) return HEAVY_COMPRESSION;
    if(avgCompression >= 40) return MEDIUM_COMPRESSION;
    if(avgCompression >= 20) return LIGHT_COMPRESSION;
    return NO_COMPRESSION;
}

//+------------------------------------------------------------------+
//| 获取压缩状态描述                                                  |
//+------------------------------------------------------------------+
string GetCompressionStateDescription(COMPRESSION_STATE state) {
    switch(state) {
        case EXTREME_COMPRESSION: return "极度压缩";
        case HEAVY_COMPRESSION:   return "重度压缩";
        case MEDIUM_COMPRESSION:  return "中度压缩";
        case LIGHT_COMPRESSION:   return "轻度压缩";
        default:                  return "无压缩";
    }
}

//+------------------------------------------------------------------+
//| 计算增强版爆发概率                                                |
//+------------------------------------------------------------------+
double CalculateEnhancedBreakoutProbability(const TimeframeCompression &tf, const CompressionIndicators &indicators, const TrendAnalysis &trend) {
    // 简化版爆发概率计算
    double baseProb = (indicators.volatility + indicators.bbWidth + indicators.atrValue) / 3.0;
    double trendBonus = (trend.quality >= MEDIUM_TREND_QUALITY) ? 20.0 : 0.0;
    double tfBonus = tf.consistencyScore * 0.3;

    return MathMin(100.0, baseProb + trendBonus + tfBonus);
}

//+------------------------------------------------------------------+
//| 获取趋势方向描述                                                  |
//+------------------------------------------------------------------+
string GetTrendDirectionDescription(int direction) {
    switch(direction) {
        case 1:  return "上升";
        case -1: return "下降";
        default: return "中性";
    }
}

//+------------------------------------------------------------------+
//| 获取趋势强度描述                                                  |
//+------------------------------------------------------------------+
string GetTrendStrengthDescription(double strength) {
    if(strength >= 75) return "极强";
    if(strength >= 50) return "强";
    if(strength >= 25) return "中等";
    return "弱";
}

//+------------------------------------------------------------------+
//| 获取趋势颜色                                                      |
//+------------------------------------------------------------------+
color GetTrendColor(int direction, double strength) {
    if(direction > 0) {
        if(strength >= 50) return clrLime;
        return clrGreen;
    } else if(direction < 0) {
        if(strength >= 50) return clrRed;
        return clrOrange;
    }
    return clrGray;
}
