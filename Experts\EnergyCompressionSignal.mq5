//+------------------------------------------------------------------+
//|                                             EnergyCompressionSignal.mq5 |
//|                      H4极度收缩信号检测EA（独立调试版）              |
//+------------------------------------------------------------------+
#property copyright   "AI助理"
#property version     "1.0"
#property strict
#property description "H4极度收缩信号检测与美观UI展示，便于参数优化和调试"

//==== 可调参数区 ====
input int    ATR_Period = 14;                // ATR周期
input int    ATR_History = 100;              // ATR历史均值长度
input int    BW_Period = 20;                 // 布林带周期
input double BW_StdDev = 2.0;                // 布林带标准差
input int    BW_History = 250;               // 布林带宽度历史长度
input int    ADX_Period = 14;                // ADX周期
input double ADX_Threshold = 18;             // ADX极低阈值
input double MA_Entangle_Threshold = 0.005;  // 均线缠绕阈值（0.5%）
input int    MA_Periods_1 = 5;
input int    MA_Periods_2 = 10;
input int    MA_Periods_3 = 20;
input int    MA_Periods_4 = 30;
input int    MA_Periods_5 = 60;
input int    Daily_Range_Days = 7;           // 日线收敛区间天数
input int    Daily_Range_History = 200;      // 日线收敛历史长度
input double Daily_Range_Threshold = 0.4;    // 日线收敛阈值（40%）
input int    H4_Body_Avg_N = 5;              // H4实体均值根数
input double H4_Body_Threshold = 0.3;        // H4实体极小阈值（ATR比例）
input int    UI_X = 10;                      // UI横向偏移
input int    UI_Y = 20;                      // UI纵向偏移
input int    UI_FontSize = 14;               // UI字体大小
input color  UI_Color_Signal = clrRed;       // 信号成立颜色
input color  UI_Color_Normal = clrGray;      // 信号未成立颜色
input color  UI_Color_BG = clrWhite;         // 背景色
input color  UI_Color_Title = clrBlue;       // 标题色

//==== 全局变量 ====
string labelName = "EnergyCompressionLabel";
string bgName = "EnergyCompressionBG";
static bool last_signal_state = false; // 用于跟踪上一次信号状态，避免不必要的UI闪烁

//==== 全局信号缓存变量 ====
double g_H4_ATR_14, g_H4_ATR_Mean_100;
bool   g_H4_ATR_VeryLow;
double g_H4_BW, g_H4_BW_10pct;
bool   g_H4_BW_VeryLow;
double g_h4_adx;
bool   g_H4_ADX_VeryLow;
double g_H4_MAsRangePct;
bool   g_H4_MAs_Entangled;
double g_Daily_Range_7, g_Daily_Typical_Range7_Mean;
bool   g_Daily_Range_Converge;
bool   g_H4_Bars_VerySmall;
double g_H4_AvgBodySize_Last_5;
bool   g_Major_News_Upcoming_Within_48Hours;
bool   g_Core_Signal;

//==== 指标检测主函数 ====
bool DetectEnergyCompression()
{
    int MA_Periods[5] = {MA_Periods_1, MA_Periods_2, MA_Periods_3, MA_Periods_4, MA_Periods_5};
    int max_h4_needed = MathMax(MathMax(BW_History, ATR_History), 60);
    int max_d1_needed = MathMax(Daily_Range_History, Daily_Range_Days);
    MqlRates h4_rates[], d1_rates[];
    int h4_bars = CopyRates(_Symbol, PERIOD_H4, 0, max_h4_needed, h4_rates);
    int d1_bars = CopyRates(_Symbol, PERIOD_D1, 0, max_d1_needed, d1_rates);
    if(h4_bars<=0 || d1_bars<=0) { return false; }

    // 2. 计算H4 ATR
    int h4_atr_handle = iATR(_Symbol, PERIOD_H4, ATR_Period);
    double h4_atr[]; ArraySetAsSeries(h4_atr,true);
    CopyBuffer(h4_atr_handle, 0, 0, ATR_History+1, h4_atr);
    g_H4_ATR_14 = h4_atr[0];
    g_H4_ATR_Mean_100 = 0;
    for(int i=1;i<=ATR_History;i++) g_H4_ATR_Mean_100+=h4_atr[i];
    g_H4_ATR_Mean_100/=ATR_History;
    g_H4_ATR_VeryLow = g_H4_ATR_14 < g_H4_ATR_Mean_100 * 0.5;

    // 3. 计算H4布林带宽度
    int h4_bands = iBands(_Symbol, PERIOD_H4, BW_Period, 0, BW_StdDev, PRICE_CLOSE);
    double h4_upper[], h4_middle[], h4_lower[];
    ArraySetAsSeries(h4_upper,true); ArraySetAsSeries(h4_middle,true); ArraySetAsSeries(h4_lower,true);
    CopyBuffer(h4_bands, 0, 0, BW_History+1, h4_upper);
    CopyBuffer(h4_bands, 1, 0, BW_History+1, h4_middle);
    CopyBuffer(h4_bands, 2, 0, BW_History+1, h4_lower);
    g_H4_BW = (h4_upper[0] - h4_lower[0]) / h4_middle[0];
    double h4_bw_history[];
    ArrayResize(h4_bw_history, BW_History);
    for(int i=1;i<=BW_History;i++) h4_bw_history[i-1]=(h4_upper[i]-h4_lower[i])/h4_middle[i];
    ArraySort(h4_bw_history);
    g_H4_BW_10pct = h4_bw_history[int(BW_History*0.1)];
    g_H4_BW_VeryLow = g_H4_BW < g_H4_BW_10pct;

    // 4. 计算H4 ADX
    int h4_adx_handle = iADX(_Symbol, PERIOD_H4, ADX_Period);
    double h4_adx[]; ArraySetAsSeries(h4_adx,true);
    CopyBuffer(h4_adx_handle, 0, 0, 2, h4_adx);
    g_h4_adx = h4_adx[0];
    g_H4_ADX_VeryLow = g_h4_adx < ADX_Threshold;

    // 5. 计算H4均线缠绕
    double mas[5];
    for(int i=0;i<5;i++) {
        int ma_handle = iMA(_Symbol, PERIOD_H4, MA_Periods[i], 0, MODE_SMA, PRICE_CLOSE);
        double ma_buf[]; ArraySetAsSeries(ma_buf,true);
        CopyBuffer(ma_handle, 0, 0, 1, ma_buf);
        mas[i] = ma_buf[0];
    }
    double ma_max=mas[0],ma_min=mas[0];
    for(int i=1;i<5;i++) { if(mas[i]>ma_max) ma_max=mas[i]; if(mas[i]<ma_min) ma_min=mas[i]; }
    g_H4_MAsRangePct = (ma_max-ma_min)/h4_rates[0].close;
    g_H4_MAs_Entangled = g_H4_MAsRangePct < MA_Entangle_Threshold;

    // 6. 日线区间收敛
    double daily_high7=d1_rates[0].high, daily_low7=d1_rates[0].low;
    for(int i=1;i<Daily_Range_Days;i++) {
        if(d1_rates[i].high>daily_high7) daily_high7=d1_rates[i].high;
        if(d1_rates[i].low<daily_low7) daily_low7=d1_rates[i].low;
    }
    g_Daily_Range_7 = daily_high7 - daily_low7;
    double daily_atr1_sum=0;
    for(int i=0;i<Daily_Range_History;i++)
        daily_atr1_sum+=d1_rates[i].high-d1_rates[i].low;
    g_Daily_Typical_Range7_Mean = (daily_atr1_sum/Daily_Range_History) * Daily_Range_Days;
    g_Daily_Range_Converge = g_Daily_Range_7 < g_Daily_Typical_Range7_Mean * Daily_Range_Threshold;

    // 7. H4 K线实体极小
    double body_sum=0;
    for(int i=0;i<H4_Body_Avg_N;i++)
        body_sum+=MathAbs(h4_rates[i].close-h4_rates[i].open);
    g_H4_AvgBodySize_Last_5 = body_sum/H4_Body_Avg_N;
    g_H4_Bars_VerySmall = g_H4_AvgBodySize_Last_5 < g_H4_ATR_14 * H4_Body_Threshold;

    // 8. 重大新闻检测（占位）
    g_Major_News_Upcoming_Within_48Hours = false;

    // 9. 核心信号判定
    g_Core_Signal = (g_H4_ATR_VeryLow || g_H4_BW_VeryLow) && (g_H4_ADX_VeryLow || g_H4_MAs_Entangled) && g_Daily_Range_Converge;

    return g_Core_Signal;
}

//+------------------------------------------------------------------+
//| 美观UI显示函数（多行分行版）                                     |
//+------------------------------------------------------------------+
void ShowEnergyCompressionSignal()
{
    // 不再需要每次都调用DetectEnergyCompression()，由OnTimer控制调用时机

    string info_lines[24];
    int line_count = 0;
    info_lines[line_count++] = "【H4极度收缩信号检测】";
    info_lines[line_count++] = "-----------------------------";
    info_lines[line_count++] = StringFormat("ATR:        %.5f", g_H4_ATR_14);
    info_lines[line_count++] = StringFormat("ATR均值*0.5: %.5f", g_H4_ATR_Mean_100*0.5);
    info_lines[line_count++] = StringFormat("ATR极低:     %s", g_H4_ATR_VeryLow ? "是" : "否");
    info_lines[line_count++] = "-----------------------------";
    info_lines[line_count++] = StringFormat("布林带宽:    %.5f", g_H4_BW);
    info_lines[line_count++] = StringFormat("10%%分位:     %.5f", g_H4_BW_10pct);
    info_lines[line_count++] = StringFormat("极窄:        %s", g_H4_BW_VeryLow ? "是" : "否");
    info_lines[line_count++] = "-----------------------------";
    info_lines[line_count++] = StringFormat("ADX:         %.2f", g_h4_adx);
    info_lines[line_count++] = StringFormat("极低:        %s", g_H4_ADX_VeryLow ? "是" : "否");
    info_lines[line_count++] = StringFormat("均线缠绕:    %.5f", g_H4_MAsRangePct);
    info_lines[line_count++] = StringFormat("缠绕:        %s", g_H4_MAs_Entangled ? "是" : "否");
    info_lines[line_count++] = "-----------------------------";
    info_lines[line_count++] = StringFormat("日线收敛:    %.5f", g_Daily_Range_7);
    info_lines[line_count++] = StringFormat("阈值:        %.5f", g_Daily_Typical_Range7_Mean * Daily_Range_Threshold);
    info_lines[line_count++] = StringFormat("收敛:        %s", g_Daily_Range_Converge ? "是" : "否");
    if(g_H4_Bars_VerySmall) info_lines[line_count++] = StringFormat("H4实体极小:   %.5f", g_H4_AvgBodySize_Last_5);
    if(g_Major_News_Upcoming_Within_48Hours) info_lines[line_count++] = "临近重要基本面事件";
    info_lines[line_count++] = "-----------------------------";
    info_lines[line_count++] = g_Core_Signal ? "【核心信号成立：极度收缩，警惕爆发】" : "（未极度收缩）";

    int line_gap = UI_FontSize + 8; // 增大行间距
    int bg_height = (line_count+2) * line_gap;

    if(ObjectFind(0,bgName)<0)
        ObjectCreate(0,bgName,OBJ_RECTANGLE_LABEL,0,0,0);
    ObjectSetInteger(0,bgName,OBJPROP_CORNER,CORNER_LEFT_UPPER);
    ObjectSetInteger(0,bgName,OBJPROP_XDISTANCE,UI_X-5);
    ObjectSetInteger(0,bgName,OBJPROP_YDISTANCE,UI_Y-5);
    ObjectSetInteger(0,bgName,OBJPROP_XSIZE,400);
    ObjectSetInteger(0,bgName,OBJPROP_YSIZE,bg_height);
    ObjectSetInteger(0,bgName,OBJPROP_BGCOLOR,UI_Color_BG);
    ObjectSetInteger(0,bgName,OBJPROP_COLOR,g_Core_Signal?UI_Color_Signal:UI_Color_Normal);
    ObjectSetInteger(0,bgName,OBJPROP_WIDTH,2);

    for(int i=0;i<30;i++) {
        string lineObj = labelName + "_line" + IntegerToString(i);
        ObjectDelete(0, lineObj);
    }
    // 移除闪烁效果，只在信号状态变化时更新UI
    for(int i=0;i<line_count;i++) {
        string lineObj = labelName + "_line" + IntegerToString(i);
        if(ObjectFind(0,lineObj)<0)
            ObjectCreate(0,lineObj,OBJ_LABEL,0,0,0);
        ObjectSetInteger(0,lineObj,OBJPROP_CORNER,CORNER_LEFT_UPPER);
        ObjectSetInteger(0,lineObj,OBJPROP_XDISTANCE,UI_X+5);
        ObjectSetInteger(0,lineObj,OBJPROP_YDISTANCE,UI_Y+5+i*line_gap);
        // 标题加粗蓝色
        if(i==0) {
            ObjectSetInteger(0,lineObj,OBJPROP_FONTSIZE,UI_FontSize+4);
            ObjectSetInteger(0,lineObj,OBJPROP_COLOR,UI_Color_Title);
            ObjectSetString(0,lineObj,OBJPROP_FONT, "Arial Bold");
        }
        // 结论固定高亮色（不闪烁）
        else if(i==line_count-1) {
            ObjectSetInteger(0,lineObj,OBJPROP_FONTSIZE,UI_FontSize+2);
            ObjectSetInteger(0,lineObj,OBJPROP_COLOR,g_Core_Signal ? clrBlue : clrDeepPink);
            ObjectSetString(0,lineObj,OBJPROP_FONT, "Arial Bold");
        }
        // 重要字段分色
        else if(StringFind(info_lines[i],"极低")>=0 || StringFind(info_lines[i],"极窄")>=0 || StringFind(info_lines[i],"缠绕")>=0 || StringFind(info_lines[i],"收敛")>=0) {
            ObjectSetInteger(0,lineObj,OBJPROP_FONTSIZE,UI_FontSize);
            ObjectSetInteger(0,lineObj,OBJPROP_COLOR,info_lines[i].Find("是")>=0 ? clrGreen : clrGray);
            ObjectSetString(0,lineObj,OBJPROP_FONT, "Arial");
        }
        else {
            ObjectSetInteger(0,lineObj,OBJPROP_FONTSIZE,UI_FontSize);
            ObjectSetInteger(0,lineObj,OBJPROP_COLOR,g_Core_Signal?UI_Color_Signal:UI_Color_Normal);
            ObjectSetString(0,lineObj,OBJPROP_FONT, "Arial");
        }
        ObjectSetString(0,lineObj,OBJPROP_TEXT,info_lines[i]);
    }
}

//+------------------------------------------------------------------+
//| EA主循环：定时刷新UI                                            |
//+------------------------------------------------------------------+
int OnInit()
{
    EventSetTimer(1); // 每1秒检查一次信号状态

    // 初始化时检测信号状态并更新UI
    last_signal_state = DetectEnergyCompression();
    ShowEnergyCompressionSignal();

    return INIT_SUCCEEDED;
}
void OnDeinit(const int reason)
{
    EventKillTimer();
    ObjectDelete(0,labelName);
    ObjectDelete(0,bgName);
    ObjectDelete(0,labelName+"_title");
    // 删除所有动态生成的行label
    for(int i=0;i<30;i++) {
        string lineObj = labelName + "_line" + IntegerToString(i);
        ObjectDelete(0, lineObj);
    }
}
void OnTimer()
{
    // 检测信号状态
    bool current_signal = DetectEnergyCompression();

    // 只有当信号状态发生变化时才更新UI，避免闪烁
    if(current_signal != last_signal_state) {
        last_signal_state = current_signal;
        ShowEnergyCompressionSignal();
    }
}
//+------------------------------------------------------------------+