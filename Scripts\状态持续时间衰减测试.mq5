//+------------------------------------------------------------------+
//|                                          状态持续时间衰减测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "状态持续时间衰减功能测试脚本"
#property script_show_inputs

// 测试参数
input int MaxTestDuration = 25;      // 最大测试持续时间(K线数)
input bool EnableDetailedLog = true; // 启用详细日志
input double BaseDecayRate = 0.05;   // 基础衰减率

//+------------------------------------------------------------------+
//| 状态持续时间衰减测试脚本                                          |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始状态持续时间衰减测试");
    Print("最大测试持续时间: ", MaxTestDuration, "根K线");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("基础衰减率: ", BaseDecayRate * 100, "%");
    Print("========================================");
    
    // 测试1: 衰减因子计算验证
    TestDecayFactorCalculation();
    
    // 测试2: 订单参数衰减调整
    TestOrderParameterDecayAdjustment();
    
    // 测试3: 效率评分计算
    TestEfficiencyScoreCalculation();
    
    // 测试4: 衰减等级分类
    TestDecayLevelClassification();
    
    // 测试5: 强制退出条件
    TestForceExitConditions();
    
    // 测试6: 完整衰减周期模拟
    TestCompleteDecayCycleSimulation();
    
    Print("========================================");
    Print("✅ 状态持续时间衰减测试完成");
}

//+------------------------------------------------------------------+
//| 测试衰减因子计算验证                                              |
//+------------------------------------------------------------------+
void TestDecayFactorCalculation()
{
    Print("📋 测试1: 衰减因子计算验证");
    
    // 测试不同持续时间的衰减因子
    struct DecayTestCase {
        int duration;
        double expectedDecayFactor;
        string description;
    };
    
    DecayTestCase testCases[] = {
        {1, 1.0, "1根K线-无衰减"},
        {3, 1.0, "3根K线-无衰减"},
        {5, 1.0, "5根K线-阈值边界"},
        {6, 0.95, "6根K线-开始衰减"},
        {8, 0.85, "8根K线-轻度衰减"},
        {10, 0.75, "10根K线-中度衰减"},
        {12, 0.65, "12根K线-重度衰减"},
        {15, 0.5, "15根K线-达到最大衰减"},
        {20, 0.5, "20根K线-保持最大衰减"}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        DecayTestCase tc = testCases[i];
        
        // 计算衰减因子
        double calculatedDecayFactor = CalculateTestDecayFactor(tc.duration);
        
        Print("场景: ", tc.description);
        Print("  持续时间: ", tc.duration, "根K线");
        Print("  计算衰减因子: ", DoubleToString(calculatedDecayFactor, 3));
        Print("  预期衰减因子: ", DoubleToString(tc.expectedDecayFactor, 3));
        Print("  偏差: ", DoubleToString(MathAbs(calculatedDecayFactor - tc.expectedDecayFactor), 3));
        
        bool factorCorrect = (MathAbs(calculatedDecayFactor - tc.expectedDecayFactor) < 0.01);
        Print("  因子验证: ", factorCorrect ? "✓" : "✗");
    }
    
    Print("✅ 衰减因子计算验证完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试衰减因子                                                  |
//+------------------------------------------------------------------+
double CalculateTestDecayFactor(int stateDuration) {
    int decayStartThreshold = 5;
    double baseDecayRate = BaseDecayRate;
    double maxDecayLimit = 0.5;
    
    // 状态持续时间未达到阈值，无衰减
    if(stateDuration <= decayStartThreshold) {
        return 1.0;
    }
    
    // 计算超出阈值的时间
    int excessDuration = stateDuration - decayStartThreshold;
    
    // 计算衰减因子
    double decayFactor = 1.0 - (excessDuration * baseDecayRate);
    
    // 应用最大衰减限制
    return MathMax(maxDecayLimit, decayFactor);
}

//+------------------------------------------------------------------+
//| 测试订单参数衰减调整                                              |
//+------------------------------------------------------------------+
void TestOrderParameterDecayAdjustment()
{
    Print("📋 测试2: 订单参数衰减调整");
    
    // 原始订单参数
    struct OriginalParams {
        int orderCount;
        int intervalSeconds;
        double orderSize;
    };
    
    OriginalParams originalParams = {5, 600, 0.05};
    
    // 测试不同衰减等级的参数调整
    int testDecayLevels[] = {0, 1, 2, 3, 4, 5};
    string levelDescriptions[] = {"无衰减", "轻度", "中度", "重度", "严重", "极度"};
    
    Print("原始参数: 订单数量=", originalParams.orderCount, 
          " 间隔=", originalParams.intervalSeconds, "秒",
          " 规模=", DoubleToString(originalParams.orderSize, 3), "手");
    Print("");
    
    for(int i = 0; i < ArraySize(testDecayLevels); i++) {
        int decayLevel = testDecayLevels[i];
        string levelDesc = levelDescriptions[i];
        
        // 计算调整后的参数
        int adjustedOrderCount = originalParams.orderCount;
        int adjustedInterval = originalParams.intervalSeconds;
        double adjustedOrderSize = originalParams.orderSize;
        
        // 应用衰减调整
        switch(decayLevel) {
            case 0: break; // 无调整
            case 1:
                adjustedOrderCount = (int)(adjustedOrderCount * 0.9);
                adjustedInterval = (int)(adjustedInterval * 1.1);
                break;
            case 2:
                adjustedOrderCount = (int)(adjustedOrderCount * 0.8);
                adjustedInterval = (int)(adjustedInterval * 1.2);
                adjustedOrderSize *= 0.9;
                break;
            case 3:
                adjustedOrderCount = (int)(adjustedOrderCount * 0.7);
                adjustedInterval = (int)(adjustedInterval * 1.3);
                adjustedOrderSize *= 0.8;
                break;
            case 4:
                adjustedOrderCount = (int)(adjustedOrderCount * 0.6);
                adjustedInterval = (int)(adjustedInterval * 1.4);
                adjustedOrderSize *= 0.7;
                break;
            case 5:
                adjustedOrderCount = 1;
                adjustedInterval = 900;
                adjustedOrderSize *= 0.5;
                break;
        }
        
        // 确保参数在合理范围内
        adjustedOrderCount = MathMax(1, MathMin(10, adjustedOrderCount));
        adjustedInterval = MathMax(300, MathMin(900, adjustedInterval));
        adjustedOrderSize = MathMax(0.01, MathMin(0.1, adjustedOrderSize));
        
        Print("衰减等级", decayLevel, " (", levelDesc, "):");
        Print("  订单数量: ", originalParams.orderCount, " → ", adjustedOrderCount);
        Print("  订单间隔: ", originalParams.intervalSeconds, "秒 → ", adjustedInterval, "秒");
        Print("  订单规模: ", DoubleToString(originalParams.orderSize, 3), "手 → ", 
              DoubleToString(adjustedOrderSize, 3), "手");
        
        // 计算调整幅度
        double countChange = (double)(adjustedOrderCount - originalParams.orderCount) / originalParams.orderCount * 100;
        double intervalChange = (double)(adjustedInterval - originalParams.intervalSeconds) / originalParams.intervalSeconds * 100;
        double sizeChange = (adjustedOrderSize - originalParams.orderSize) / originalParams.orderSize * 100;
        
        Print("  变化幅度: 数量", DoubleToString(countChange, 1), "% 间隔", 
              DoubleToString(intervalChange, 1), "% 规模", DoubleToString(sizeChange, 1), "%");
    }
    
    Print("✅ 订单参数衰减调整完成\n");
}

//+------------------------------------------------------------------+
//| 测试效率评分计算                                                  |
//+------------------------------------------------------------------+
void TestEfficiencyScoreCalculation()
{
    Print("📋 测试3: 效率评分计算");
    
    // 测试不同衰减情况的效率评分
    struct EfficiencyTestCase {
        double decayFactor;
        int duration;
        double expectedScore;
        string description;
    };
    
    EfficiencyTestCase testCases[] = {
        {1.0, 3, 100.0, "无衰减短期"},
        {0.95, 6, 97.5, "轻度衰减"},
        {0.85, 8, 92.5, "中度衰减"},
        {0.75, 10, 87.5, "重度衰减"},
        {0.65, 12, 82.5, "严重衰减"},
        {0.5, 15, 75.0, "极度衰减"},
        {0.5, 20, 65.0, "长期极度衰减"}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        EfficiencyTestCase tc = testCases[i];
        
        // 计算效率评分
        double baseScore = 100.0;
        double decayPenalty = (1.0 - tc.decayFactor) * 50.0;
        double durationPenalty = tc.duration > 10 ? (tc.duration - 10) * 2.0 : 0.0;
        double calculatedScore = MathMax(0.0, baseScore - decayPenalty - durationPenalty);
        
        Print("场景: ", tc.description);
        Print("  衰减因子: ", DoubleToString(tc.decayFactor, 3));
        Print("  持续时间: ", tc.duration, "根K线");
        Print("  衰减扣分: ", DoubleToString(decayPenalty, 1));
        Print("  时间扣分: ", DoubleToString(durationPenalty, 1));
        Print("  计算评分: ", DoubleToString(calculatedScore, 1));
        Print("  预期评分: ", DoubleToString(tc.expectedScore, 1));
        
        bool scoreCorrect = (MathAbs(calculatedScore - tc.expectedScore) < 5.0);
        Print("  评分验证: ", scoreCorrect ? "✓" : "✗");
    }
    
    Print("✅ 效率评分计算完成\n");
}

//+------------------------------------------------------------------+
//| 测试衰减等级分类                                                  |
//+------------------------------------------------------------------+
void TestDecayLevelClassification()
{
    Print("📋 测试4: 衰减等级分类");
    
    // 测试衰减因子到等级的映射
    double testDecayFactors[] = {1.0, 0.95, 0.85, 0.75, 0.65, 0.55, 0.45};
    int expectedLevels[] = {0, 1, 2, 3, 4, 5, 5};
    string levelNames[] = {"无衰减", "轻度", "中度", "重度", "严重", "极度"};
    
    for(int i = 0; i < ArraySize(testDecayFactors); i++) {
        double decayFactor = testDecayFactors[i];
        int expectedLevel = expectedLevels[i];
        
        // 计算衰减等级
        int calculatedLevel = 0;
        if(decayFactor >= 1.0) calculatedLevel = 0;
        else if(decayFactor >= 0.9) calculatedLevel = 1;
        else if(decayFactor >= 0.8) calculatedLevel = 2;
        else if(decayFactor >= 0.7) calculatedLevel = 3;
        else if(decayFactor >= 0.6) calculatedLevel = 4;
        else calculatedLevel = 5;
        
        string levelName = calculatedLevel < ArraySize(levelNames) ? levelNames[calculatedLevel] : "未知";
        
        Print("衰减因子: ", DoubleToString(decayFactor, 3), 
              " → 等级: ", calculatedLevel, " (", levelName, ")");
        Print("  预期等级: ", expectedLevel);
        
        bool levelCorrect = (calculatedLevel == expectedLevel);
        Print("  等级验证: ", levelCorrect ? "✓" : "✗");
    }
    
    Print("✅ 衰减等级分类完成\n");
}

//+------------------------------------------------------------------+
//| 测试强制退出条件                                                  |
//+------------------------------------------------------------------+
void TestForceExitConditions()
{
    Print("📋 测试5: 强制退出条件");
    
    // 测试不同条件组合的强制退出判断
    struct ForceExitTest {
        int duration;
        double decayFactor;
        double urgency;
        bool expectedForceExit;
        string description;
    };
    
    ForceExitTest testCases[] = {
        {10, 0.8, 30.0, false, "中等持续时间+中度衰减+中等紧迫度"},
        {16, 0.65, 25.0, true, "长持续时间+重度衰减+低紧迫度"},
        {20, 0.5, 15.0, true, "超长持续时间+极度衰减+极低紧迫度"},
        {18, 0.8, 40.0, false, "长持续时间但紧迫度较高"},
        {12, 0.5, 35.0, false, "重度衰减但紧迫度尚可"},
        {25, 0.6, 10.0, true, "所有条件都满足强制退出"}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        ForceExitTest fet = testCases[i];
        
        // 判断强制退出条件
        bool longDuration = (fet.duration > 15);
        bool heavyDecay = (fet.decayFactor < 0.7);
        bool lowEfficiency = (fet.urgency < 20.0);
        
        bool shouldForceExit = longDuration && heavyDecay && lowEfficiency;
        
        Print("场景: ", fet.description);
        Print("  持续时间: ", fet.duration, "根K线 (长期:", longDuration ? "是" : "否", ")");
        Print("  衰减因子: ", DoubleToString(fet.decayFactor, 3), " (重度:", heavyDecay ? "是" : "否", ")");
        Print("  紧迫度: ", DoubleToString(fet.urgency, 1), " (低效:", lowEfficiency ? "是" : "否", ")");
        Print("  强制退出: ", shouldForceExit ? "是" : "否");
        Print("  预期结果: ", fet.expectedForceExit ? "是" : "否");
        
        bool exitCorrect = (shouldForceExit == fet.expectedForceExit);
        Print("  退出验证: ", exitCorrect ? "✓" : "✗");
    }
    
    Print("✅ 强制退出条件完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整衰减周期模拟                                              |
//+------------------------------------------------------------------+
void TestCompleteDecayCycleSimulation()
{
    Print("📋 测试6: 完整衰减周期模拟");
    
    // 模拟一个完整的状态衰减周期
    double initialUrgency = 75.0;
    Print("初始紧迫度: ", DoubleToString(initialUrgency, 1));
    Print("衰减周期模拟:");
    
    for(int duration = 1; duration <= MaxTestDuration; duration++) {
        // 计算衰减因子
        double decayFactor = CalculateTestDecayFactor(duration);
        
        // 应用衰减到紧迫度
        double decayedUrgency = initialUrgency * decayFactor;
        
        // 计算效率评分
        double baseScore = 100.0;
        double decayPenalty = (1.0 - decayFactor) * 50.0;
        double durationPenalty = duration > 10 ? (duration - 10) * 2.0 : 0.0;
        double efficiencyScore = MathMax(0.0, baseScore - decayPenalty - durationPenalty);
        
        // 判断衰减等级
        int decayLevel = 0;
        if(decayFactor < 1.0) {
            if(decayFactor >= 0.9) decayLevel = 1;
            else if(decayFactor >= 0.8) decayLevel = 2;
            else if(decayFactor >= 0.7) decayLevel = 3;
            else if(decayFactor >= 0.6) decayLevel = 4;
            else decayLevel = 5;
        }
        
        // 判断是否强制退出
        bool shouldForceExit = (duration > 15) && (decayFactor < 0.7) && (decayedUrgency < 20.0);
        
        // 输出关键节点
        if(duration <= 10 || duration % 5 == 0 || shouldForceExit) {
            Print("  第", duration, "根K线: 衰减因子=", DoubleToString(decayFactor, 3),
                  " 紧迫度=", DoubleToString(decayedUrgency, 1),
                  " 效率=", DoubleToString(efficiencyScore, 1),
                  " 等级=", decayLevel,
                  shouldForceExit ? " [强制退出]" : "");
        }
        
        // 如果触发强制退出，结束模拟
        if(shouldForceExit) {
            Print("⚠️ 第", duration, "根K线触发强制退出条件");
            break;
        }
    }
    
    Print("✅ 完整衰减周期模拟完成\n");
}
