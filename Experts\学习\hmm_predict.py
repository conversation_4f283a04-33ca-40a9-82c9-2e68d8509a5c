import numpy as np
from hmmlearn import hmm
import pandas as pd
import sys

def load_market_data(file_path):
    """加载市场数据"""
    try:
        data = pd.read_csv(file_path)
        return data
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def preprocess_data(data):
    """数据预处理"""
    # 计算收益率
    returns = np.diff(np.log(data['close']))
    # 计算波动率
    volatility = data['high'] - data['low']
    # 计算成交量变化
    volume_change = np.diff(np.log(data['volume']))
    
    # 组合特征
    features = np.column_stack([returns[1:], volatility[1:], volume_change])
    return features

def train_hmm_model(features, n_states=4):
    """训练隐马尔可夫模型"""
    model = hmm.GaussianHMM(n_components=n_states, covariance_type="full", n_iter=100)
    model.fit(features)
    return model

def predict_market_state(model, features):
    """预测市场状态"""
    # 获取最可能的状态序列
    states = model.predict(features)
    # 获取最后一个状态的概率分布
    last_probs = model.predict_proba(features)[-1]
    return states[-1], last_probs

def main():
    if len(sys.argv) < 2:
        print("Please provide data file path")
        return
    
    # 加载数据
    data = load_market_data(sys.argv[1])
    if data is None:
        return
    
    # 数据预处理
    features = preprocess_data(data)
    
    # 训练模型
    model = train_hmm_model(features)
    
    # 预测状态
    current_state, state_probs = predict_market_state(model, features)
    
    # 输出结果
    print(f"{current_state},{','.join([str(p) for p in state_probs])}")

if __name__ == "__main__":
    main()