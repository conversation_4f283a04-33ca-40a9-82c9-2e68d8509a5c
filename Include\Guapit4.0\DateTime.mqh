//+------------------------------------------------------------------+
//|                                                       Guapit.mq5 |
//|                                           Copyright 2023, Guapit |
//|                                          https://www.guapit.com/ |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, Guapit"
#property link      "https://www.guapit.com/"
#property version   "1.00"

class DateTime
{
  public:
    DateTime(void);
    DateTime(DateTime &dt);
    DateTime(const datetime dtime);
    DateTime(int year, int month, int day, int hour, int minute, int secound);
    ~DateTime(void);
    
    int year; // 年份
    int month; // 月份
    int day; // 日
    int hour; // 时
    int minute; // 分
    int second; // 秒
    int days; // 当年1月1日到现在时间
    int week; // 星期几
    
    // 方法
    datetime Time(void) { return m_dtime; }
    int TimeSec(void) { return (int)m_dtime; }
    // 判断闰年
    bool IsLeapYear(void);
    // 根据月份计算出当月的天数
    int DaysInMonth(int Month);
    void setTime(const datetime dtime) { m_dtime = dtime; }
    DateTime *FromSec(const int secounds);
    // 手动添加时间
    DateTime *AddSecounds(const int secounds);
    // 添加分钟
    DateTime *AddMinutes(const int minutes);
    // 添加小时
    DateTime *AddHours(const int Hours);
    // 添加年
    DateTime *AddYears(const int years);
    // 根据添加的月份计算出新的时间
    DateTime *AddMonths(int months);
    // 根据天数添加时间
    DateTime *AddDays(int days);
    int CompareTo(DateTime &other);
    
    string ToString();
    string ToString(string format);
    
    // 重载运算符
    DateTime operator + (DateTime &other);
    DateTime operator - (DateTime &other);
    bool operator == (DateTime &other);
    bool operator != (DateTime &other);
    bool operator > (DateTime &other);
    bool operator >= (DateTime &other);
    bool operator < (DateTime &other);
    bool operator <= (DateTime &other);
    
    
   private:
      void Init();
      datetime m_dtime; // 核心时间
      MqlDateTime m_dt; // 转化为日期时间结构体

};

DateTime::DateTime(void)
{
    m_dtime = TimeCurrent();
    Init();
}

DateTime::DateTime(DateTime &dt)
{
   m_dtime = dt.Time();
   Init();
}
DateTime::~DateTime(void)
{
    // delete _dtime;
}

DateTime::DateTime(const datetime dtime)
{
   m_dtime = dtime;
   Init();
}

DateTime::DateTime(int Year, int Month, int Day, int Hour, int Minute, int Secound)
{
 
   m_dt.year = Year;
   m_dt.mon = Month;
   m_dt.day = Day;
   m_dt.hour = Hour;
   m_dt.min = Minute;
   m_dt.sec = Secound;
   m_dtime = StructToTime(m_dt);
   Init();
}
void DateTime::Init()
{
   TimeToStruct(m_dtime,m_dt);
   year = m_dt.year; // 年份
   month= m_dt.year; // 月份
   day = m_dt.year; // 日
   hour = m_dt.year;; // 时
   minute= m_dt.year; // 分
   second= m_dt.year; // 秒
   days = m_dt.day_of_year; // 当年1月1日到现在时间
   week = m_dt.day_of_week; // 星期几 0 - 6
   
}

string DateTime::ToString()
{
   return ToString("Y-m-d H:M:S");
}

/*
Y: 年份
m: 月份
d: 日
H: 小时
M(大写): 分钟
S(大写): 秒
例子: Y-m-D H:M:S
*/
string DateTime::ToString(string format)
{
   string u_yy = "Y", u_mm = "m", u_dd = "d", u_HH = "H", u_MM = "M", u_SS = "S";
   string newStr = format;
   string u_yy_str = IntegerToString(m_dt.year);
   string u_mm_str = IntegerToString(m_dt.mon,2,'0');
   string u_dd_str = IntegerToString(m_dt.day,2,'0');
   string u_HH_str = IntegerToString(m_dt.hour,2,'0');
   string u_MM_str = IntegerToString(m_dt.min,2,'0');
   string u_SS_str = IntegerToString(m_dt.sec,2,'0');
   StringReplace(newStr,u_yy,u_yy_str);
   StringReplace(newStr,u_mm,u_mm_str);
   StringReplace(newStr,u_dd,u_dd_str);
   StringReplace(newStr,u_HH,u_HH_str);
   StringReplace(newStr,u_MM,u_MM_str);
   StringReplace(newStr,u_SS,u_SS_str);
   return newStr;
}

// 根据秒数转换成时间
DateTime *DateTime::FromSec(const int secounds)
{
   m_dtime = datetime(secounds);
   return &this;
}


DateTime DateTime::operator + (DateTime &other)
{
   DateTime _dtime = DateTime();
   _dtime.setTime(m_dtime + other.Time());
   return _dtime;
}

DateTime DateTime::operator - (DateTime &other)
{
   DateTime _dtime = DateTime();
   _dtime.setTime(m_dtime - other.Time());
   return _dtime;
}

bool DateTime::operator == (DateTime &other)
{
   if(CompareTo(other) == 0) return true;
   return false;
}

bool DateTime::operator != (DateTime &other)
{
   if(CompareTo(other) != 0) return true;
   return false;
}

bool DateTime::operator > (DateTime &other)
{
   if(CompareTo(other) == 1) return true;
   return false;
}

bool DateTime::operator >= (DateTime &other)
{
   if(CompareTo(other) >= 0) return true;
   return false;
}

bool DateTime::operator < (DateTime &other)
{
   if(CompareTo(other) < 0) return true;
   return false;
}

bool DateTime::operator <= (DateTime &other)
{
   if(CompareTo(other) <= 0) return true;
   return false;
}

int DateTime::CompareTo(DateTime &other)
{
   return m_dtime > other.Time() ? 1 : m_dtime == other.Time() ? 0 : -1;
}

bool DateTime::IsLeapYear(void)
{
  return (year % 4 == 0 && year % 100 != 0) ||
         (year % 400 == 0 && year % 100 == 0) ? true : false;
         
}

int DateTime::DaysInMonth(int Month)
{
  if (Month < 1 || Month > 12)
    {
        printf("月份超出范围,值范围: 1 - 12 之间, 当前值: %d", Month);
        return 0;
    }
  if (Month == 2)
     return IsLeapYear() ? 29 : 28;
  else if (Month == 4 || Month == 6 || Month == 9 || Month == 11)
     return 30;
  else
    return 31;

}

DateTime *DateTime::AddMonths(int months)
{
    int _year = m_dt.year;
    int _month = m_dt.mon;
    int _day = m_dt.day;
    _month += months;
    while (_month > 12)
    {
        _year++;
        _month -= 12;
    }

    while (_month < 1)
    {
        _year--;
        _month += 12;
    }

    int daysInMonth = DaysInMonth(_month);
    if (_day > daysInMonth)
    {
        _day = daysInMonth;
    }
    this = DateTime(_year, _month, _day, m_dt.hour, m_dt.min, m_dt.sec);
    return &this;
}

DateTime *DateTime::AddYears(const int years)
{
    if(years < 0 || years > 9999)
    {
        printf("年限超出范围,默认范围: 0 - 9999");
        return &this;
    }
      
    m_dt.year += years;
    Init();
    return &this;
}
DateTime *DateTime::AddDays(int Days)
{
   AddSecounds(days * 86400);
   return &this;
}


DateTime *DateTime::AddSecounds(const int secounds)
{
   m_dtime += secounds;
   Init();
   return &this;
}

DateTime *DateTime::AddMinutes(const int minutes)
{
   AddSecounds(minutes * 60);
   return &this;
}

DateTime *DateTime::AddHours(const int Hours)
{
   AddSecounds(Hours * 3600);
   return &this;
}