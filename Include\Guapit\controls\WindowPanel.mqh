//+------------------------------------------------------------------+
//|                                                       Guapit.mq5 |
//|                                           Copyright 2023, Guapit |
//|                                          https://www.guapit.com/ |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, Guapit"
#property link      "https://www.guapit.com/"
#property version   "1.00"
#include "GObject.mqh"

class WindowPanel: public GObject
{
   public:
      WindowPanel(void);
      ~WindowPanel(void);
      
      // 创建面板
      void Create(string name, int x, int y);
      // 设置尺寸
      void Size(int width, int height);
      // 设置定位点
      void Corner(ENUM_BASE_CORNER type);
      // 设置边框点造型
      void Border(ENUM_BORDER_TYPE type, color clr);
      // 设置边框点样式
      void BorderStyle(ENUM_LINE_STYLE style, int width, color clr);
      // 移动控件
      void Move(int x, int y);
      // 主动更新控件设置
      void Update(void);
      // 删除控件
      void DeleteAll(string prefix);
      // 是否接受鼠标点击事件
      void IsSelected(const bool selected);
      // 是否可以被拖动
      void IsSelectable(const bool selectable);
      // 在对象列表菜单中隐藏
      void IsHidden(const bool hidden);
      // 是否接受优先点击
      void IsZorder(const bool zorder);
};

WindowPanel::WindowPanel(void)
{
  SetInt(OBJPROP_BORDER_TYPE,BORDER_FLAT);
  IsSelected(false);
  IsSelectable(false);
  IsHidden(true);
  IsZorder(false);
}
WindowPanel::~WindowPanel(void)
{
}

void WindowPanel::Create(string name,int x,int y)
{
    BaseCreateXY(name,OBJ_RECTANGLE_LABEL);
    SetInt(OBJPROP_XDISTANCE,x);
    SetInt(OBJPROP_YDISTANCE,y);
}

void WindowPanel::Size(int width,int height)
{
    SetInt(OBJPROP_XSIZE,width);
    SetInt(OBJPROP_YSIZE,height);
}

void WindowPanel::Corner(ENUM_BASE_CORNER type)
{
  SetInt(OBJPROP_CORNER,type);
}

void WindowPanel::Border(ENUM_BORDER_TYPE type, color clr)
{
  SetInt(OBJPROP_BORDER_TYPE,type);
  SetInt(OBJPROP_BORDER_COLOR,clr);
}

void WindowPanel::BorderStyle(ENUM_LINE_STYLE style, int width, color clr)
{
  SetInt(OBJPROP_STYLE,style);
  SetInt(OBJPROP_WIDTH,width);
  SetInt(OBJPROP_COLOR,clr);
}

// 是否接受鼠标点击事件
void WindowPanel::IsSelected(const bool selected)
{
  SetInt(OBJPROP_SELECTED,selected);
}
// 是否可以被拖动
void WindowPanel::IsSelectable(const bool selectable)
{
  SetInt(OBJPROP_SELECTABLE,selectable);
}
// 在对象列表菜单中隐藏
void WindowPanel::IsHidden(const bool hidden)
{
  SetInt(OBJPROP_HIDDEN,hidden);
}
// 是否接受优先点击
void WindowPanel::IsZorder(const bool zorder)
{
  SetInt(OBJPROP_ZORDER,zorder);
}



void WindowPanel::Update(void)
{
  BaseUpdate();
}

void WindowPanel::DeleteAll(string prefix)
{
  string prefix_name = StringSubstr(prefix,0,3); 
  ObjectsDeleteAll(ChartId(),SubWindow(),-1);
}

void WindowPanel::Move(int x, int y)
{
   SetInt(OBJPROP_XDISTANCE,x);
   SetInt(OBJPROP_YDISTANCE,y);
}


