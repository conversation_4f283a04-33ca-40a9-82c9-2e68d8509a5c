//+------------------------------------------------------------------+
//|                                           Bittma EA带UI界面.mq5 |
//|                                 Copyright 2024, MetaQuotes Software Corp. |
//|                                           https://www.meta.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link             "https://www.meta.com"
#property version       "1.30"

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/SymbolInfo.mqh>
#include <Arrays/ArrayLong.mqh>
#include <Indicators/Indicator.mqh>
#include <Indicators/Trend.mqh>
#include <Indicators/Oscilators.mqh>
#include <Guapit/controls/Button.mqh>

//--- 输入参数
input group "===== 资金管理 =====" 
input bool       UseCompoundMode = true;             // 使用复利模式
input double BaseEquityPerLot = 5000.0;       // 每0.01手 所需的资金(复利模式) 
input double FixedLotSize = 0.01;                   // 固定手数(非复利模式) 
input double MaxRiskPercent = 10.0;               // 账户最大风险比例(触发平仓)
input int         MaxLayers = 15;                             // 最大加仓层数
input double BaseProfitTarget = 2000.0;       // 基础止盈目标(美元) 
input double ManualOrderVolume = 0.01;         // 手动下单默认手数

input group "===== ATR参数 =====" 
input int         ATRPeriod = 14;                             // ATR 周期
input double ATRMultiplier = 5.0;                   // ATR 乘数(加仓间距)
input double InitialATRMultiplier = 10.0;   // 初始加仓ATR乘数
input double StopLossATRMultiplier = 3.0;   // 止损ATR乘数

input group "===== 加仓参数 =====" 
input double InitialLayerMultiplier = 1.5;   // 初期加仓倍数
input double MidLayerMultiplier = 1.3;           // 中期加仓倍数
input double LateLayerMultiplier = 1.1;         // 后期加仓倍数
input int         MidLayerStart = 2;                       // 中期加仓起始层数
input int         LateLayerStart = 6;                     // 后期加仓起始层数
input double MaxPositionSizePercent = 10.0;   // 最大仓位占比(%)

input group "===== 止盈止损参数 =====" 
input int         BreakEvenPoints = 300;               // 保本点数
input int         TrailPoints = 50;                         // 跟踪止损点数
input double MinStopDistanceMultiplier = 1.5;   // 最小止损距离乘数

//--- 全局变量
CTrade               trade;                                // 交易类
CPositionInfo        *positionInfo;                      // 持仓信息类
CSymbolInfo          symbolInfo;                         // 品种信息类
CArrayLong           m_arr_tickets;                      // 订单号数组

double               initialEquity;                      // 初始净值
double               currentEquity;                      // 当前净值
double               highestEquity;                      // 最高净值
bool                 trendDirection;                     // true:向上趋势, false:向下趋势
int                  currentLayer;                       // 当前加仓层数
double               lastBuyPrice;                       // 上次买入价格
double               lastSellPrice;                      // 上次卖出价格
int                  atrHandle;                          // ATR指标句柄
int                  macdHandle;                         // MACD指标句柄
double               symbolPoint;                        // 点值
double               symbolMinLot;                       // 最小手数
double               symbolLotStep;                      // 手数步长
double               symbolMaxLot;                       // 最大手数
int                  symbolTradeStopsLevel;              // 交易止损级别
int                  symbolTradeFreezeLevel;             // 交易冻结级别

//--- UI相关变量
Button               btnBuy, btnSell, btnCloseAll;       // 按钮控件
double               account_equity;                     // 账户净值 
double               account_balance;                    // 账户余额 
double               account_profit;                     // 当前浮动盈亏 
double               long_volume;                        // 多单手数 
double               short_volume;                       // 空单手数 
double               total_volume;                       // 总手数 
double               daily_profit;                       // 当日收益
double               daily_start_balance;                // 当日开始时的账户余额
datetime             last_daily_check;                   // 上次检查日期

// 定义RTOTAL和SLEEPTIME (用于平仓循环)
#define RTOTAL 3
#define SLEEPTIME 1000

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
      // 初始化指标句柄
      atrHandle = iATR(_Symbol, _Period, ATRPeriod);
      macdHandle = iMACD(_Symbol, _Period, 12, 26, 9, PRICE_CLOSE);
       
      // 设置初始资金
      initialEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      currentEquity = initialEquity;
      highestEquity = initialEquity;
       
      // 初始化交易变量
      currentLayer = 0;
      lastBuyPrice = 0;
      lastSellPrice = 0;
      trendDirection = false;
       
      // 设置交易参数
      trade.SetDeviationInPoints(10);
      trade.SetTypeFilling(ORDER_FILLING_FOK);
      trade.SetExpertMagicNumber(0);
      trade.SetMarginMode();
      trade.SetTypeFillingBySymbol(Symbol());
       
      // 获取交易品种属性
      symbolInfo.Name(Symbol());
      symbolPoint = _Point;
      symbolMinLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      symbolLotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
      symbolMaxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

      // 获取交易属性的数值
      symbolTradeStopsLevel = GetSymbolIntegerInfo(_Symbol, SYMBOL_TRADE_STOPS_LEVEL, "交易止损级别");
      symbolTradeFreezeLevel = GetSymbolIntegerInfo(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL, "交易冻结级别");

      // 输出交易品种属性
      Print(_Symbol, " 交易属性 - 最小手数: ", symbolMinLot, 
                                    ", 手数步长: ", symbolLotStep,
                                    ", 最大手数: ", symbolMaxLot);
      Print("最小止损距离: ", symbolTradeStopsLevel, " points");
      Print("止损冻结区域: ", symbolTradeFreezeLevel, " points");
      
      // 初始化持仓信息对象 
      positionInfo = new CPositionInfo();
      
      // 创建UI界面
      CreateUI();
      
      // 创建信息面板
      CreateInfoPanel();
      
      // 初始化当日收益相关变量
      daily_profit = 0.0;
      daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      last_daily_check = 0;
       
      return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
      // 释放指标句柄
      IndicatorRelease(atrHandle);
      IndicatorRelease(macdHandle);
      
      // 删除信息面板和UI界面
      ObjectsDeleteAll(0, "InfoPanel_");
      ObjectsDeleteAll(0, "gp_button_");
      
      // 释放持仓信息对象 
      if(CheckPointer(positionInfo) == POINTER_DYNAMIC)
            delete positionInfo;
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
      // 处理按钮点击事件
      if(id == CHARTEVENT_OBJECT_CLICK)
      {
            if(sparam == "gp_button_buy")
            {
                  // 买入按钮点击
                  OpenBuyOrder();
                  btnBuy.State(false);
                  btnBuy.Update();
            }
            else if(sparam == "gp_button_sell")
            {
                  // 卖出按钮点击
                  OpenSellOrder();
                  btnSell.State(false);
                  btnSell.Update();
            }
            else if(sparam == "gp_button_close")
            {
                  // 平仓按钮点击
                  CloseAllPositions();
                  btnCloseAll.State(false);
                  btnCloseAll.Update();
            }
      }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
      // 更新市场数据
      if(!UpdateMarketData())
            return;
            
      // 更新信息面板
      UpdateInfoPanel();
      
      // 更新账户资金
      UpdateEquity();
       
      // 检查账户风险
      CheckAccountRisk();
       
      // 获取当前ATR值
      double atr = GetATRValue();
       
      // 确定趋势
      DetermineTrendDirection();
       
      // 执行交易逻辑
      ExecuteTradingLogic(atr);
       
      // 管理已有仓位
      ManageOpenPositions(atr);
}

//+------------------------------------------------------------------+
//| 创建UI界面                                                       |
//+------------------------------------------------------------------+
void CreateUI()
{
      // 创建买入按钮
      btnBuy = new Button();
      btnBuy.Create("gp_button_buy", 150, 250);
      btnBuy.Size(100, 32);
      btnBuy.Text("买入开仓");
      btnBuy.Font("极影毁片荧圆", 12);
      btnBuy.Color(clrWhite);
      btnBuy.BGColor(clrDarkGreen);
      btnBuy.BorderColor(clrGreen);
      btnBuy.State(false);
      btnBuy.Update();
      
      // 创建卖出按钮
      btnSell = new Button();
      btnSell.Create("gp_button_sell", 150, 200);
      btnSell.Size(100, 32);
      btnSell.Text("卖出开仓");
      btnSell.Font("极影毁片荧圆", 12);
      btnSell.Color(clrWhite);
      btnSell.BGColor(clrDarkRed);
      btnSell.BorderColor(clrRed);
      btnSell.State(false);
      btnSell.Update();
      
      // 创建平仓按钮
      btnCloseAll = new Button();
      btnCloseAll.Create("gp_button_close", 150, 150);
      btnCloseAll.Size(100, 32);
      btnCloseAll.Text("一键平仓");
      btnCloseAll.Font("极影毁片荧圆", 12);
      btnCloseAll.Color(clrWhite);
      btnCloseAll.BGColor(clrCrimson);
      btnCloseAll.BorderColor(clrBrown);
      btnCloseAll.State(false);
      btnCloseAll.Update();
      
      // 设置按钮对齐方式为右下角
      ObjectSetInteger(0, "gp_button_buy", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
      ObjectSetInteger(0, "gp_button_sell", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
      ObjectSetInteger(0, "gp_button_close", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
      // 创建信息面板背景
      ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 5);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 25);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 220);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 230);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrMediumBlue);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
      ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
      
      // 创建标题
      ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 35);
      ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "Bittma EA 信息");
      ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrWhiteSmoke);
      
      // 创建账户余额标签
      ObjectCreate(0, "InfoPanel_Balance", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_YDISTANCE, 60 + 8);
      ObjectSetString(0, "InfoPanel_Balance", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_COLOR, clrWhiteSmoke);
      
      // 创建账户净值标签
      ObjectCreate(0, "InfoPanel_Equity", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_YDISTANCE, 80 + 10);
      ObjectSetString(0, "InfoPanel_Equity", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_COLOR, clrLightYellow);
      
      // 创建浮动盈亏标签
      ObjectCreate(0, "InfoPanel_Profit", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_YDISTANCE, 100 + 13);
      ObjectSetString(0, "InfoPanel_Profit", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, clrBlack);
      
      // 创建多单总量标签
      ObjectCreate(0, "InfoPanel_LongVolume", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_YDISTANCE, 120 + 16);
      ObjectSetString(0, "InfoPanel_LongVolume", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_COLOR, clrLightYellow);
      
      // 创建空单总量标签
      ObjectCreate(0, "InfoPanel_ShortVolume", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_YDISTANCE, 140 + 17);
      ObjectSetString(0, "InfoPanel_ShortVolume", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_COLOR, clrLightYellow);
      
      // 创建总仓位标签
      ObjectCreate(0, "InfoPanel_TotalVolume", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_XDISTANCE, 17);
      ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_YDISTANCE, 160 + 19);
      ObjectSetString(0, "InfoPanel_TotalVolume", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_FONTSIZE, 16);
      ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_COLOR, clrLightYellow);
      
      // 创建当日收益标签
      ObjectCreate(0, "InfoPanel_DailyProfit", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_XDISTANCE, 15);
      ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_YDISTANCE, 180+ 22); 
      ObjectSetString(0, "InfoPanel_DailyProfit", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_FONTSIZE, 14);
      ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_COLOR, clrBlack);
      
      // 创建当前层数标签
      ObjectCreate(0, "InfoPanel_Layer", OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "InfoPanel_Layer", OBJPROP_XDISTANCE, 15);
      ObjectSetInteger(0, "InfoPanel_Layer", OBJPROP_YDISTANCE, 200+ 25); 
      ObjectSetString(0, "InfoPanel_Layer", OBJPROP_FONT, "极影毁片荧圆");
      ObjectSetInteger(0, "InfoPanel_Layer", OBJPROP_FONTSIZE, 14);
      ObjectSetInteger(0, "InfoPanel_Layer", OBJPROP_COLOR, clrYellow);
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
      // 获取账户信息 
      account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
      account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
      
      // 计算当日收益
      CalculateDailyProfit();
   
      // 更新账户余额
      ObjectSetString(0, "InfoPanel_Balance", OBJPROP_TEXT, StringFormat("余额: %.2f", account_balance));
      
      // 更新账户净值
      ObjectSetString(0, "InfoPanel_Equity", OBJPROP_TEXT, StringFormat("净值: %.2f", account_equity));
      
      // 更新浮动盈亏
      string profit_text = StringFormat("盈亏: %.2f", account_profit);
      color profit_color = (account_profit >= 0) ? clrYellow : clrRed;
      ObjectSetString(0, "InfoPanel_Profit", OBJPROP_TEXT, profit_text);
      ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, profit_color);
      
      // 更新多单总量
      ObjectSetString(0, "InfoPanel_LongVolume", OBJPROP_TEXT, StringFormat("多单: %.2f", long_volume));
      
      // 更新空单总量
      ObjectSetString(0, "InfoPanel_ShortVolume", OBJPROP_TEXT, StringFormat("空单: %.2f", short_volume));
      
      // 更新总仓位
      ObjectSetString(0, "InfoPanel_TotalVolume", OBJPROP_TEXT, StringFormat("仓位: %.2f", total_volume));
      
      // 更新当日收益
      string daily_profit_text = StringFormat("当日收益: %.2f", daily_profit);
      color daily_profit_color = (daily_profit >= 0) ? clrYellow : clrRed;
      ObjectSetString(0, "InfoPanel_DailyProfit", OBJPROP_TEXT, daily_profit_text);
      ObjectSetInteger(0, "InfoPanel_DailyProfit", OBJPROP_COLOR, daily_profit_color);
      
      // 更新当前层数
      string layer_text = StringFormat("当前层数: %d", currentLayer);
      ObjectSetString(0, "InfoPanel_Layer", OBJPROP_TEXT, layer_text);
}

//+------------------------------------------------------------------+
//| 计算当日收益                                                     |
//+------------------------------------------------------------------+
void CalculateDailyProfit()
{
      // 获取当前服务器时间 
      datetime current_time = TimeCurrent();
      
      // 如果是新的一天，重置当日收益 
      if(last_daily_check == 0 || TimeToString(last_daily_check, TIME_DATE) != TimeToString(current_time, TIME_DATE))
      {
            daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
            last_daily_check = current_time;
      }
      
      // 计算当日收益
      daily_profit = account_equity - daily_start_balance;
}

//+------------------------------------------------------------------+
//| 更新市场数据                                                     |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
      // 获取账户信息 
      account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
      account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
      
      // 重置手数统计 
      long_volume = 0;
      short_volume = 0;
      
      int total = PositionsTotal();
      for(int i = total - 1; i >= 0; i--)
      {
            if(positionInfo.SelectByIndex(i) && positionInfo.Symbol() == Symbol())
            {
                  ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)positionInfo.PositionType();
                  if(pos_type == POSITION_TYPE_BUY)
                  {
                        long_volume += positionInfo.Volume();
                  }
                  else if(pos_type == POSITION_TYPE_SELL)
                  {
                        short_volume += positionInfo.Volume();
                  }
            }
      }
      
      // 计算总手数（绝对值）
      total_volume = MathAbs(long_volume - short_volume);
      
      return true;
}

//+------------------------------------------------------------------+
//| 获取整型交易属性的数值                                            |
//+------------------------------------------------------------------+
int GetSymbolIntegerInfo(const string symbol, const ENUM_SYMBOL_INFO_INTEGER property_id, const string info_name) 
{
        long value = 0; // 注意是 long 类型，匹配 SymbolInfoInteger 的第三个参数
        if (SymbolInfoInteger(symbol, property_id, value)) // 如果获取成功
        {
                Print("获取交易", info_name, ": ", value);
                return (int)value; // 将 long 类型转为 int 返回
        }
        else
        {
                Print("获取", info_name, "失败: ", GetLastError());
                return 0; // 返回默认值 0 
        }
}

//+------------------------------------------------------------------+
//| 手数规范化函数                                                    |
//+------------------------------------------------------------------+
double NormalizeLotSize(double lots)
{
      // 检查输入值是否为负数
      if(lots < 0)
      {
            Print("警告: 输入手数为负值 ", lots, "，已修正为最小手数");
            return symbolMinLot;
      }
      
      // 检查最小手数
      if(lots < symbolMinLot)
      {
            Print("警告: 输入手数 ", lots, " 小于最小手数 ", symbolMinLot, "，已修正");
            return symbolMinLot;
      }
       
      // 检查最大手数
      if(lots > symbolMaxLot)
      {
            Print("警告: 输入手数 ", lots, " 大于最大手数 ", symbolMaxLot, "，已修正");
            lots = symbolMaxLot;
      }
       
      // 规范化手数 - 使用MathFloor确保不超过有效值
      double normalizedLot = MathFloor(lots / symbolLotStep) * symbolLotStep;
      
      // 确保规范化后的手数不小于最小手数
      normalizedLot = MathMax(normalizedLot, symbolMinLot);
      
      // 确保规范化后的手数是有效的
      normalizedLot = NormalizeDouble(normalizedLot, 2);
      
      // 额外检查确保手数是有效的
      if(MathMod(normalizedLot, symbolLotStep) != 0)
      {
            Print("警告: 手数 ", normalizedLot, " 不是 ", symbolLotStep, " 的整数倍，已修正为 ", 
                  MathFloor(normalizedLot / symbolLotStep) * symbolLotStep);
            normalizedLot = MathFloor(normalizedLot / symbolLotStep) * symbolLotStep;
            normalizedLot = NormalizeDouble(normalizedLot, 2);
      }
      
      // 最终检查确保手数在有效范围内
      if(normalizedLot < symbolMinLot) normalizedLot = symbolMinLot;
      if(normalizedLot > symbolMaxLot) normalizedLot = symbolMaxLot;
       
      return normalizedLot;
}

//+------------------------------------------------------------------+
//| 更新账户资金                                                      |
//+------------------------------------------------------------------+
void UpdateEquity()
{
      currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      if(currentEquity > highestEquity)
            highestEquity = currentEquity;
}

//+------------------------------------------------------------------+
//| 检查账户风险                                                      |
//+------------------------------------------------------------------+
void CheckAccountRisk()
{
      double drawdownPercent = (initialEquity - currentEquity) / initialEquity * 100;
      if(drawdownPercent >= MaxRiskPercent)
      {
            CloseAllPositions();
            Print("账户风险达到 ", MaxRiskPercent, "%, 触发平仓");
      }
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                          |
//+------------------------------------------------------------------+
double GetATRValue()
{
      double atr[1];
      if(CopyBuffer(atrHandle, 0, 0, 1, atr) == 1)
            return atr[0];
      return 0;
}

//+------------------------------------------------------------------+
//| 确定趋势方向                                                      |
//+------------------------------------------------------------------+
void DetermineTrendDirection()
{
      double macdMain[1], macdSignal[1];
      if(CopyBuffer(macdHandle, MAIN_LINE, 0, 1, macdMain) == 1 && 
            CopyBuffer(macdHandle, SIGNAL_LINE, 0, 1, macdSignal) == 1)
      {
            trendDirection = (macdMain[0] > macdSignal[0]);
      }
}

//+------------------------------------------------------------------+
//| 执行交易逻辑                                                      |
//+------------------------------------------------------------------+
void ExecuteTradingLogic(double atr)
{
      // 计算当前手数
      double lotSize = CalculateLotSize();
       
      // 获取当前价格
      double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
       
      // 计算加仓间距
      double spacing = CalculateSpacing(atr, currentLayer);
       
      // 向上趋势逻辑
      if(trendDirection)
      {
            if(lastBuyPrice == 0 || (ask - lastBuyPrice) >= spacing)
            {
                  OpenBuyPosition(lotSize, atr);
                  lastBuyPrice = ask;
            }
      }
      // 向下趋势逻辑
      else
      {
            if(lastSellPrice == 0 || (lastSellPrice - bid) >= spacing)
            {
                  OpenSellPosition(lotSize, atr);
                  lastSellPrice = bid;
            }
      }
}

//+------------------------------------------------------------------+
//| 计算手数大小                                                      |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
      double lotSize = 0;
       
      if(!UseCompoundMode)
      {
            lotSize = FixedLotSize;
      }
      else
      {
            lotSize = NormalizeDouble((currentEquity / BaseEquityPerLot) * 0.01, 2);
      }
       
      // 返回手数规范化
      lotSize = NormalizeLotSize(lotSize);
       
      return MathMax(lotSize, symbolMinLot);
}

//+------------------------------------------------------------------+
//| 计算加仓间距                                                      |
//+------------------------------------------------------------------+
double CalculateSpacing(double atr, int layer)
{
      double spacing = atr * ATRMultiplier * symbolPoint;
       
      // 初始加仓使用更大的间距
      if(layer == 0)
            spacing = atr * InitialATRMultiplier * symbolPoint;
       
      // 根据趋势加速调整间距
      if(IsTrendAccelerating())
            spacing *= 0.7; // 减少30% 
       
      return spacing;
}

//+------------------------------------------------------------------+
//| 确定趋势是否加速                                                  |
//+------------------------------------------------------------------+
bool IsTrendAccelerating()
{
      double macdMain[2], macdSignal[2];
      if(CopyBuffer(macdHandle, MAIN_LINE, 0, 2, macdMain) == 2 && 
            CopyBuffer(macdHandle, SIGNAL_LINE, 0, 2, macdSignal) == 2)
      {
            double currentDiff = macdMain[0] - macdSignal[0];
            double previousDiff = macdMain[1] - macdSignal[1];
            return (MathAbs(currentDiff) > MathAbs(previousDiff));
      }
      return false;
}

//+------------------------------------------------------------------+
//| 开仓买入                                                          |
//+------------------------------------------------------------------+
void OpenBuyPosition(double lotSize, double atr)
{
      // 计算止损价格
      double stopLoss = SymbolInfoDouble(_Symbol, SYMBOL_BID) - atr * StopLossATRMultiplier * symbolPoint;
       
      // 确保止损距离符合最小要求
      double minStopDistance = symbolTradeStopsLevel * symbolPoint * MinStopDistanceMultiplier;
      double currentStopDistance = SymbolInfoDouble(_Symbol, SYMBOL_BID) - stopLoss;
      if(currentStopDistance < minStopDistance)
            stopLoss = SymbolInfoDouble(_Symbol, SYMBOL_BID) - minStopDistance;
       
      // 计算加仓倍数
      double multiplier = GetLayerMultiplier(currentLayer);
      double adjustedLotSize = lotSize * multiplier;
       
      // 检查总仓位大小
      if(CheckTotalPositionSize(adjustedLotSize))
      {
            // 执行买入
            if(trade.Buy(adjustedLotSize, _Symbol, 0, stopLoss, 0))
            {
                  currentLayer++;
                  Print("买入开仓成功，手数: ", adjustedLotSize, ", 层数: ", currentLayer);
            }
            else
            {
                  Print("买入开仓失败，错误: ", GetLastError());
            }
      }
}

//+------------------------------------------------------------------+
//| 开仓卖出                                                          |
//+------------------------------------------------------------------+
void OpenSellPosition(double lotSize, double atr)
{
      // 计算止损价格
      double stopLoss = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + atr * StopLossATRMultiplier * symbolPoint;
       
      // 确保止损距离符合最小要求
      double minStopDistance = symbolTradeStopsLevel * symbolPoint * MinStopDistanceMultiplier;
      double currentStopDistance = stopLoss - SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      if(currentStopDistance < minStopDistance)
            stopLoss = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + minStopDistance;
       
      // 计算加仓倍数
      double multiplier = GetLayerMultiplier(currentLayer);
      double adjustedLotSize = lotSize * multiplier;
       
      // 检查总仓位大小
      if(CheckTotalPositionSize(adjustedLotSize))
      {
            // 执行卖出
            if(trade.Sell(adjustedLotSize, _Symbol, 0, stopLoss, 0))
            {
                  currentLayer++;
                  Print("卖出开仓成功，手数: ", adjustedLotSize, ", 层数: ", currentLayer);
            }
            else
            {
                  Print("卖出开仓失败，错误: ", GetLastError());
            }
      }
}

//+------------------------------------------------------------------+
//| 获取加仓倍数                                                      |
//+------------------------------------------------------------------+
double GetLayerMultiplier(int layer)
{
      // 初期加仓倍数
      if(layer < MidLayerStart)
            return InitialLayerMultiplier;
       
      // 中期加仓倍数
      if(layer < LateLayerStart)
            return MidLayerMultiplier;
       
      // 后期加仓倍数
      return LateLayerMultiplier;
}

//+------------------------------------------------------------------+
//| 检查总仓位大小                                                    |
//+------------------------------------------------------------------+
bool CheckTotalPositionSize(double newLotSize)
{
      double totalLots = GetTotalPositionSize() + newLotSize;
      double maxAllowedLots = (AccountInfoDouble(ACCOUNT_EQUITY) / 100000.0) * MaxPositionSizePercent;
       
      if(totalLots > maxAllowedLots)
      {
            Print("总仓位 ", totalLots, " 超过最大允许值 ", maxAllowedLots);
            return false;
      }
       
      return true;
}

//+------------------------------------------------------------------+
//| 获取当前总仓位大小                                                |
//+------------------------------------------------------------------+
double GetTotalPositionSize()
{
      double totalLots = 0;
      for(int i = 0; i < PositionsTotal(); i++)
      {
            if(PositionGetSymbol(i) == _Symbol)
                  totalLots += PositionGetDouble(POSITION_VOLUME);
      }
      return totalLots;
}

//+------------------------------------------------------------------+
//| 管理已有仓位                                                      |
//+------------------------------------------------------------------+
void ManageOpenPositions(double atr)
{
      // 检查是否达到止盈目标
      CheckProfitTarget();
       
      // 更新止损
      UpdateStopLosses(atr);
}

//+------------------------------------------------------------------+
//| 检查是否达到止盈目标                                              |
//+------------------------------------------------------------------+
void CheckProfitTarget()
{
      // 计算当前利润
      double currentProfit = currentEquity - initialEquity;
       
      // 根据当前层数调整止盈目标
      double profitTarget = BaseProfitTarget * (1 + (currentLayer * 0.1));
       
      // 如果达到止盈目标，平掉所有仓位
      if(currentProfit >= profitTarget)
      {
            CloseAllPositions();
            Print("达到止盈目标: $", profitTarget, ", 平掉所有仓位");
            
            // 重置交易变量
            currentLayer = 0;
            lastBuyPrice = 0;
            lastSellPrice = 0;
            initialEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      }
}

//+------------------------------------------------------------------+
//| 更新止损                                                          |
//+------------------------------------------------------------------+
void UpdateStopLosses(double atr)
{
      for(int i = 0; i < PositionsTotal(); i++)
      {
            // 只处理当前交易品种的仓位
            if(PositionGetSymbol(i) != _Symbol) continue;
            
            // 获取仓位信息
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentSL = PositionGetDouble(POSITION_SL);
            double currentTP = PositionGetDouble(POSITION_TP);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            // 计算新的止损价格
            double newSL = currentSL;
            
            // 买入仓位的止损管理
            if(posType == POSITION_TYPE_BUY)
            {
                  double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                  
                  // 保本设置
                  if(currentPrice >= openPrice + BreakEvenPoints * symbolPoint && 
                     (currentSL < openPrice || currentSL == 0))
                  {
                        newSL = openPrice + 10 * symbolPoint; // 保本加10点利润
                  }
                  
                  // 跟踪止损
                  if(currentSL > openPrice) // 已经在盈利区域
                  {
                        double potentialSL = currentPrice - TrailPoints * symbolPoint;
                        if(potentialSL > currentSL)
                              newSL = potentialSL;
                  }
            }
            // 卖出仓位的止损管理
            else if(posType == POSITION_TYPE_SELL)
            {
                  double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                  
                  // 保本设置
                  if(currentPrice <= openPrice - BreakEvenPoints * symbolPoint && 
                     (currentSL > openPrice || currentSL == 0))
                  {
                        newSL = openPrice - 10 * symbolPoint; // 保本加10点利润
                  }
                  
                  // 跟踪止损
                  if(currentSL < openPrice && currentSL != 0) // 已经在盈利区域
                  {
                        double potentialSL = currentPrice + TrailPoints * symbolPoint;
                        if(potentialSL < currentSL || currentSL == 0)
                              newSL = potentialSL;
                  }
            }
            
            // 如果止损价格有变化，更新止损
            if(newSL != currentSL)
                  trade.PositionModify(ticket, newSL, currentTP);
      }
}

//+------------------------------------------------------------------+
//| 手动开仓函数 - 买入                                               |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
      MqlTick last_tick;
      if(!SymbolInfoTick(Symbol(), last_tick))
      {
            Print("获取市场价格失败!");
            return;
      }
      
      // 使用ATR计算止损
      double atr = GetATRValue();
      double stopLoss = last_tick.bid - atr * StopLossATRMultiplier * symbolPoint;
      
      // 确保止损距离符合最小要求
      double minStopDistance = symbolTradeStopsLevel * symbolPoint * MinStopDistanceMultiplier;
      double currentStopDistance = last_tick.bid - stopLoss;
      if(currentStopDistance < minStopDistance)
            stopLoss = last_tick.bid - minStopDistance;
      
      if(!trade.Buy(ManualOrderVolume, Symbol(), last_tick.ask, stopLoss, 0, "手动买入"))
      {
            Print("买入订单执行失败! 错误代码: ", GetLastError());
            return;
      }
      
      Print("买入订单执行成功! 订单号: ", trade.ResultOrder(), ", 成交价: ", trade.ResultPrice());
}

//+------------------------------------------------------------------+
//| 手动开仓函数 - 卖出                                               |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
      MqlTick last_tick;
      if(!SymbolInfoTick(Symbol(), last_tick))
      {
            Print("获取市场价格失败!");
            return;
      }
      
      // 使用ATR计算止损
      double atr = GetATRValue();
      double stopLoss = last_tick.ask + atr * StopLossATRMultiplier * symbolPoint;
      
      // 确保止损距离符合最小要求
      double minStopDistance = symbolTradeStopsLevel * symbolPoint * MinStopDistanceMultiplier;
      double currentStopDistance = stopLoss - last_tick.ask;
      if(currentStopDistance < minStopDistance)
            stopLoss = last_tick.ask + minStopDistance;
      
      if(!trade.Sell(ManualOrderVolume, Symbol(), last_tick.bid, stopLoss, 0, "手动卖出"))
      {
            Print("卖出订单执行失败! 错误代码: ", GetLastError());
            return;
      }
      
      Print("卖出订单执行成功! 订单号: ", trade.ResultOrder(), ", 成交价: ", trade.ResultPrice());
}

//+------------------------------------------------------------------+
//| 平仓当前品种所有持仓                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
      trade.SetDeviationInPoints(INT_MAX);
      trade.SetAsyncMode(true);
      trade.SetMarginMode();
      trade.LogLevel(LOG_LEVEL_ERRORS);

      for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
      {
            bool allClosed = true;
            m_arr_tickets.Shutdown();

            int total = PositionsTotal();
            for(int i = total - 1; i >= 0; i--)
            {
                  if(positionInfo.SelectByIndex(i) && positionInfo.Symbol() == Symbol())
                  {
                        m_arr_tickets.Add(positionInfo.Ticket());
                  }
            }

            int ticketCount = m_arr_tickets.Total();
            for(int i = 0; i < ticketCount && !IsStopped(); i++)
            {
                  ulong ticket = m_arr_tickets.At(i);
                  if(positionInfo.SelectByTicket(ticket)) 
                  {
                        int freeze_level = (int)SymbolInfoInteger(positionInfo.Symbol(), SYMBOL_TRADE_FREEZE_LEVEL);
                        double point = SymbolInfoDouble(positionInfo.Symbol(), SYMBOL_POINT);
                        bool TP_check = (MathAbs(positionInfo.PriceCurrent() - positionInfo.TakeProfit()) > freeze_level * point);
                        bool SL_check = (MathAbs(positionInfo.PriceCurrent() - positionInfo.StopLoss()) > freeze_level * point);

                        if(TP_check && SL_check)
                        {
                              trade.SetExpertMagicNumber(positionInfo.Magic());
                              trade.SetTypeFillingBySymbol(positionInfo.Symbol());
                              
                              if(trade.PositionClose(ticket) && 
                                 (trade.ResultRetcode() == TRADE_RETCODE_DONE || 
                                  trade.ResultRetcode() == TRADE_RETCODE_PLACED))
                              {
                                    PrintFormat("当前品种持仓 #%I64u 已平仓", ticket);
                                    PlaySound("expert.wav"); 
                              }
                              else 
                              {
                                    PrintFormat("平仓失败 #%I64u: 错误代码=%u (%s)", 
                                             ticket, trade.ResultRetcode(), trade.ResultComment());
                                    allClosed = false;
                              }
                        }
                        else 
                        {
                              PrintFormat("无法平仓 #%I64u: 止损或止盈距离太近 [冻结]", ticket);
                              allClosed = false;
                        }
                  }
            }

            if(allClosed)
                  break;

            Sleep(SLEEPTIME);
            PlaySound("timeout.wav"); 
      }
      
      // 重置交易变量
      currentLayer = 0;
      lastBuyPrice = 0;
      lastSellPrice = 0;
}