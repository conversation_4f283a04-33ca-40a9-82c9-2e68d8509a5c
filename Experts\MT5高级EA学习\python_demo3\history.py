# 导入MT5的模块
import MetaTrader5 as mt5


# 连接MT5软件
def login(path, server, username, password, timeout):
    is_ok = mt5.initialize(path=path,
                           server=server,
                           login=username,
                           password=password,
                           timeout=timeout)
    if not is_ok:
        print("连接MT5失败, 错误原因: ", mt5.last_error())
        mt5.shutdown()
        return False
    else:
        return True


# 主动断开连接
def stop():
    mt5.shutdown()