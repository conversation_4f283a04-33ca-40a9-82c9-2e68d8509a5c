import time

import MetaTrader5 as mt5
from tools.order import *
import datetime


if __name__ == '__main__':
    path = "D:\\MetaTrader 5\\terminal64.exe"
    if not login(path=path,
                 server="Exness-MT5Trial5",
                 username=76314481,
                 password="6ixuhbWp",
                 timeout=2000):
        quit()

    delta = datetime.timedelta(seconds=120)
    dt = (datetime.datetime.now() + delta).timetuple()
    sec = int(time.mktime(dt))
    print(sec)

    symbol = "EURUSDz"
    magic = 8199231
    symbol_info:mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        stop()

    m_price = symbol_info.ask - 100 * symbol_info.point

    buy_limit(symbol,  # 交易品种
               m_price,  # 挂单价格
               0.01,  # 挂单手数
               0,  # 挂单止损点数
               0,  # 挂单止盈点数
               5,  # 挂单成交时的滑点值
               0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
               0,  # 挂单到期时间的模式
               sec,  # 挂单到期时间(描述)
               "buy limit",
               magic
               )

    # 修改制定方向订单
    m_price = symbol_info.ask - 200 * symbol_info.point
    m_sl = m_price - 100 * symbol_info.point
    m_tp = m_price + 150 * symbol_info.point

    delta = datetime.timedelta(seconds=800)
    dt = (datetime.datetime.now() + delta).timetuple()
    sec = int(time.mktime(dt))

    modify_order(symbol, 2, magic,
                 m_price,
                 m_sl,
                 m_tp,
                 0,
                 2,
                 sec
                 )

    # sell
    m_price = symbol_info.ask + 100 * symbol_info.point

    sell_limit(symbol,  # 交易品种
              m_price,  # 挂单价格
              0.01,  # 挂单手数
              0,  # 挂单止损点数
              0,  # 挂单止盈点数
              5,  # 挂单成交时的滑点值
              0,  # stop_limit模式: 先突破指定价格(price),回踩指定价格(stop_limit)
              0,  # 挂单到期时间的模式
              sec,  # 挂单到期时间(描述)
              "sell limit",
              magic
              )

    # 修改制定方向订单
    m_price = symbol_info.ask + 100 * symbol_info.point
    m_sl = m_price + 100 * symbol_info.point
    m_tp = m_price - 150 * symbol_info.point

    delta = datetime.timedelta(seconds=800)
    dt = (datetime.datetime.now() + delta).timetuple()
    sec = int(time.mktime(dt))

    modify_order(symbol, 3, magic,
                 m_price,
                 m_sl,
                 0,
                 0,
                 2,
                 sec
                 )


    # 删除指定方向挂单
    delete_order(symbol, 2, 0)

    delete_order(symbol, 3, 0)