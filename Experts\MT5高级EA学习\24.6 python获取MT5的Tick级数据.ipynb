{"cells": [{"cell_type": "code", "execution_count": 72, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 导入MT5的模块\n", "import MetaTrader5 as mt5\n", "from pprint import pprint\n", "import datetime\n", "import pytz\n", "import numpy as np\n", "import pandas as pd\n", "\n", "if __name__ in \"__main__\":\n", "    path = \"D:\\\\MetaTrader 5\\\\terminal64.exe\"\n", "    # 第一种连接方式\n", "    is_ok = mt5.initialize(path=path,\n", "                           server=\"Exness-MT5Trial5\",\n", "                           login=76314481,\n", "                           password=\"6ixuhbWp\",\n", "                           timeout=2000)\n", "\n", "    if not is_ok:\n", "        print(\"连接MT5失败, 错误原因: \", mt5.last_error())\n", "        mt5.shutdown()\n", "        quit()"]}, {"cell_type": "code", "execution_count": 73, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-04-19 07:45:51.520693+00:00\n"]}], "source": ["utc_tzone = pytz.timezone(\"UTC\") # Asia/Shanghai\n", "utc_time = datetime.datetime.now(tz=utc_tzone)\n", "delta = datetime.<PERSON><PERSON><PERSON>(minutes=-10)\n", "start_time = utc_time + delta\n", "print(start_time)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 74, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'numpy.ndarray'>\n"]}], "source": ["# 根据开始时间往后获取数据\n", "# ticks = mt5.copy_ticks_from(\"EURUSDz\",start_time,0,mt5.COPY_TICKS_ALL)\n", "# 根据指定范围获取数据\n", "delta = datetime.<PERSON><PERSON><PERSON>(minutes=-1)\n", "start_time = utc_time + delta\n", "stop_time = utc_time\n", "ticks = mt5.copy_ticks_range(\"EURUSDz\",start_time,stop_time,mt5.COPY_TICKS_ALL)\n", "print(type(ticks))"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 75, "outputs": [{"data": {"text/plain": "          time      bid      ask  last  volume       time_msc  flags   \n0   1681890891  1.09620  1.09620   0.0       0  1681890891402      6  \\\n1   1681890894  1.09621  1.09621   0.0       0  1681890894573      6   \n2   1681890899  1.09622  1.09622   0.0       0  1681890899616      6   \n3   1681890904  1.09624  1.09624   0.0       0  1681890904279      6   \n4   1681890904  1.09625  1.09625   0.0       0  1681890904467      6   \n5   1681890910  1.09628  1.09628   0.0       0  1681890910352      6   \n6   1681890910  1.09629  1.09629   0.0       0  1681890910380      6   \n7   1681890910  1.09631  1.09631   0.0       0  1681890910428      6   \n8   1681890910  1.09632  1.09632   0.0       0  1681890910454      6   \n9   1681890910  1.09634  1.09634   0.0       0  1681890910485      6   \n10  1681890910  1.09633  1.09633   0.0       0  1681890910848      6   \n11  1681890912  1.09635  1.09635   0.0       0  1681890912379      6   \n12  1681890914  1.09633  1.09633   0.0       0  1681890914497      6   \n13  1681890914  1.09632  1.09632   0.0       0  1681890914625      6   \n14  1681890918  1.09631  1.09631   0.0       0  1681890918580      6   \n15  1681890920  1.09633  1.09633   0.0       0  1681890920407      6   \n16  1681890920  1.09635  1.09635   0.0       0  1681890920437      6   \n17  1681890926  1.09636  1.09636   0.0       0  1681890926351      6   \n18  1681890929  1.09634  1.09634   0.0       0  1681890929347      6   \n19  1681890929  1.09635  1.09635   0.0       0  1681890929606      6   \n20  1681890929  1.09637  1.09637   0.0       0  1681890929642      6   \n21  1681890929  1.09638  1.09638   0.0       0  1681890929794      6   \n22  1681890930  1.09639  1.09639   0.0       0  1681890930249      6   \n23  1681890930  1.09641  1.09641   0.0       0  1681890930269      6   \n24  1681890930  1.09642  1.09642   0.0       0  1681890930315      6   \n25  1681890935  1.09640  1.09640   0.0       0  1681890935329      6   \n26  1681890936  1.09638  1.09638   0.0       0  1681890936911      6   \n27  1681890938  1.09637  1.09637   0.0       0  1681890938988      6   \n28  1681890939  1.09638  1.09638   0.0       0  1681890939958      6   \n29  1681890940  1.09637  1.09637   0.0       0  1681890940821      6   \n30  1681890941  1.09635  1.09635   0.0       0  1681890941917      6   \n31  1681890943  1.09634  1.09634   0.0       0  1681890943851      6   \n32  1681890948  1.09633  1.09633   0.0       0  1681890948302      6   \n33  1681890948  1.09631  1.09631   0.0       0  1681890948413      6   \n34  1681890948  1.09630  1.09630   0.0       0  1681890948473      6   \n35  1681890949  1.09629  1.09629   0.0       0  1681890949312      6   \n36  1681890949  1.09628  1.09628   0.0       0  1681890949374      6   \n37  1681890949  1.09626  1.09626   0.0       0  1681890949737      6   \n38  1681890950  1.09625  1.09625   0.0       0  1681890950436      6   \n39  1681890950  1.09624  1.09624   0.0       0  1681890950971      6   \n\n    volume_real  \n0           0.0  \n1           0.0  \n2           0.0  \n3           0.0  \n4           0.0  \n5           0.0  \n6           0.0  \n7           0.0  \n8           0.0  \n9           0.0  \n10          0.0  \n11          0.0  \n12          0.0  \n13          0.0  \n14          0.0  \n15          0.0  \n16          0.0  \n17          0.0  \n18          0.0  \n19          0.0  \n20          0.0  \n21          0.0  \n22          0.0  \n23          0.0  \n24          0.0  \n25          0.0  \n26          0.0  \n27          0.0  \n28          0.0  \n29          0.0  \n30          0.0  \n31          0.0  \n32          0.0  \n33          0.0  \n34          0.0  \n35          0.0  \n36          0.0  \n37          0.0  \n38          0.0  \n39          0.0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>bid</th>\n      <th>ask</th>\n      <th>last</th>\n      <th>volume</th>\n      <th>time_msc</th>\n      <th>flags</th>\n      <th>volume_real</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1681890891</td>\n      <td>1.09620</td>\n      <td>1.09620</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890891402</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>1681890894</td>\n      <td>1.09621</td>\n      <td>1.09621</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890894573</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>1681890899</td>\n      <td>1.09622</td>\n      <td>1.09622</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890899616</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>1681890904</td>\n      <td>1.09624</td>\n      <td>1.09624</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890904279</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>1681890904</td>\n      <td>1.09625</td>\n      <td>1.09625</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890904467</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>1681890910</td>\n      <td>1.09628</td>\n      <td>1.09628</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910352</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>1681890910</td>\n      <td>1.09629</td>\n      <td>1.09629</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910380</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>1681890910</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910428</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>1681890910</td>\n      <td>1.09632</td>\n      <td>1.09632</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910454</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>1681890910</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910485</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>1681890910</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910848</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>1681890912</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890912379</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>1681890914</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890914497</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>1681890914</td>\n      <td>1.09632</td>\n      <td>1.09632</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890914625</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>1681890918</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890918580</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>1681890920</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890920407</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>1681890920</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890920437</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>1681890926</td>\n      <td>1.09636</td>\n      <td>1.09636</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890926351</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>1681890929</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929347</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>1681890929</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929606</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>1681890929</td>\n      <td>1.09637</td>\n      <td>1.09637</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929642</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>1681890929</td>\n      <td>1.09638</td>\n      <td>1.09638</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929794</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>1681890930</td>\n      <td>1.09639</td>\n      <td>1.09639</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890930249</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>1681890930</td>\n      <td>1.09641</td>\n      <td>1.09641</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890930269</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>24</th>\n      <td>1681890930</td>\n      <td>1.09642</td>\n      <td>1.09642</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890930315</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>25</th>\n      <td>1681890935</td>\n      <td>1.09640</td>\n      <td>1.09640</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890935329</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>26</th>\n      <td>1681890936</td>\n      <td>1.09638</td>\n      <td>1.09638</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890936911</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>27</th>\n      <td>1681890938</td>\n      <td>1.09637</td>\n      <td>1.09637</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890938988</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>28</th>\n      <td>1681890939</td>\n      <td>1.09638</td>\n      <td>1.09638</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890939958</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>29</th>\n      <td>1681890940</td>\n      <td>1.09637</td>\n      <td>1.09637</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890940821</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>30</th>\n      <td>1681890941</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890941917</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>31</th>\n      <td>1681890943</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890943851</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>32</th>\n      <td>1681890948</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948302</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>33</th>\n      <td>1681890948</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948413</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>34</th>\n      <td>1681890948</td>\n      <td>1.09630</td>\n      <td>1.09630</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948473</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>35</th>\n      <td>1681890949</td>\n      <td>1.09629</td>\n      <td>1.09629</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949312</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>36</th>\n      <td>1681890949</td>\n      <td>1.09628</td>\n      <td>1.09628</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949374</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>37</th>\n      <td>1681890949</td>\n      <td>1.09626</td>\n      <td>1.09626</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949737</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>38</th>\n      <td>1681890950</td>\n      <td>1.09625</td>\n      <td>1.09625</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890950436</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>39</th>\n      <td>1681890950</td>\n      <td>1.09624</td>\n      <td>1.09624</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890950971</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(ticks)\n", "df"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 76, "outputs": [{"data": {"text/plain": "                  time      bid      ask  last  volume       time_msc  flags   \n0  2023-04-19 07:54:51  1.09620  1.09620   0.0       0  1681890891402      6  \\\n1  2023-04-19 07:54:54  1.09621  1.09621   0.0       0  1681890894573      6   \n2  2023-04-19 07:54:59  1.09622  1.09622   0.0       0  1681890899616      6   \n3  2023-04-19 07:55:04  1.09624  1.09624   0.0       0  1681890904279      6   \n4  2023-04-19 07:55:04  1.09625  1.09625   0.0       0  1681890904467      6   \n5  2023-04-19 07:55:10  1.09628  1.09628   0.0       0  1681890910352      6   \n6  2023-04-19 07:55:10  1.09629  1.09629   0.0       0  1681890910380      6   \n7  2023-04-19 07:55:10  1.09631  1.09631   0.0       0  1681890910428      6   \n8  2023-04-19 07:55:10  1.09632  1.09632   0.0       0  1681890910454      6   \n9  2023-04-19 07:55:10  1.09634  1.09634   0.0       0  1681890910485      6   \n10 2023-04-19 07:55:10  1.09633  1.09633   0.0       0  1681890910848      6   \n11 2023-04-19 07:55:12  1.09635  1.09635   0.0       0  1681890912379      6   \n12 2023-04-19 07:55:14  1.09633  1.09633   0.0       0  1681890914497      6   \n13 2023-04-19 07:55:14  1.09632  1.09632   0.0       0  1681890914625      6   \n14 2023-04-19 07:55:18  1.09631  1.09631   0.0       0  1681890918580      6   \n15 2023-04-19 07:55:20  1.09633  1.09633   0.0       0  1681890920407      6   \n16 2023-04-19 07:55:20  1.09635  1.09635   0.0       0  1681890920437      6   \n17 2023-04-19 07:55:26  1.09636  1.09636   0.0       0  1681890926351      6   \n18 2023-04-19 07:55:29  1.09634  1.09634   0.0       0  1681890929347      6   \n19 2023-04-19 07:55:29  1.09635  1.09635   0.0       0  1681890929606      6   \n20 2023-04-19 07:55:29  1.09637  1.09637   0.0       0  1681890929642      6   \n21 2023-04-19 07:55:29  1.09638  1.09638   0.0       0  1681890929794      6   \n22 2023-04-19 07:55:30  1.09639  1.09639   0.0       0  1681890930249      6   \n23 2023-04-19 07:55:30  1.09641  1.09641   0.0       0  1681890930269      6   \n24 2023-04-19 07:55:30  1.09642  1.09642   0.0       0  1681890930315      6   \n25 2023-04-19 07:55:35  1.09640  1.09640   0.0       0  1681890935329      6   \n26 2023-04-19 07:55:36  1.09638  1.09638   0.0       0  1681890936911      6   \n27 2023-04-19 07:55:38  1.09637  1.09637   0.0       0  1681890938988      6   \n28 2023-04-19 07:55:39  1.09638  1.09638   0.0       0  1681890939958      6   \n29 2023-04-19 07:55:40  1.09637  1.09637   0.0       0  1681890940821      6   \n30 2023-04-19 07:55:41  1.09635  1.09635   0.0       0  1681890941917      6   \n31 2023-04-19 07:55:43  1.09634  1.09634   0.0       0  1681890943851      6   \n32 2023-04-19 07:55:48  1.09633  1.09633   0.0       0  1681890948302      6   \n33 2023-04-19 07:55:48  1.09631  1.09631   0.0       0  1681890948413      6   \n34 2023-04-19 07:55:48  1.09630  1.09630   0.0       0  1681890948473      6   \n35 2023-04-19 07:55:49  1.09629  1.09629   0.0       0  1681890949312      6   \n36 2023-04-19 07:55:49  1.09628  1.09628   0.0       0  1681890949374      6   \n37 2023-04-19 07:55:49  1.09626  1.09626   0.0       0  1681890949737      6   \n38 2023-04-19 07:55:50  1.09625  1.09625   0.0       0  1681890950436      6   \n39 2023-04-19 07:55:50  1.09624  1.09624   0.0       0  1681890950971      6   \n\n    volume_real  \n0           0.0  \n1           0.0  \n2           0.0  \n3           0.0  \n4           0.0  \n5           0.0  \n6           0.0  \n7           0.0  \n8           0.0  \n9           0.0  \n10          0.0  \n11          0.0  \n12          0.0  \n13          0.0  \n14          0.0  \n15          0.0  \n16          0.0  \n17          0.0  \n18          0.0  \n19          0.0  \n20          0.0  \n21          0.0  \n22          0.0  \n23          0.0  \n24          0.0  \n25          0.0  \n26          0.0  \n27          0.0  \n28          0.0  \n29          0.0  \n30          0.0  \n31          0.0  \n32          0.0  \n33          0.0  \n34          0.0  \n35          0.0  \n36          0.0  \n37          0.0  \n38          0.0  \n39          0.0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>bid</th>\n      <th>ask</th>\n      <th>last</th>\n      <th>volume</th>\n      <th>time_msc</th>\n      <th>flags</th>\n      <th>volume_real</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2023-04-19 07:54:51</td>\n      <td>1.09620</td>\n      <td>1.09620</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890891402</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2023-04-19 07:54:54</td>\n      <td>1.09621</td>\n      <td>1.09621</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890894573</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2023-04-19 07:54:59</td>\n      <td>1.09622</td>\n      <td>1.09622</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890899616</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2023-04-19 07:55:04</td>\n      <td>1.09624</td>\n      <td>1.09624</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890904279</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2023-04-19 07:55:04</td>\n      <td>1.09625</td>\n      <td>1.09625</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890904467</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>2023-04-19 07:55:10</td>\n      <td>1.09628</td>\n      <td>1.09628</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910352</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>2023-04-19 07:55:10</td>\n      <td>1.09629</td>\n      <td>1.09629</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910380</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>2023-04-19 07:55:10</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910428</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>2023-04-19 07:55:10</td>\n      <td>1.09632</td>\n      <td>1.09632</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910454</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>2023-04-19 07:55:10</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910485</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>2023-04-19 07:55:10</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890910848</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>2023-04-19 07:55:12</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890912379</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>2023-04-19 07:55:14</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890914497</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>2023-04-19 07:55:14</td>\n      <td>1.09632</td>\n      <td>1.09632</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890914625</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>2023-04-19 07:55:18</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890918580</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>2023-04-19 07:55:20</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890920407</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>2023-04-19 07:55:20</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890920437</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>2023-04-19 07:55:26</td>\n      <td>1.09636</td>\n      <td>1.09636</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890926351</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>2023-04-19 07:55:29</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929347</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>2023-04-19 07:55:29</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929606</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>2023-04-19 07:55:29</td>\n      <td>1.09637</td>\n      <td>1.09637</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929642</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>2023-04-19 07:55:29</td>\n      <td>1.09638</td>\n      <td>1.09638</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890929794</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>2023-04-19 07:55:30</td>\n      <td>1.09639</td>\n      <td>1.09639</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890930249</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>2023-04-19 07:55:30</td>\n      <td>1.09641</td>\n      <td>1.09641</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890930269</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>24</th>\n      <td>2023-04-19 07:55:30</td>\n      <td>1.09642</td>\n      <td>1.09642</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890930315</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>25</th>\n      <td>2023-04-19 07:55:35</td>\n      <td>1.09640</td>\n      <td>1.09640</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890935329</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>26</th>\n      <td>2023-04-19 07:55:36</td>\n      <td>1.09638</td>\n      <td>1.09638</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890936911</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>27</th>\n      <td>2023-04-19 07:55:38</td>\n      <td>1.09637</td>\n      <td>1.09637</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890938988</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>28</th>\n      <td>2023-04-19 07:55:39</td>\n      <td>1.09638</td>\n      <td>1.09638</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890939958</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>29</th>\n      <td>2023-04-19 07:55:40</td>\n      <td>1.09637</td>\n      <td>1.09637</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890940821</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>30</th>\n      <td>2023-04-19 07:55:41</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890941917</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>31</th>\n      <td>2023-04-19 07:55:43</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890943851</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>32</th>\n      <td>2023-04-19 07:55:48</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948302</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>33</th>\n      <td>2023-04-19 07:55:48</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948413</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>34</th>\n      <td>2023-04-19 07:55:48</td>\n      <td>1.09630</td>\n      <td>1.09630</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948473</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>35</th>\n      <td>2023-04-19 07:55:49</td>\n      <td>1.09629</td>\n      <td>1.09629</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949312</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>36</th>\n      <td>2023-04-19 07:55:49</td>\n      <td>1.09628</td>\n      <td>1.09628</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949374</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>37</th>\n      <td>2023-04-19 07:55:49</td>\n      <td>1.09626</td>\n      <td>1.09626</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949737</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>38</th>\n      <td>2023-04-19 07:55:50</td>\n      <td>1.09625</td>\n      <td>1.09625</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890950436</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>39</th>\n      <td>2023-04-19 07:55:50</td>\n      <td>1.09624</td>\n      <td>1.09624</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890950971</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["df['time'] = pd.to_datetime(df['time'],unit='s')\n", "df"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 77, "outputs": [{"data": {"text/plain": "                  time      bid      ask  last  volume       time_msc  flags   \n30 2023-04-19 07:55:41  1.09635  1.09635   0.0       0  1681890941917      6  \\\n31 2023-04-19 07:55:43  1.09634  1.09634   0.0       0  1681890943851      6   \n32 2023-04-19 07:55:48  1.09633  1.09633   0.0       0  1681890948302      6   \n33 2023-04-19 07:55:48  1.09631  1.09631   0.0       0  1681890948413      6   \n34 2023-04-19 07:55:48  1.09630  1.09630   0.0       0  1681890948473      6   \n35 2023-04-19 07:55:49  1.09629  1.09629   0.0       0  1681890949312      6   \n36 2023-04-19 07:55:49  1.09628  1.09628   0.0       0  1681890949374      6   \n37 2023-04-19 07:55:49  1.09626  1.09626   0.0       0  1681890949737      6   \n38 2023-04-19 07:55:50  1.09625  1.09625   0.0       0  1681890950436      6   \n39 2023-04-19 07:55:50  1.09624  1.09624   0.0       0  1681890950971      6   \n\n    volume_real  \n30          0.0  \n31          0.0  \n32          0.0  \n33          0.0  \n34          0.0  \n35          0.0  \n36          0.0  \n37          0.0  \n38          0.0  \n39          0.0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>bid</th>\n      <th>ask</th>\n      <th>last</th>\n      <th>volume</th>\n      <th>time_msc</th>\n      <th>flags</th>\n      <th>volume_real</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>30</th>\n      <td>2023-04-19 07:55:41</td>\n      <td>1.09635</td>\n      <td>1.09635</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890941917</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>31</th>\n      <td>2023-04-19 07:55:43</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890943851</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>32</th>\n      <td>2023-04-19 07:55:48</td>\n      <td>1.09633</td>\n      <td>1.09633</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948302</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>33</th>\n      <td>2023-04-19 07:55:48</td>\n      <td>1.09631</td>\n      <td>1.09631</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948413</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>34</th>\n      <td>2023-04-19 07:55:48</td>\n      <td>1.09630</td>\n      <td>1.09630</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890948473</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>35</th>\n      <td>2023-04-19 07:55:49</td>\n      <td>1.09629</td>\n      <td>1.09629</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949312</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>36</th>\n      <td>2023-04-19 07:55:49</td>\n      <td>1.09628</td>\n      <td>1.09628</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949374</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>37</th>\n      <td>2023-04-19 07:55:49</td>\n      <td>1.09626</td>\n      <td>1.09626</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890949737</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>38</th>\n      <td>2023-04-19 07:55:50</td>\n      <td>1.09625</td>\n      <td>1.09625</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890950436</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>39</th>\n      <td>2023-04-19 07:55:50</td>\n      <td>1.09624</td>\n      <td>1.09624</td>\n      <td>0.0</td>\n      <td>0</td>\n      <td>1681890950971</td>\n      <td>6</td>\n      <td>0.0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取最后10个tick数据\n", "df.loc[df.shape[0]-10: df.shape[0]-1]"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 78, "outputs": [{"data": {"text/plain": "time           2023-04-19 07:55:50\nbid                        1.09624\nask                        1.09624\nlast                           0.0\nvolume                           0\ntime_msc             1681890950971\nflags                            6\nvolume_real                    0.0\nName: 39, dtype: object"}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取最后的报价\n", "df.loc[df.shape[0]-1]"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 79, "outputs": [{"data": {"text/plain": "1.09624"}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取最后的报价\n", "df.loc[df.shape[0]-1]['ask']"], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}