# 导入MT5的模块
import MetaTrader5 as mt5


if __name__ in "__main__":
    path = "D:\\MetaTrader 5\\terminal64.exe"
    # 第一种连接方式
    is_ok = mt5.initialize(path=path,
                           server="Exness-MT5Trial5",
                           login=76314481,
                           password="6ixuhbWp",
                           timeout=2000)
    # 检测连接状态
    if is_ok:
        print("MT5连接成功")
    else:
        print("连接失败: {0}".format(mt5.last_error()))

    # 断开MT5连接
    mt5.shutdown()

    # 第二种连接方式
    is_ok2 = mt5.initialize(path=path)
    if is_ok2:
        is_ok3 = mt5.login(server="Exness-MT5Trial5",
                           login=76314481,
                           password="6ixuhbWp"
                           )
        if is_ok3:
            print("第二种方式 MT5连接成功")
        else:
            print("连接失败: {0}".format(mt5.last_error()))

    # 断开MT5连接
    mt5.shutdown()
