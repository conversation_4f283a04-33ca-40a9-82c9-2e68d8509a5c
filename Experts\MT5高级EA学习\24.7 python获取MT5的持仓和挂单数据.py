# 导入MT5的模块
import MetaTrader5 as mt5
from pprint import pprint
import datetime


# 连接MT5软件
def login(path, server, username, password, timeout):
    is_ok = mt5.initialize(path=path,
                           server=server,
                           login=username,
                           password=password,
                           timeout=timeout)
    if not is_ok:
        print("连接MT5失败, 错误原因: ", mt5.last_error())
        mt5.shutdown()
        return False
    else:
        return True


# 主动断开连接
def stop():
    mt5.shutdown()


# 搜索指定订单是否存在
def find_position(group, comment):
    # 获取所有持仓单数据
    positions = mt5.positions_get(group=group)
    if not positions:
        return False
    # pprint(positions)
    for pos in positions:
        m_pos = pos._asdict()
        if m_pos['comment'] == comment:
            return True
    return False


# 统计指定条件的订单数
def position_count(group, type, magic):
    count = 0
    positions = mt5.positions_get(group=group)
    # not == !
    if not positions:
        return 0
    for pos in positions:
        m_pos = pos._asdict()
        # and == &&
        if m_pos['type'] == type and m_pos['magic'] == magic:
            count += 1
        elif type == 12 and m_pos['magic'] == magic:
            count += 1
    return count


# 查询指定的挂单是否存在
def find_order(group,comment):
    orders = mt5.orders_get(group=group)
    if not orders:
        return False
    for order in orders:
        m_order = order._asdict()
        if m_order['comment'] == comment:
            return True

    return False


# 统计指定条件挂单的数量
def order_count(group,type,magic):
    count = 0
    orders = mt5.orders_get(group=group)
    if not orders:
        return 0
    for order in orders:
        m_order = order._asdict()
        if m_order['type'] == type and m_order['magic'] == magic:
            count += 1
        elif type == 12 and m_order['magic'] == magic:
            count += 1
    return count

if __name__ in "__main__":
    path = "D:\\MetaTrader 5\\terminal64.exe"
    if not login(path=path,
             server="Exness-MT5Trial5",
             username=76314481,
             password="6ixuhbWp",
             timeout=2000):
        quit()

    # 获取所有持仓单信息
    total = mt5.positions_total()
    print(total)

    # 获取所有持仓单数据
    # positions = mt5.positions_get(group="*")
    # # pprint(positions)
    # for pos in positions:
    #     m_pos = pos._asdict()
    #     print(m_pos)

    # 查询指定注释订单是否存在
    print(find_position("*", "hello123"))

    # 统计指定条件的订单数量
    print(position_count("*", 0, 0))

    print(position_count("*", 1, 0))

    print(position_count("*", 12, 0))

    # 查询挂单的总数
    order_total = mt5.orders_total()
    print("挂单总数: ",order_total)

    orders = mt5.orders_get(group="*")
    pprint(orders)

    # 搜索指定的注释挂单是否存在
    print(find_order("*", "hello"))

    # 获取指定条件的挂单数量
    print("buy limit: ", order_count("*", 2, 0))

    print("sell limit: ", order_count("*", 3, 0))

    print("buy stop: ", order_count("*", 4, 0))

    print("sell stop: ", order_count("*", 5, 0))