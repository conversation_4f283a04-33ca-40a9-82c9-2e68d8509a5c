//+------------------------------------------------------------------+
//|                                          智能资金管理测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "智能资金管理系统功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input double TestAccountBalance = 10000.0; // 测试账户余额

//+------------------------------------------------------------------+
//| 智能资金管理系统测试脚本                                          |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始智能资金管理系统测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("测试账户余额: ", DoubleToString(TestAccountBalance, 2));
    Print("========================================");
    
    // 测试1: 仓位计算方法
    TestPositionSizingMethods();
    
    // 测试2: 风险管理机制
    TestRiskManagementMechanisms();
    
    // 测试3: 动态止损系统
    TestDynamicStopLossSystem();
    
    // 测试4: 账户指标计算
    TestAccountMetricsCalculation();
    
    // 测试5: 紧急停止机制
    TestEmergencyStopMechanisms();
    
    // 测试6: 完整资金管理流程
    TestCompleteMoneyManagementWorkflow();
    
    Print("========================================");
    Print("✅ 智能资金管理系统测试完成");
}

//+------------------------------------------------------------------+
//| 测试仓位计算方法                                                  |
//+------------------------------------------------------------------+
void TestPositionSizingMethods()
{
    Print("📋 测试1: 仓位计算方法");
    
    // 测试不同仓位计算方法
    struct PositionSizeTest {
        int method;
        double riskPercent;
        double expectedLotSize;
        string description;
    };
    
    PositionSizeTest tests[] = {
        {0, 0.0, 0.10, "固定手数"},
        {1, 2.0, 0.20, "百分比仓位"},
        {2, 2.0, 0.15, "风险基础"},
        {3, 0.0, 0.12, "凯利公式"},
        {4, 2.0, 0.18, "自适应仓位"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        PositionSizeTest pst = tests[i];
        
        // 计算仓位大小
        double calculatedLotSize = CalculateTestPositionSize(pst.method, pst.riskPercent);
        
        Print("仓位计算方法: ", pst.description);
        Print("  方法代码: ", pst.method);
        Print("  风险百分比: ", DoubleToString(pst.riskPercent, 1), "%");
        Print("  计算仓位: ", DoubleToString(calculatedLotSize, 2), "手");
        Print("  预期仓位: ", DoubleToString(pst.expectedLotSize, 2), "手");
        
        bool lotSizeReasonable = (calculatedLotSize >= 0.01 && calculatedLotSize <= 1.0);
        Print("  仓位合理性: ", lotSizeReasonable ? "✓" : "✗");
    }
    
    Print("✅ 仓位计算方法测试完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试仓位大小                                                  |
//+------------------------------------------------------------------+
double CalculateTestPositionSize(int method, double riskPercent) {
    double accountEquity = TestAccountBalance;
    double stopLossDistance = 50.0; // 假设50点止损
    double tickValue = 1.0; // 假设每点价值1美元
    
    switch(method) {
        case 0: // 固定手数
            return 0.10;
        case 1: // 百分比仓位
            {
                double riskAmount = accountEquity * (riskPercent / 100.0);
                return riskAmount / (stopLossDistance * tickValue);
            }
        case 2: // 风险基础
            {
                double riskAmount = accountEquity * (riskPercent / 100.0);
                double volatilityAdjustment = 0.8; // 假设波动率调整
                return (riskAmount * volatilityAdjustment) / (stopLossDistance * tickValue);
            }
        case 3: // 凯利公式
            {
                double winRate = 0.6; // 假设60%胜率
                double avgWin = 100.0; // 假设平均盈利100
                double avgLoss = 50.0; // 假设平均亏损50
                double kellyPercent = (winRate * avgWin - (1 - winRate) * avgLoss) / avgLoss;
                kellyPercent = MathMax(0.0, MathMin(kellyPercent, 0.25)) * 0.25; // 凯利乘数25%
                double kellyAmount = accountEquity * kellyPercent;
                return kellyAmount / (stopLossDistance * tickValue);
            }
        case 4: // 自适应仓位
            {
                double baseRisk = riskPercent / 100.0;
                double marketStateAdjustment = 1.2; // 假设市场状态调整
                double mtfAdjustment = 1.1; // 假设多时间框架调整
                double riskAdjustment = 0.9; // 假设风险调整
                double finalRisk = baseRisk * marketStateAdjustment * mtfAdjustment * riskAdjustment;
                double riskAmount = accountEquity * finalRisk;
                return riskAmount / (stopLossDistance * tickValue);
            }
        default:
            return 0.10;
    }
}

//+------------------------------------------------------------------+
//| 测试风险管理机制                                                  |
//+------------------------------------------------------------------+
void TestRiskManagementMechanisms()
{
    Print("📋 测试2: 风险管理机制");
    
    // 测试不同风险场景
    struct RiskScenario {
        double currentDrawdown;
        double marginLevel;
        double profitFactor;
        int expectedRiskLevel;
        bool expectedEmergencyStop;
        string description;
    };
    
    RiskScenario scenarios[] = {
        {2.0, 800.0, 1.8, 2, false, "低风险场景"},
        {8.0, 400.0, 1.3, 1, false, "中等风险场景"},
        {15.0, 250.0, 0.9, 0, false, "高风险场景"},
        {25.0, 150.0, 0.6, 0, true, "极高风险场景"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        RiskScenario rs = scenarios[i];
        
        // 评估风险等级
        int calculatedRiskLevel = CalculateTestRiskLevel(rs.currentDrawdown, rs.profitFactor);
        
        // 检查紧急停止条件
        bool calculatedEmergencyStop = CheckTestEmergencyStop(rs.currentDrawdown, rs.marginLevel);
        
        Print("风险场景: ", rs.description);
        Print("  当前回撤: ", DoubleToString(rs.currentDrawdown, 1), "%");
        Print("  保证金水平: ", DoubleToString(rs.marginLevel, 1), "%");
        Print("  盈利因子: ", DoubleToString(rs.profitFactor, 2));
        Print("  计算风险等级: ", calculatedRiskLevel, " (", GetTestRiskLevelDescription(calculatedRiskLevel), ")");
        Print("  预期风险等级: ", rs.expectedRiskLevel, " (", GetTestRiskLevelDescription(rs.expectedRiskLevel), ")");
        Print("  计算紧急停止: ", calculatedEmergencyStop ? "是" : "否");
        Print("  预期紧急停止: ", rs.expectedEmergencyStop ? "是" : "否");
        
        bool riskLevelCorrect = (calculatedRiskLevel == rs.expectedRiskLevel);
        bool emergencyStopCorrect = (calculatedEmergencyStop == rs.expectedEmergencyStop);
        
        Print("  风险等级验证: ", riskLevelCorrect ? "✓" : "✗");
        Print("  紧急停止验证: ", emergencyStopCorrect ? "✓" : "✗");
    }
    
    Print("✅ 风险管理机制测试完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试风险等级                                                  |
//+------------------------------------------------------------------+
int CalculateTestRiskLevel(double currentDrawdown, double profitFactor) {
    if(currentDrawdown <= 5.0 && profitFactor >= 1.5) {
        return 2; // 激进风险
    } else if(currentDrawdown <= 10.0 && profitFactor >= 1.2) {
        return 1; // 中等风险
    } else {
        return 0; // 保守风险
    }
}

//+------------------------------------------------------------------+
//| 检查测试紧急停止                                                  |
//+------------------------------------------------------------------+
bool CheckTestEmergencyStop(double currentDrawdown, double marginLevel) {
    // 检查回撤阈值 (20%)
    if(currentDrawdown >= 20.0) return true;
    
    // 检查保证金水平 (200%)
    if(marginLevel < 200.0) return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| 获取测试风险等级描述                                              |
//+------------------------------------------------------------------+
string GetTestRiskLevelDescription(int level) {
    switch(level) {
        case 0: return "保守";
        case 1: return "中等";
        case 2: return "激进";
        case 3: return "动态";
        default: return "未知";
    }
}

//+------------------------------------------------------------------+
//| 测试动态止损系统                                                  |
//+------------------------------------------------------------------+
void TestDynamicStopLossSystem()
{
    Print("📋 测试3: 动态止损系统");
    
    // 测试动态止损场景
    struct StopLossScenario {
        double entryPrice;
        double currentPrice;
        double initialStopLoss;
        double trailingStep;
        double expectedNewStopLoss;
        bool isBuyPosition;
        string description;
    };
    
    StopLossScenario scenarios[] = {
        {1.10000, 1.10050, 1.09950, 0.00010, 1.09960, true, "多头小幅盈利"},
        {1.10000, 1.10100, 1.09950, 0.00010, 1.10010, true, "多头大幅盈利"},
        {1.10000, 1.09950, 1.10050, 0.00010, 1.10040, false, "空头小幅盈利"},
        {1.10000, 1.09900, 1.10050, 0.00010, 1.09990, false, "空头大幅盈利"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        StopLossScenario sls = scenarios[i];
        
        // 计算新的止损价格
        double calculatedStopLoss = CalculateTestDynamicStopLoss(sls.entryPrice, sls.currentPrice, 
                                                               sls.initialStopLoss, sls.trailingStep, 
                                                               sls.isBuyPosition);
        
        Print("动态止损场景: ", sls.description);
        Print("  入场价格: ", DoubleToString(sls.entryPrice, 5));
        Print("  当前价格: ", DoubleToString(sls.currentPrice, 5));
        Print("  初始止损: ", DoubleToString(sls.initialStopLoss, 5));
        Print("  跟踪步长: ", DoubleToString(sls.trailingStep, 5));
        Print("  计算新止损: ", DoubleToString(calculatedStopLoss, 5));
        Print("  预期新止损: ", DoubleToString(sls.expectedNewStopLoss, 5));
        
        bool stopLossReasonable = (MathAbs(calculatedStopLoss - sls.expectedNewStopLoss) < 0.0002);
        Print("  止损合理性: ", stopLossReasonable ? "✓" : "✗");
    }
    
    Print("✅ 动态止损系统测试完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试动态止损                                                  |
//+------------------------------------------------------------------+
double CalculateTestDynamicStopLoss(double entryPrice, double currentPrice, double initialStopLoss, 
                                   double trailingStep, bool isBuyPosition) {
    if(isBuyPosition) {
        // 多头持仓
        double unrealizedProfit = currentPrice - entryPrice;
        if(unrealizedProfit > 0) {
            double maxFavorableExcursion = unrealizedProfit;
            double newStopLoss = entryPrice + (maxFavorableExcursion - trailingStep);
            return MathMax(newStopLoss, initialStopLoss);
        }
    } else {
        // 空头持仓
        double unrealizedProfit = entryPrice - currentPrice;
        if(unrealizedProfit > 0) {
            double maxFavorableExcursion = unrealizedProfit;
            double newStopLoss = entryPrice - (maxFavorableExcursion - trailingStep);
            return MathMin(newStopLoss, initialStopLoss);
        }
    }
    
    return initialStopLoss;
}

//+------------------------------------------------------------------+
//| 测试账户指标计算                                                  |
//+------------------------------------------------------------------+
void TestAccountMetricsCalculation()
{
    Print("📋 测试4: 账户指标计算");
    
    // 模拟交易结果数据
    double tradeResults[] = {100, -50, 150, -30, 200, -80, 120, -40, 180, -60};
    
    // 计算盈利因子
    double totalProfit = 0.0;
    double totalLoss = 0.0;
    int winCount = 0;
    int lossCount = 0;
    
    for(int i = 0; i < ArraySize(tradeResults); i++) {
        if(tradeResults[i] > 0) {
            totalProfit += tradeResults[i];
            winCount++;
        } else {
            totalLoss += MathAbs(tradeResults[i]);
            lossCount++;
        }
    }
    
    double profitFactor = (totalLoss > 0) ? totalProfit / totalLoss : 999.0;
    double winRate = (double)winCount / ArraySize(tradeResults) * 100.0;
    double avgWin = (winCount > 0) ? totalProfit / winCount : 0.0;
    double avgLoss = (lossCount > 0) ? totalLoss / lossCount : 0.0;
    
    // 计算夏普比率
    double mean = 0.0;
    for(int i = 0; i < ArraySize(tradeResults); i++) {
        mean += tradeResults[i];
    }
    mean = mean / ArraySize(tradeResults);
    
    double variance = 0.0;
    for(int i = 0; i < ArraySize(tradeResults); i++) {
        variance += MathPow(tradeResults[i] - mean, 2);
    }
    variance = variance / (ArraySize(tradeResults) - 1);
    double stdDev = MathSqrt(variance);
    double sharpeRatio = (stdDev > 0) ? mean / stdDev : 0.0;
    
    Print("账户指标计算:");
    Print("  交易数据: ", ArraySize(tradeResults), "笔交易");
    Print("  总盈利: ", DoubleToString(totalProfit, 2));
    Print("  总亏损: ", DoubleToString(totalLoss, 2));
    Print("  盈利因子: ", DoubleToString(profitFactor, 2));
    Print("  胜率: ", DoubleToString(winRate, 1), "%");
    Print("  平均盈利: ", DoubleToString(avgWin, 2));
    Print("  平均亏损: ", DoubleToString(avgLoss, 2));
    Print("  夏普比率: ", DoubleToString(sharpeRatio, 2));
    
    // 验证计算
    bool profitFactorValid = (profitFactor > 1.0 && profitFactor < 10.0);
    bool winRateValid = (winRate >= 50.0 && winRate <= 80.0);
    bool sharpeRatioValid = (sharpeRatio > 0.0);
    
    Print("  盈利因子验证: ", profitFactorValid ? "✓" : "✗");
    Print("  胜率验证: ", winRateValid ? "✓" : "✗");
    Print("  夏普比率验证: ", sharpeRatioValid ? "✓" : "✗");
    
    Print("✅ 账户指标计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试紧急停止机制                                                  |
//+------------------------------------------------------------------+
void TestEmergencyStopMechanisms()
{
    Print("📋 测试5: 紧急停止机制");
    
    // 测试紧急停止触发条件
    struct EmergencyStopTest {
        double currentDrawdown;
        double marginLevel;
        double emergencyThreshold;
        bool expectedTrigger;
        string description;
    };
    
    EmergencyStopTest tests[] = {
        {5.0, 500.0, 20.0, false, "正常状态"},
        {15.0, 300.0, 20.0, false, "轻微风险"},
        {22.0, 250.0, 20.0, true, "回撤超限"},
        {10.0, 180.0, 20.0, true, "保证金不足"},
        {25.0, 150.0, 20.0, true, "双重风险"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        EmergencyStopTest est = tests[i];
        
        // 检查紧急停止条件
        bool calculatedTrigger = CheckTestEmergencyStopConditions(est.currentDrawdown, 
                                                                 est.marginLevel, 
                                                                 est.emergencyThreshold);
        
        Print("紧急停止场景: ", est.description);
        Print("  当前回撤: ", DoubleToString(est.currentDrawdown, 1), "%");
        Print("  保证金水平: ", DoubleToString(est.marginLevel, 1), "%");
        Print("  紧急阈值: ", DoubleToString(est.emergencyThreshold, 1), "%");
        Print("  计算触发: ", calculatedTrigger ? "是" : "否");
        Print("  预期触发: ", est.expectedTrigger ? "是" : "否");
        
        bool triggerCorrect = (calculatedTrigger == est.expectedTrigger);
        Print("  触发验证: ", triggerCorrect ? "✓" : "✗");
    }
    
    Print("✅ 紧急停止机制测试完成\n");
}

//+------------------------------------------------------------------+
//| 检查测试紧急停止条件                                              |
//+------------------------------------------------------------------+
bool CheckTestEmergencyStopConditions(double currentDrawdown, double marginLevel, double emergencyThreshold) {
    // 检查回撤阈值
    if(currentDrawdown >= emergencyThreshold) return true;
    
    // 检查保证金水平
    if(marginLevel < 200.0) return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| 测试完整资金管理流程                                              |
//+------------------------------------------------------------------+
void TestCompleteMoneyManagementWorkflow()
{
    Print("📋 测试6: 完整资金管理流程");
    
    // 模拟完整的资金管理流程
    Print("模拟资金管理流程:");
    
    // 步骤1: 初始化资金管理系统
    Print("步骤1: 初始化资金管理系统");
    Print("  账户余额: ", DoubleToString(TestAccountBalance, 2));
    Print("  风险等级: 中等");
    Print("  最大风险: 2.0%");
    Print("  最大回撤: 20.0%");
    
    // 步骤2: 分析当前账户状态
    Print("步骤2: 分析当前账户状态");
    double currentEquity = TestAccountBalance * 0.95; // 假设5%浮亏
    double currentDrawdown = (TestAccountBalance - currentEquity) / TestAccountBalance * 100.0;
    double marginLevel = 400.0; // 假设保证金水平
    
    Print("  当前净值: ", DoubleToString(currentEquity, 2));
    Print("  当前回撤: ", DoubleToString(currentDrawdown, 2), "%");
    Print("  保证金水平: ", DoubleToString(marginLevel, 1), "%");
    
    // 步骤3: 计算建议仓位
    Print("步骤3: 计算建议仓位");
    double riskAmount = currentEquity * 0.02; // 2%风险
    double stopLossDistance = 50.0; // 50点止损
    double tickValue = 1.0; // 每点1美元
    double baseLotSize = riskAmount / (stopLossDistance * tickValue);
    
    // 应用调整因子
    double marketStateAdjustment = 1.2; // 市场状态调整
    double riskAdjustment = 0.9; // 风险调整
    double finalLotSize = baseLotSize * marketStateAdjustment * riskAdjustment;
    
    Print("  基础仓位: ", DoubleToString(baseLotSize, 2), "手");
    Print("  市场调整: ", DoubleToString(marketStateAdjustment, 2));
    Print("  风险调整: ", DoubleToString(riskAdjustment, 2));
    Print("  最终仓位: ", DoubleToString(finalLotSize, 2), "手");
    
    // 步骤4: 设置动态止损
    Print("步骤4: 设置动态止损");
    double entryPrice = 1.10000;
    double initialStopLoss = entryPrice - 0.00050; // 50点止损
    double trailingStep = 0.00010; // 10点跟踪步长
    
    Print("  入场价格: ", DoubleToString(entryPrice, 5));
    Print("  初始止损: ", DoubleToString(initialStopLoss, 5));
    Print("  跟踪步长: ", DoubleToString(trailingStep, 5));
    
    // 步骤5: 风险检查
    Print("步骤5: 风险检查");
    bool allowNewPosition = (currentDrawdown < 20.0 && marginLevel >= 200.0);
    bool emergencyStop = (currentDrawdown >= 20.0 || marginLevel < 200.0);
    
    Print("  允许新仓: ", allowNewPosition ? "是" : "否");
    Print("  紧急停止: ", emergencyStop ? "是" : "否");
    
    // 步骤6: 生成交易建议
    Print("步骤6: 生成交易建议");
    string recommendation = "";
    if(emergencyStop) {
        recommendation = "立即停止交易";
    } else if(currentDrawdown > 10.0) {
        recommendation = "谨慎交易，减少仓位";
    } else if(currentDrawdown > 5.0) {
        recommendation = "适度交易，正常仓位";
    } else {
        recommendation = "积极交易，可增加仓位";
    }
    
    Print("  交易建议: ", recommendation);
    Print("  建议仓位: ", DoubleToString(finalLotSize, 2), "手");
    
    // 步骤7: 流程验证
    Print("步骤7: 流程验证");
    bool workflowValid = (finalLotSize > 0 && finalLotSize <= 1.0 && !emergencyStop);
    Print("  流程有效性: ", workflowValid ? "✓" : "✗");
    Print("  验证条件: 仓位合理 无紧急停止 风险可控");
    
    Print("✅ 完整资金管理流程完成\n");
}
