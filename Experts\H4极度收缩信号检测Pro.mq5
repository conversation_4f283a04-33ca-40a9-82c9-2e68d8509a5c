//+------------------------------------------------------------------+
//|                                           CompressionDetectionEA.mq5 |
//|                                      Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.metaquotes.net |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net"
#property version   "1.10"

#include <CompressionDetection.mqh>
#include <TimeframeAnalysis.mqh>
#include <TrendAnalysis.mqh>

// 市场状态枚举
enum MARKET_STATE {
    MARKET_TRENDING,    // 趋势市场
    MARKET_RANGING,     // 震荡市场
    MARKET_LOW_VOL,     // 低波动市场
    MARKET_HIGH_VOL,    // 高波动市场
    MARKET_EVENT_DRIVEN // 事件驱动市场
};

// 基础周期参数 (统一管理相同用途的周期)
input int    ShortPeriod = 14;               // 短期周期 (ATR, ADX等)
input int    MediumPeriod = 20;              // 中期周期 (波动率, 成交量, 布林带等)
input int    LongPeriod = 50;                // 长期周期 (趋势分析等)
input bool EnableMultiTimeframe = true;      // 启用多时间框架分析
input int UpdateInterval = 60;               // 多时间框架更新间隔(秒)

// === 新增：Deepseek建议的增强参数 ===
input string Deepseek_Settings = "------- Deepseek优化设置 -------";
input bool   Enable_Advanced_Trend_Filter = true;  // 启用高级趋势过滤
input double Fibonacci_Retrace_Threshold = 0.618;  // 斐波那契回撤阈值
input int    Trend_Confirmation_Bars = 3;          // 趋势确认K线数量
input double RSI_Momentum_Threshold = 45;          // RSI动量阈值
input int    ATR_History = 100;              // ATR历史均值长度 (100-150最佳)
input double BW_StdDev = 1.8;                // 布林带标准差 (1.8-2.2最佳)
input int    BW_History = 200;               // 布林带宽度历史长度 (200最佳)
input double ADX_Threshold = 16;             // ADX极低阈值 (15-18最佳)
input double MA_Entangle_Threshold = 0.004;  // 均线缠绕阈值 (0.4%最佳)
input int    MA_Period1 = 10;                // 短期均线
input int    MA_Period2 = 20;                // 中期均线
input int    MA_Period3 = 50;                // 中期均线
input int    MA_Period4 = 144;               // 长期均线 
input int    MA_Period5 = 275;               // 长期均线 
input int    DailyRangePeriod = 5;           // 日线区间分析周期 (5-7最佳)
input int    Daily_History_Period = 180;     // 日线历史数据周期 (180-200最佳)
input double Daily_Range_Threshold = 0.35;   // 日线区间收缩阈值 (35%最佳)
input int    H4_Body_Avg_N = 6;              // H4实体均值根数 (5-6最佳)
input double H4_Body_Threshold = 0.25;       // H4实体极小阈值 (25%最佳)
input bool   EnableAdaptiveParameters = true; // 启用自适应参数
input double LearningRate = 0.01;            // 学习率 (0.01-0.05)

// 界面元素名称
#define PANEL_NAME "CompressionPanel"
#define VOLATILITY_LABEL "VolatilityLabel"
#define BB_WIDTH_LABEL "BBWidthLabel"
#define ATR_LABEL "ATRLabel"
#define VOLUME_LABEL "VolumeLabel"
#define PATTERN_LABEL "PatternLabel"
#define STATE_LABEL "StateLabel"
#define COMPRESSION_METER "CompressionMeter"
#define H1_LABEL "H1Label"
#define H4_LABEL "H4Label"
#define D1_LABEL "D1Label"
#define W1_LABEL "W1Label"
#define MN_LABEL "MNLabel"
#define CONSISTENCY_LABEL "ConsistencyLabel"
#define BREAKOUT_PROB_LABEL "BreakoutProbLabel"
#define BREAKOUT_METER "BreakoutMeter"
#define TREND_DIRECTION_LABEL "TrendDirectionLabel"
#define TREND_STRENGTH_LABEL "TrendStrengthLabel"
#define TREND_SCORE_LABEL "TrendScoreLabel"
#define TREND_METER "TrendMeter"
#define EXPECTED_DIRECTION_LABEL "ExpectedDirectionLabel"
#define MARKET_STATE_LABEL "MarketStateLabel"
#define TREND_CONSECUTIVE_LABEL "TrendConsecutiveLabel"
#define MOMENTUM_SCORE_LABEL "MomentumScoreLabel"
#define VOLUME_CONFIRM_LABEL "VolumeConfirmLabel"
#define RETRACEMENT_LABEL "RetracementLabel"

// 全局变量
CompressionIndicators g_indicators;   // 当前时间框架指标数据
TimeframeCompression g_tfCompression; // 多时间框架压缩数据
TrendAnalysis g_trendAnalysis;       // 趋势分析数据
bool g_initialized = false;           // 初始化标志
datetime g_lastMultiTfUpdate = 0;     // 上次多时间框架更新时间

// 市场状态相关全局变量
MARKET_STATE g_CurrentMarketState = MARKET_RANGING;  // 当前市场状态
MARKET_STATE g_LastMarketState = MARKET_RANGING;     // 上次市场状态
double g_Dynamic_ADX_Threshold;       // 动态ADX阈值
double g_Dynamic_BW_Threshold;        // 动态布林带阈值
double g_Dynamic_Entangle_Threshold;  // 动态均线缠绕阈值
datetime g_lastStateUpdate = 0;       // 上次状态更新时间
double g_Learning_ADX_Adjust = 1.0;   // ADX学习调整系数
double g_Learning_BW_Adjust = 1.0;    // 布林带学习调整系数

// 统一指标管理器类
class UnifiedIndicatorManager {
private:
    double m_atr;
    double m_atr_ma;
    double m_adx;
    double m_di_plus;
    double m_di_minus;
    datetime m_lastCalc;

    // 缓存的ATR句柄和数据
    int m_atrHandle;
    double m_atrValues[];

public:
    UnifiedIndicatorManager() {
        m_lastCalc = 0;
        m_atrHandle = INVALID_HANDLE;
        ArraySetAsSeries(m_atrValues, true);
    }

    ~UnifiedIndicatorManager() {
        if(m_atrHandle != INVALID_HANDLE) {
            IndicatorRelease(m_atrHandle);
        }
    }
    
    // 获取ATR值
    double GetATR(int period) {
        if(TimeCurrent() - m_lastCalc > 300) { // 5分钟刷新一次
            int handle = iATR(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double atr[];
                ArraySetAsSeries(atr, true);
                if(CopyBuffer(handle, 0, 0, 1, atr) > 0) {
                    m_atr = atr[0];
                }
                IndicatorRelease(handle);
            }
            m_lastCalc = TimeCurrent();
        }
        return m_atr;
    }

    // 获取ATR均值
    double GetATRMA(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iMA(_Symbol, PERIOD_D1, period, 0, MODE_SMA, PRICE_TYPICAL);
            if(handle != INVALID_HANDLE) {
                double ma[];
                ArraySetAsSeries(ma, true);
                if(CopyBuffer(handle, 0, 0, 1, ma) > 0) {
                    m_atr_ma = ma[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_atr_ma;
    }

    // 获取ADX值
    double GetADX(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double adx[];
                ArraySetAsSeries(adx, true);
                if(CopyBuffer(handle, 0, 0, 1, adx) > 0) {
                    m_adx = adx[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_adx;
    }

    // 获取DI+值
    double GetDIPlus(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double di_plus[];
                ArraySetAsSeries(di_plus, true);
                if(CopyBuffer(handle, 1, 0, 1, di_plus) > 0) {
                    m_di_plus = di_plus[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_di_plus;
    }

    // 获取DI-值
    double GetDIMinus(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double di_minus[];
                ArraySetAsSeries(di_minus, true);
                if(CopyBuffer(handle, 2, 0, 1, di_minus) > 0) {
                    m_di_minus = di_minus[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_di_minus;
    }
    
    // 重置缓存
    void Reset() {
        m_lastCalc = 0;
    }
};

// 创建统一指标管理器实例
UnifiedIndicatorManager g_indicatorManager;

// 交易时段枚举
enum TRADING_SESSION {
    SESSION_ASIA = 0x01,
    SESSION_LONDON = 0x02,
    SESSION_NEWYORK = 0x04,
    SESSION_OVERLAP = 0x08
};

//+------------------------------------------------------------------+
//| 创建区域背景框                                                    |
//+------------------------------------------------------------------+
void CreateZoneBackgrounds() {
    // 第一区域背景 - 基础压缩指标
    if(ObjectCreate(0, "Zone1_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_YDISTANCE, 15);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_XSIZE, 300);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_YSIZE, 170);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_BGCOLOR, C'20,20,20');
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_BORDER_COLOR, clrDimGray);
    }

    // 第二区域背景 - 多时间框架分析
    if(ObjectCreate(0, "Zone2_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_YDISTANCE, 195);
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_XSIZE, 300);
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_YSIZE, 180);
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_BGCOLOR, C'20,20,20');
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "Zone2_BG", OBJPROP_BORDER_COLOR, clrDimGray);
    }

    // 第三区域背景 - 趋势分析
    if(ObjectCreate(0, "Zone3_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_YDISTANCE, 385);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_XSIZE, 400);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_YSIZE, 200);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_BGCOLOR, C'20,20,20');
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_BORDER_COLOR, clrDimGray);
    }

    // 第四区域背景 - 市场状态
    if(ObjectCreate(0, "Zone4_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_YDISTANCE, 595);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_XSIZE, 400);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_YSIZE, 70);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_BGCOLOR, C'20,20,20');
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_BORDER_COLOR, clrDimGray);
    }

    // 添加区域标题
    CreateLabel("Zone1_Title", "基础压缩指标", 20, 17, clrGold);
    CreateLabel("Zone2_Title", "多时间框架分析", 20, 197, clrGold);
    CreateLabel("Zone3_Title", "趋势分析", 20, 387, clrGold);
    CreateLabel("Zone4_Title", "市场状态", 20, 597, clrGold);
}

//+------------------------------------------------------------------+
//| 创建标签                                                          |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color textColor) {
    if(ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, name, OBJPROP_TEXT, text);
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
        ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 添加抗锯齿效果
        ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
    }
}

//+------------------------------------------------------------------+
//| 创建度量表                                                        |
//+------------------------------------------------------------------+
void CreateMeter(string meterName, int yPosition) {
    // 创建背景框
    string bgName = meterName + "_BG";
    if(ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, yPosition - 5);
        ObjectSetInteger(0, bgName, OBJPROP_XSIZE, 210);
        ObjectSetInteger(0, bgName, OBJPROP_YSIZE, 30);
        ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, C'25,25,25');
        ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, bgName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bgName, OBJPROP_BORDER_COLOR, clrDimGray);
    }

    if(ObjectCreate(0, meterName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, meterName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, meterName, OBJPROP_YDISTANCE, yPosition);
        ObjectSetInteger(0, meterName, OBJPROP_XSIZE, 200);
        ObjectSetInteger(0, meterName, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, meterName, OBJPROP_BGCOLOR, clrDarkGray);
        ObjectSetInteger(0, meterName, OBJPROP_BORDER_TYPE, BORDER_RAISED);
        ObjectSetInteger(0, meterName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    // 添加关键阈值标记线
    CreateThresholdMarks(meterName, yPosition);
}

//+------------------------------------------------------------------+
//| 创建阈值标记                                                      |
//+------------------------------------------------------------------+
void CreateThresholdMarks(string meterName, int yPosition) {
    int thresholds[6];
    thresholds[0] = 30; thresholds[1] = 45; thresholds[2] = 60;
    thresholds[3] = 70; thresholds[4] = 85; thresholds[5] = 95;

    for(int i = 0; i < 6; i++) {
        int threshold = thresholds[i];
        int xPos = 20 + (int)(threshold * 2);

        // 创建标记线
        string markName = meterName + "_Mark" + IntegerToString(threshold);
        if(ObjectCreate(0, markName, OBJ_VLINE, 0, 0, 0)) {
            ObjectSetInteger(0, markName, OBJPROP_XDISTANCE, xPos);
            ObjectSetInteger(0, markName, OBJPROP_YDISTANCE, yPosition);
            ObjectSetInteger(0, markName, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, markName, OBJPROP_STYLE, STYLE_DOT);
            ObjectSetInteger(0, markName, OBJPROP_WIDTH, 1);
            ObjectSetInteger(0, markName, OBJPROP_BACK, false);
            ObjectSetInteger(0, markName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }

        // 创建标记标签
        string labelName = meterName + "_Label" + IntegerToString(threshold);
        if(ObjectCreate(0, labelName, OBJ_LABEL, 0, 0, 0)) {
            ObjectSetString(0, labelName, OBJPROP_TEXT, IntegerToString(threshold));
            ObjectSetInteger(0, labelName, OBJPROP_XDISTANCE, xPos);
            ObjectSetInteger(0, labelName, OBJPROP_YDISTANCE, yPosition + 10);
            ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrSilver);
            ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 7);
            ObjectSetInteger(0, labelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            // 居中锚点
            ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }
    }
}

//+------------------------------------------------------------------+
//| 清理阈值标记                                                      |
//+------------------------------------------------------------------+
void CleanupThresholdMarks(string meterName) {
    int thresholds[6];
    thresholds[0] = 30; thresholds[1] = 45; thresholds[2] = 60;
    thresholds[3] = 70; thresholds[4] = 85; thresholds[5] = 95;

    for(int i = 0; i < 6; i++) {
        string markName = meterName + "_Mark" + IntegerToString(thresholds[i]);
        string labelName = meterName + "_Label" + IntegerToString(thresholds[i]);

        ObjectDelete(0, markName);
        ObjectDelete(0, labelName);
    }

    // 清理背景框
    string bgName = meterName + "_BG";
    ObjectDelete(0, bgName);
}

//+------------------------------------------------------------------+
//| 创建压缩度量表                                                    |
//+------------------------------------------------------------------+
void CreateCompressionMeter() {
    // 创建主度量表
    CreateMeter(COMPRESSION_METER, 155);

    // 添加刻度标签
    if(ObjectCreate(0, "CompressionMeterLabel0", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "CompressionMeterLabel0", OBJPROP_TEXT, "0");
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_YDISTANCE, 175);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    if(ObjectCreate(0, "CompressionMeterLabel50", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "CompressionMeterLabel50", OBJPROP_TEXT, "50");
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_XDISTANCE, 120);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_YDISTANCE, 175);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    if(ObjectCreate(0, "CompressionMeterLabel100", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "CompressionMeterLabel100", OBJPROP_TEXT, "100");
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_XDISTANCE, 220);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_YDISTANCE, 175);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

//+------------------------------------------------------------------+
//| 更新度量表                                                        |
//+------------------------------------------------------------------+
void UpdateMeter(string meterName, double value, double minValue = 0.0, double maxValue = 100.0) {
    // 计算标准化值
    double normalizedValue = (value - minValue) / (maxValue - minValue) * 100.0;
    normalizedValue = MathMax(0.0, MathMin(100.0, normalizedValue));

    // 智能颜色分配
    color meterColor;
    if(StringFind(meterName, "Trend") >= 0) {
        // 趋势度量表专用颜色方案（9级莫兰迪色系）
        if(normalizedValue < 20) {
            // 低强度 - 柔和灰蓝色
            meterColor = C'142,158,171'; // 莫兰迪灰蓝
        } else if(normalizedValue < 30) {
            // 低中强度 - 淡橄榄绿
            meterColor = C'152,170,146'; // 莫兰迪橄榄
        } else if(normalizedValue < 40) {
            // 中低强度 - 淡紫灰
            meterColor = C'167,151,168'; // 莫兰迪紫灰
        } else if(normalizedValue < 50) {
            // 中等强度 - 淡砖红
            meterColor = C'184,145,132'; // 莫兰迪砖红
        } else if(normalizedValue < 60) {
            // 中等强度 - 淡黄褐
            meterColor = C'193,166,132'; // 莫兰迪黄褐
        } else if(normalizedValue < 70) {
            // 中高强度 - 淡绿松石
            meterColor = C'134,174,170'; // 莫兰迪绿松石
        } else if(normalizedValue < 80) {
            // 高中强度 - 淡玫瑰红
            meterColor = C'208,138,142'; // 莫兰迪玫瑰红
        } else if(normalizedValue < 90) {
            // 高强度 - 淡赭石
            meterColor = C'217,160,102'; // 莫兰迪赭石
        } else {
            // 极高强度 - 淡珊瑚红
            meterColor = C'229,144,115'; // 莫兰迪珊瑚红
        }
    } else {
        // 压缩度量表颜色方案（7级渐变）
        if(normalizedValue < 30) {
            // 低压缩 - 冷蓝色
            meterColor = C'65,105,225'; // 皇家蓝
        } else if(normalizedValue < 45) {
            // 低中压缩 - 青色
            meterColor = C'64,224,208'; // 绿松石
        } else if(normalizedValue < 60) {
            // 中等压缩 - 绿色
            meterColor = C'50,205,50'; // 酸橙绿
        } else if(normalizedValue < 75) {
            // 中高压缩 - 黄色
            meterColor = C'255,215,0'; // 金色
        } else if(normalizedValue < 85) {
            // 高压缩 - 橙色
            meterColor = C'255,140,0'; // 深橙色
        } else if(normalizedValue < 95) {
            // 极高压缩 - 红色
            meterColor = C'255,69,0'; // 红橙色
        } else {
            // 临界压缩 - 深红色
            meterColor = C'178,34,34'; // 火砖红
        }
    }

    // 创建填充矩形
    string fillName = meterName + "_Fill";
    if(ObjectFind(0, fillName) < 0) {
        ObjectCreate(0, fillName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    }

    // 设置填充矩形属性
    ObjectSetInteger(0, fillName, OBJPROP_XDISTANCE, 20);
    ObjectSetInteger(0, fillName, OBJPROP_YDISTANCE, ObjectGetInteger(0, meterName, OBJPROP_YDISTANCE));
    ObjectSetInteger(0, fillName, OBJPROP_XSIZE, (int)(200 * normalizedValue / 100));
    ObjectSetInteger(0, fillName, OBJPROP_YSIZE, 20);
    ObjectSetInteger(0, fillName, OBJPROP_BGCOLOR, meterColor);
    ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, fillName, OBJPROP_CORNER, CORNER_LEFT_UPPER);

    // 添加光晕效果
    if(normalizedValue > 70) {
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_COLOR, clrWhite);
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_SUNKEN);
        ObjectSetInteger(0, fillName, OBJPROP_WIDTH, 2);
    } else {
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_COLOR, meterColor);
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, fillName, OBJPROP_WIDTH, 1);
    }
}

//+------------------------------------------------------------------+
//| 更新压缩度量表                                                    |
//+------------------------------------------------------------------+
void UpdateCompressionMeter(double compressionScore) {
    // 更新度量表
    UpdateMeter(COMPRESSION_METER, compressionScore);
}

//+------------------------------------------------------------------+
//| 获取当前交易时段                                                  |
//+------------------------------------------------------------------+
int GetCurrentSession() {
    MqlDateTime tm;
    TimeCurrent(tm);
    int hour = tm.hour;
    
    int session = 0;
    if(hour >= 1 && hour < 8) session |= SESSION_ASIA;
    if(hour >= 8 && hour < 16) session |= SESSION_LONDON;
    if(hour >= 13 && hour < 17) session |= SESSION_OVERLAP;
    if(hour >= 14 && hour < 22) session |= SESSION_NEWYORK;
    
    return session;
}

//+------------------------------------------------------------------+
//| 判断是否为重大事件日                                             |
//+------------------------------------------------------------------+
bool IsMajorNewsDay() {
    // 基于波动率异常检测
    double current_vol = 0.0;
    double avg_vol = 0.0;

    // 获取当前ATR值
    int atrHandle = iATR(_Symbol, PERIOD_H1, 14);
    if(atrHandle != INVALID_HANDLE) {
        double atr[];
        ArraySetAsSeries(atr, true);
        if(CopyBuffer(atrHandle, 0, 0, 1, atr) > 0) {
            current_vol = atr[0];
        }
        IndicatorRelease(atrHandle);
    }

    // 获取平均值
    int maHandle = iMA(_Symbol, PERIOD_H1, 48, 0, MODE_SMA, PRICE_TYPICAL);
    if(maHandle != INVALID_HANDLE) {
        double ma[];
        ArraySetAsSeries(ma, true);
        if(CopyBuffer(maHandle, 0, 0, 1, ma) > 0) {
            avg_vol = ma[0];
        }
        IndicatorRelease(maHandle);
    }

    // 如果当前波动率显著高于平均值，认为是重大事件日
    if(current_vol > 0 && avg_vol > 0 && current_vol > avg_vol * 1.8) {
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| 高级市场状态分析函数                                             |
//+------------------------------------------------------------------+
MARKET_STATE AnalyzeMarketState() {
    // 1. 波动率分析
    double atr = g_indicatorManager.GetATR(14);
    double atr_ma = g_indicatorManager.GetATRMA(20);
    double volatility_ratio = atr / atr_ma;

    // 2. 趋势强度分析
    double adx = g_indicatorManager.GetADX(14);
    double di_plus = g_indicatorManager.GetDIPlus(14);
    double di_minus = g_indicatorManager.GetDIMinus(14);
    
    // 3. 价格行为分析
    double high[], low[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    double range = 0.0;
    if(CopyHigh(_Symbol, PERIOD_D1, 0, 1, high) > 0 && CopyLow(_Symbol, PERIOD_D1, 0, 1, low) > 0) {
        range = high[0] - low[0];
    }
    double avg_range = g_indicatorManager.GetATR(5);
    
    // 4. 时间因素
    bool is_news_day = IsMajorNewsDay();
    
    // 状态决策逻辑
    if(is_news_day) {
        return MARKET_EVENT_DRIVEN;
    }
    else if(volatility_ratio > 1.4) {
        return MARKET_HIGH_VOL;
    }
    else if(volatility_ratio < 0.7) {
        return MARKET_LOW_VOL;
    }
    else if(adx > 25 && MathAbs(di_plus - di_minus) > 15) {
        return MARKET_TRENDING;
    }
    else {
        return MARKET_RANGING;
    }
}

//+------------------------------------------------------------------+
//| 动态参数调整函数                                                 |
//+------------------------------------------------------------------+
void AdjustDynamicParameters() {
    g_LastMarketState = g_CurrentMarketState;
    g_CurrentMarketState = AnalyzeMarketState();

    // 使用预设调整系数（不再动态学习）
    static const double stateFactors[5][3] = {
        {0.9, 1.2, 1.1},    // TRENDING
        {1.1, 0.9, 0.9},    // RANGING
        {0.8, 0.8, 0.85},   // LOW_VOL
        {1.25, 1.3, 1.2},   // HIGH_VOL
        {1.4, 1.5, 1.3}     // EVENT_DRIVEN
    };

    int stateIndex = (int)g_CurrentMarketState;
    if(stateIndex >= 0 && stateIndex < 5) {
        g_Dynamic_ADX_Threshold = ADX_Threshold * stateFactors[stateIndex][0];
        g_Dynamic_BW_Threshold = BW_StdDev * stateFactors[stateIndex][1];
        g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * stateFactors[stateIndex][2];
    } else {
        // 默认值
        g_Dynamic_ADX_Threshold = ADX_Threshold;
        g_Dynamic_BW_Threshold = BW_StdDev;
        g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold;
    }

    // 记录更新时间
    g_lastStateUpdate = TimeCurrent();
}

//+------------------------------------------------------------------+
//| 在UI中显示市场状态                                               |
//+------------------------------------------------------------------+
void ShowMarketStateInUI() {
    string stateText = "";
    color stateColor = clrGray;
    
    switch(g_CurrentMarketState) {
        case MARKET_TRENDING:
            stateText = "趋势市场";
            stateColor = clrBlue;
            break;
        case MARKET_RANGING:
            stateText = "震荡市场";
            stateColor = clrGreen;
            break;
        case MARKET_LOW_VOL:
            stateText = "低波动市场";
            stateColor = clrGold;
            break;
        case MARKET_HIGH_VOL:
            stateText = "高波动市场";
            stateColor = clrOrangeRed;
            break;
        case MARKET_EVENT_DRIVEN:
            stateText = "事件驱动市场";
            stateColor = clrRed;
            break;
        default:
            stateText = "未知状态";
    }
    
    // 创建或更新标签 (第四区域：市场状态)
    if(ObjectFind(0, MARKET_STATE_LABEL) < 0) {
        CreateLabel(MARKET_STATE_LABEL, "市场状态: " + stateText, 20, 545, stateColor);
    } else {
        ObjectSetString(0, MARKET_STATE_LABEL, OBJPROP_TEXT, "市场状态: " + stateText);
        ObjectSetInteger(0, MARKET_STATE_LABEL, OBJPROP_COLOR, stateColor);
    }

    // 显示动态参数 (增加间距)
    string paramText = StringFormat("动态参数: ADX=%.2f BW=%.2f MA=%.4f",
                                   g_Dynamic_ADX_Threshold,
                                   g_Dynamic_BW_Threshold,
                                   g_Dynamic_Entangle_Threshold);

    if(ObjectFind(0, "DynamicParamsLabel") < 0) {
        CreateLabel("DynamicParamsLabel", paramText, 20, 570, clrWhite);
    } else {
        ObjectSetString(0, "DynamicParamsLabel", OBJPROP_TEXT, paramText);
    }
}

//+------------------------------------------------------------------+
//| 创建爆发概率度量表                                                |
//+------------------------------------------------------------------+
void CreateBreakoutMeter() {
    // 创建主度量表
    CreateMeter(BREAKOUT_METER, 355);

    // 添加刻度标签
    if(ObjectCreate(0, "BreakoutMeterLabel0", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "BreakoutMeterLabel0", OBJPROP_TEXT, "0");
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_YDISTANCE, 375);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    if(ObjectCreate(0, "BreakoutMeterLabel50", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "BreakoutMeterLabel50", OBJPROP_TEXT, "50");
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_XDISTANCE, 120);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_YDISTANCE, 375);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    if(ObjectCreate(0, "BreakoutMeterLabel100", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "BreakoutMeterLabel100", OBJPROP_TEXT, "100");
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_XDISTANCE, 220);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_YDISTANCE, 375);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

//+------------------------------------------------------------------+
//| 创建趋势度量表                                                    |
//+------------------------------------------------------------------+
void CreateTrendMeter() {
    // 创建主度量表
    CreateMeter(TREND_METER, 565);

    // 添加刻度标签
    if(ObjectCreate(0, "TrendMeterLabel0", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "TrendMeterLabel0", OBJPROP_TEXT, "0");
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_YDISTANCE, 585);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    if(ObjectCreate(0, "TrendMeterLabel50", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "TrendMeterLabel50", OBJPROP_TEXT, "50");
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_XDISTANCE, 120);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_YDISTANCE, 585);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    if(ObjectCreate(0, "TrendMeterLabel100", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "TrendMeterLabel100", OBJPROP_TEXT, "100");
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_XDISTANCE, 220);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_YDISTANCE, 585);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        // 居中锚点
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

//+------------------------------------------------------------------+
//| 自定义函数：更新爆发概率度量表                                      |
//+------------------------------------------------------------------+
void UpdateBreakoutMeter(double probability) {
    UpdateMeter(BREAKOUT_METER, probability, 0.0, 100.0);
}

//+------------------------------------------------------------------+
//| 自定义函数：更新多时间框架分析                                      |
//+------------------------------------------------------------------+
void UpdateMultiTimeframeAnalysis() {
    if(!EnableMultiTimeframe) return;
    
    // 检查是否需要更新（根据设定的间隔）
    datetime currentTime = TimeCurrent();
    if(currentTime - g_lastMultiTfUpdate < UpdateInterval) return;
    
    // 更新多时间框架压缩数据
    g_tfCompression = AnalyzeTimeframes(MediumPeriod,    // 波动率周期
                                       MediumPeriod,     // 布林带周期
                                       BW_StdDev,        // 布林带标准差
                                       ShortPeriod,      // ATR周期
                                       MediumPeriod);    // 成交量周期
    
    // 更新上次更新时间
    g_lastMultiTfUpdate = currentTime;
    
    // 更新界面显示
    string h1Text = StringFormat("H1: %s", GetCompressionStateDescription(g_tfCompression.h1State));
    string h4Text = StringFormat("H4: %s", GetCompressionStateDescription(g_tfCompression.h4State));
    string d1Text = StringFormat("D1: %s", GetCompressionStateDescription(g_tfCompression.d1State));
    string w1Text = StringFormat("W1: %s", GetCompressionStateDescription(g_tfCompression.w1State));
    string mnText = StringFormat("MN: %s", GetCompressionStateDescription(g_tfCompression.mnState));
    string consistencyText = StringFormat("时间框架一致性: %.2f%%", g_tfCompression.consistencyScore);
    
    // 计算增强版爆发概率
    double breakoutProb = CalculateEnhancedBreakoutProbability(g_tfCompression, g_indicators, g_trendAnalysis);
    string breakoutText = StringFormat("爆发概率: %.2f%%", breakoutProb);
    
    // 更新标签文本
    ObjectSetString(0, H1_LABEL, OBJPROP_TEXT, h1Text);
    ObjectSetString(0, H4_LABEL, OBJPROP_TEXT, h4Text);
    ObjectSetString(0, D1_LABEL, OBJPROP_TEXT, d1Text);
    ObjectSetString(0, W1_LABEL, OBJPROP_TEXT, w1Text);
    ObjectSetString(0, MN_LABEL, OBJPROP_TEXT, mnText);
    ObjectSetString(0, CONSISTENCY_LABEL, OBJPROP_TEXT, consistencyText);
    ObjectSetString(0, BREAKOUT_PROB_LABEL, OBJPROP_TEXT, breakoutText);
    
    // 更新爆发概率度量表
    UpdateBreakoutMeter(breakoutProb);
}



//+------------------------------------------------------------------+
//| 自定义函数：更新趋势度量表（优化为0-100范围）                      |
//+------------------------------------------------------------------+
void UpdateTrendMeter(double score) {
    // 将-100到+100的评分转换为0到100的强度值
    double trendStrength = MathAbs(score); // 取绝对值作为趋势强度

    // 确保在0-100范围内
    trendStrength = MathMax(0.0, MathMin(100.0, trendStrength));

    UpdateMeter(TREND_METER, trendStrength, 0.0, 100.0);
}

//+------------------------------------------------------------------+
//| 自定义函数：更新基本显示                                           |
//+------------------------------------------------------------------+
void UpdateDisplay() {
    // 计算综合压缩分数
    double compressionScore = (g_indicators.volatility * 0.25 +
                             g_indicators.bbWidth * 0.25 +
                             g_indicators.atrValue * 0.20 +
                             g_indicators.volumeCompress * 0.20);

    // 更新基本指标标签
    string volatilityText = StringFormat("波动率压缩: %.2f%%", g_indicators.volatility);
    string bbWidthText = StringFormat("布林带压缩: %.2f%%", g_indicators.bbWidth);
    string atrText = StringFormat("ATR压缩: %.2f%%", g_indicators.atrValue);
    string volumeText = StringFormat("成交量压缩: %.2f%%", g_indicators.volumeCompress);
    string patternText = StringFormat("K线形态: %s", g_indicators.candlePattern);

    // 获取压缩状态
    COMPRESSION_STATE state = EvaluateCompression(g_indicators);
    string stateText = StringFormat("压缩状态: %s", GetCompressionStateDescription(state));

    // 更新标签文本
    ObjectSetString(0, VOLATILITY_LABEL, OBJPROP_TEXT, volatilityText);
    ObjectSetString(0, BB_WIDTH_LABEL, OBJPROP_TEXT, bbWidthText);
    ObjectSetString(0, ATR_LABEL, OBJPROP_TEXT, atrText);
    ObjectSetString(0, VOLUME_LABEL, OBJPROP_TEXT, volumeText);
    ObjectSetString(0, PATTERN_LABEL, OBJPROP_TEXT, patternText);
    ObjectSetString(0, STATE_LABEL, OBJPROP_TEXT, stateText);

    // 更新压缩度量表
    UpdateCompressionMeter(compressionScore);

    // 设置状态标签颜色
    color stateColor = clrYellow;
    switch(state) {
        case EXTREME_COMPRESSION: stateColor = clrRed; break;
        case HEAVY_COMPRESSION:   stateColor = clrOrange; break;
        case MEDIUM_COMPRESSION:  stateColor = clrYellow; break;
        case LIGHT_COMPRESSION:   stateColor = clrLightGreen; break;
        default:                  stateColor = clrGreen; break;
    }
    ObjectSetInteger(0, STATE_LABEL, OBJPROP_COLOR, stateColor);
}

//+------------------------------------------------------------------+
//| 自定义函数：分析预期爆发方向                                       |
//+------------------------------------------------------------------+
string AnalyzeExpectedDirection() {
    string direction = "未知";
    color directionColor = clrGray;
    
    // 计算综合压缩分数
    double compressionScore = (g_indicators.volatility * 0.25 +
                             g_indicators.bbWidth * 0.25 +
                             g_indicators.atrValue * 0.20 +
                             g_indicators.volumeCompress * 0.20);
                             
    // 如果压缩程度足够高
    if(compressionScore >= 60.0) {
        // 根据趋势方向和RSI判断可能的爆发方向
        if(g_trendAnalysis.direction == TREND_BULLISH) {
            direction = "预期向上突破";
            directionColor = clrLime;
        }
        else if(g_trendAnalysis.direction == TREND_BEARISH) {
            direction = "预期向下突破";
            directionColor = clrRed;
        }
        else {
            // 在趋势中性时，根据RSI判断
            double rsi[];
            ArraySetAsSeries(rsi, true);
            int rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
            
            if(rsiHandle != INVALID_HANDLE) {
                if(CopyBuffer(rsiHandle, 0, 0, 1, rsi) > 0) {
                    if(rsi[0] > 60) {
                        direction = "倾向向上突破";
                        directionColor = clrGreen;
                    }
                    else if(rsi[0] < 40) {
                        direction = "倾向向下突破";
                        directionColor = clrCrimson;
                    }
                    else {
                        direction = "方向不明确";
                        directionColor = clrYellow;
                    }
                }
                IndicatorRelease(rsiHandle);
            }
        }
    }
    else {
        direction = "压缩程度不足";
        directionColor = clrGray;
    }
    
    // 更新方向标签
    if(ObjectFind(0, EXPECTED_DIRECTION_LABEL) >= 0) {
        ObjectSetString(0, EXPECTED_DIRECTION_LABEL, OBJPROP_TEXT, 
                       StringFormat("预期方向: %s", direction));
        ObjectSetInteger(0, EXPECTED_DIRECTION_LABEL, OBJPROP_COLOR, directionColor);
    }
    
    return direction;
}

//+------------------------------------------------------------------+
//| Deepseek建议的智能趋势过滤器                                      |
//+------------------------------------------------------------------+
bool ApplyAdvancedTrendFilter(double breakoutProb) {
    if(!Enable_Advanced_Trend_Filter) return true; // 如果禁用过滤器，直接通过

    // 1. 趋势质量检查
    if(g_trendAnalysis.quality == NO_TREND_QUALITY) {
        return false; // 无趋势质量时拒绝信号
    }

    // 2. 斐波那契回撤检查
    if(g_trendAnalysis.fibonacciRetrace < Fibonacci_Retrace_Threshold) {
        return false; // 回撤不足时拒绝信号
    }

    // 3. 连续K线确认
    if(g_trendAnalysis.consecutiveBars < Trend_Confirmation_Bars) {
        return false; // 连续性不足时拒绝信号
    }

    // 4. RSI健康度检查
    if(g_trendAnalysis.rsiValue < RSI_Momentum_Threshold ||
       g_trendAnalysis.rsiValue > (100 - RSI_Momentum_Threshold)) {
        return false; // RSI极端值时拒绝信号
    }

    // 5. 多时间框架一致性检查
    if(!g_trendAnalysis.multiTimeframeAlign) {
        return false; // 多时间框架不一致时拒绝信号
    }

    // 6. 动态概率阈值调整
    double dynamicThreshold = 65.0; // 基础阈值

    // 根据市场状态调整阈值
    switch(g_CurrentMarketState) {
        case MARKET_HIGH_VOL:
            dynamicThreshold = 70.0; // 高波动市场提高阈值
            break;
        case MARKET_EVENT_DRIVEN:
            dynamicThreshold = 75.0; // 事件驱动市场进一步提升阈值
            break;
        case MARKET_LOW_VOL:
            dynamicThreshold = 60.0; // 低波动市场降低阈值
            break;
    }

    return breakoutProb >= dynamicThreshold;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                      |
//+------------------------------------------------------------------+
int OnInit() {
    // 创建区域背景框
    CreateZoneBackgrounds();

    // === 第一区域：基础压缩指标 (Y: 20-160) ===
    CreateLabel(VOLATILITY_LABEL, "波动率压缩: 0.00%", 30, 35, clrWhite);
    CreateLabel(BB_WIDTH_LABEL, "布林带压缩: 0.00%", 30, 55, clrWhite);
    CreateLabel(ATR_LABEL, "ATR压缩: 0.00%", 30, 75, clrWhite);
    CreateLabel(VOLUME_LABEL, "成交量压缩: 0.00%", 30, 95, clrWhite);
    CreateLabel(PATTERN_LABEL, "K线形态: -", 30, 115, clrWhite);
    CreateLabel(STATE_LABEL, "压缩状态: 无压缩", 30, 135, clrYellow);

    // 创建压缩度量表 (Y: 155)
    CreateCompressionMeter();

    // === 第二区域：多时间框架分析 (Y: 195-375) ===
    if(EnableMultiTimeframe) {
        CreateLabel(H1_LABEL, "H1: 无压缩", 30, 215, clrWhite);
        CreateLabel(H4_LABEL, "H4: 无压缩", 30, 235, clrWhite);
        CreateLabel(D1_LABEL, "D1: 无压缩", 30, 255, clrWhite);
        CreateLabel(W1_LABEL, "W1: 无压缩", 30, 275, clrWhite);
        CreateLabel(MN_LABEL, "MN: 无压缩", 30, 295, clrWhite);
        CreateLabel(CONSISTENCY_LABEL, "时间框架一致性: 0.00%", 30, 315, clrYellow);
        CreateLabel(BREAKOUT_PROB_LABEL, "爆发概率: 0.00%", 30, 335, clrMagenta);

        // 创建爆发概率度量表 (Y: 345)
        CreateBreakoutMeter();
    }

    // === 第三区域：趋势分析 (Y: 385-585) ===
    // 左侧列
    CreateLabel(TREND_DIRECTION_LABEL, "趋势方向: 中性", 30, 405, clrWhite);
    CreateLabel(TREND_STRENGTH_LABEL, "趋势强度: 弱", 30, 425, clrWhite);
    CreateLabel(TREND_SCORE_LABEL, "趋势评分: 0.00", 30, 445, clrWhite);
    CreateLabel(EXPECTED_DIRECTION_LABEL, "预期方向: 未知", 30, 465, clrWhite);
    CreateLabel(TREND_CONSECUTIVE_LABEL, "连续Bar数: 0", 30, 485, clrWhite);
    CreateLabel(MOMENTUM_SCORE_LABEL, "动量评分: 0.00", 30, 505, clrWhite);
    CreateLabel(VOLUME_CONFIRM_LABEL, "成交量确认: 否", 30, 525, clrWhite);
    CreateLabel(RETRACEMENT_LABEL, "回撤深度: 0.00%", 30, 545, clrWhite);

    // 右侧列 - Deepseek优化显示
    CreateLabel("TrendQualityLabel", "趋势质量: 无", 250, 405, clrWhite);
    CreateLabel("FibRetracementLabel", "斐波回撤: 0.000", 250, 425, clrWhite);
    CreateLabel("MTFAlignLabel", "多框架一致: 否", 250, 445, clrWhite);
    CreateLabel("TrendFilterLabel", "智能过滤: 关闭", 250, 465, clrWhite);

    // 创建趋势度量表 (Y: 550)
    CreateTrendMeter();


    // === 第四区域：市场状态 (Y: 595-665) ===
    // 初始化市场状态显示
    AdjustDynamicParameters();  // 初始化市场状态
    ShowMarketStateInUI();      // 立即显示市场状态

    // 设置定时器（每秒更新一次）
    EventSetTimer(1);

    g_initialized = true;
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // 删除定时器
    EventKillTimer();

    // === 清理第一区域：基础压缩指标 ===
    ObjectDelete(0, VOLATILITY_LABEL);
    ObjectDelete(0, BB_WIDTH_LABEL);
    ObjectDelete(0, ATR_LABEL);
    ObjectDelete(0, VOLUME_LABEL);
    ObjectDelete(0, PATTERN_LABEL);
    ObjectDelete(0, STATE_LABEL);
    ObjectDelete(0, COMPRESSION_METER);
    ObjectDelete(0, COMPRESSION_METER + "_Fill");

    // === 清理第二区域：多时间框架分析 ===
    if(EnableMultiTimeframe) {
        ObjectDelete(0, H1_LABEL);
        ObjectDelete(0, H4_LABEL);
        ObjectDelete(0, D1_LABEL);
        ObjectDelete(0, W1_LABEL);
        ObjectDelete(0, MN_LABEL);
        ObjectDelete(0, CONSISTENCY_LABEL);
        ObjectDelete(0, BREAKOUT_PROB_LABEL);
        ObjectDelete(0, BREAKOUT_METER);
        ObjectDelete(0, BREAKOUT_METER + "_Fill");
    }

    // === 清理第三区域：趋势分析 ===
    ObjectDelete(0, TREND_DIRECTION_LABEL);
    ObjectDelete(0, TREND_STRENGTH_LABEL);
    ObjectDelete(0, TREND_SCORE_LABEL);
    ObjectDelete(0, TREND_METER);
    ObjectDelete(0, TREND_METER + "_Fill");
    ObjectDelete(0, EXPECTED_DIRECTION_LABEL);
    ObjectDelete(0, TREND_CONSECUTIVE_LABEL);
    ObjectDelete(0, MOMENTUM_SCORE_LABEL);
    ObjectDelete(0, VOLUME_CONFIRM_LABEL);
    ObjectDelete(0, RETRACEMENT_LABEL);

    // === 清理第四区域：市场状态 ===
    ObjectDelete(0, MARKET_STATE_LABEL);
    ObjectDelete(0, "DynamicParamsLabel");

    // === 清理Deepseek优化显示 ===
    ObjectDelete(0, "TrendQualityLabel");
    ObjectDelete(0, "FibRetracementLabel");
    ObjectDelete(0, "MTFAlignLabel");
    ObjectDelete(0, "TrendFilterLabel");

    // === 清理趋势度量表刻度标签 ===
    ObjectDelete(0, "TrendMeterLabel0");
    ObjectDelete(0, "TrendMeterLabel50");
    ObjectDelete(0, "TrendMeterLabel100");

    // === 清理压缩度量表刻度标签 ===
    ObjectDelete(0, "CompressionMeterLabel0");
    ObjectDelete(0, "CompressionMeterLabel50");
    ObjectDelete(0, "CompressionMeterLabel100");

    // === 清理爆发概率度量表刻度标签 ===
    ObjectDelete(0, "BreakoutMeterLabel0");
    ObjectDelete(0, "BreakoutMeterLabel50");
    ObjectDelete(0, "BreakoutMeterLabel100");

    // === 清理Deepseek增强的阈值标记 ===
    CleanupThresholdMarks(TREND_METER);
    CleanupThresholdMarks(COMPRESSION_METER);
    CleanupThresholdMarks(BREAKOUT_METER);

    // === 清理区域背景框 ===
    ObjectDelete(0, "Zone1_BG");
    ObjectDelete(0, "Zone2_BG");
    ObjectDelete(0, "Zone3_BG");
    ObjectDelete(0, "Zone4_BG");
    ObjectDelete(0, "Zone1_Title");
    ObjectDelete(0, "Zone2_Title");
    ObjectDelete(0, "Zone3_Title");
    ObjectDelete(0, "Zone4_Title");

    // 重置全局状态变量
    g_initialized = false;
    g_CurrentMarketState = MARKET_RANGING;
    g_LastMarketState = MARKET_RANGING;

    // 打印退出信息
    Print("压缩检测EA已停止，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| 自定义函数：更新趋势分析显示                                       |
//+------------------------------------------------------------------+
void UpdateTrendDisplay() {
    // 更新趋势分析数据
    g_trendAnalysis = AnalyzeTrend(LongPeriod);
    
    // 获取趋势方向和强度的描述
    string directionDesc = GetTrendDirectionDescription(g_trendAnalysis.direction);
    string strengthDesc = GetTrendStrengthDescription(g_trendAnalysis.strength);
    
    // 获取趋势颜色
    color trendColor = GetTrendColor(g_trendAnalysis.direction, g_trendAnalysis.strength);
    
    // 更新趋势标签
    ObjectSetString(0, TREND_DIRECTION_LABEL, OBJPROP_TEXT, 
                   StringFormat("趋势方向: %s", directionDesc));
    ObjectSetInteger(0, TREND_DIRECTION_LABEL, OBJPROP_COLOR, trendColor);
    
    ObjectSetString(0, TREND_STRENGTH_LABEL, OBJPROP_TEXT, 
                   StringFormat("趋势强度: %s", strengthDesc));
    ObjectSetInteger(0, TREND_STRENGTH_LABEL, OBJPROP_COLOR, trendColor);
    
    // 计算趋势强度（0-100）
    double trendStrength = MathAbs(g_trendAnalysis.score);

    ObjectSetString(0, TREND_SCORE_LABEL, OBJPROP_TEXT,
                   StringFormat("趋势强度: %.1f/100", trendStrength));

    // 更新趋势度量表
    UpdateTrendMeter(g_trendAnalysis.score);

    // Deepseek建议：7级精细强度等级说明
    string strengthLevel = "";
    color strengthColor = clrWhite;
    if(trendStrength >= 95) {
        strengthLevel = " (极危险)";
        strengthColor = clrRed;
    }
    else if(trendStrength >= 85) {
        strengthLevel = " (高风险)";
        strengthColor = 0xFF6347; // 番茄红
    }
    else if(trendStrength >= 70) {
        strengthLevel = " (警告)";
        strengthColor = clrOrange;
    }
    else if(trendStrength >= 60) {
        strengthLevel = " (注意)";
        strengthColor = clrGold;
    }
    else if(trendStrength >= 45) {
        strengthLevel = " (中等)";
        strengthColor = clrYellowGreen;
    }
    else if(trendStrength >= 30) {
        strengthLevel = " (良好)";
        strengthColor = clrLime;
    }
    else {
        strengthLevel = " (安全)";
        strengthColor = clrGreen;
    }

    ObjectSetString(0, TREND_SCORE_LABEL, OBJPROP_TEXT,
                   StringFormat("趋势强度: %.1f/100%s", trendStrength, strengthLevel));
    ObjectSetInteger(0, TREND_SCORE_LABEL, OBJPROP_COLOR, strengthColor);

    // 更新新增的趋势增强指标
    ObjectSetString(0, TREND_CONSECUTIVE_LABEL, OBJPROP_TEXT,
                   StringFormat("连续Bar数: %d", g_trendAnalysis.consecutiveBars));

    ObjectSetString(0, MOMENTUM_SCORE_LABEL, OBJPROP_TEXT,
                   StringFormat("动量评分: %.2f", g_trendAnalysis.momentumScore));

    ObjectSetString(0, VOLUME_CONFIRM_LABEL, OBJPROP_TEXT,
                   StringFormat("成交量确认: %s", g_trendAnalysis.volumeConfirmation ? "是" : "否"));
    ObjectSetInteger(0, VOLUME_CONFIRM_LABEL, OBJPROP_COLOR,
                    g_trendAnalysis.volumeConfirmation ? clrLime : clrGray);

    ObjectSetString(0, RETRACEMENT_LABEL, OBJPROP_TEXT,
                   StringFormat("回撤深度: %.2f%%", g_trendAnalysis.retracementDepth * 100));

    // === 新增：Deepseek增强的阈值标记更新 ===
    // 趋势质量显示
    string qualityText = "";
    color qualityColor = clrGray;
    switch(g_trendAnalysis.quality) {
        case VERY_STRONG_TREND_QUALITY:
            qualityText = "极强";
            qualityColor = clrLime;
            break;
        case STRONG_TREND_QUALITY:
            qualityText = "强势";
            qualityColor = clrGreen;
            break;
        case EARLY_TREND_QUALITY:
            qualityText = "初期";
            qualityColor = clrYellow;
            break;
        default:
            qualityText = "无";
            qualityColor = clrGray;
    }

    ObjectSetString(0, "TrendQualityLabel", OBJPROP_TEXT,
                   StringFormat("趋势质量: %s", qualityText));
    ObjectSetInteger(0, "TrendQualityLabel", OBJPROP_COLOR, qualityColor);

    // 斐波那契回撤显示
    ObjectSetString(0, "FibRetracementLabel", OBJPROP_TEXT,
                   StringFormat("斐波回撤: %.3f", g_trendAnalysis.fibonacciRetrace));

    // 多时间框架一致性显示
    ObjectSetString(0, "MTFAlignLabel", OBJPROP_TEXT,
                   StringFormat("多框架一致: %s", g_trendAnalysis.multiTimeframeAlign ? "是" : "否"));
    ObjectSetInteger(0, "MTFAlignLabel", OBJPROP_COLOR,
                    g_trendAnalysis.multiTimeframeAlign ? clrLime : clrGray);

    // 智能过滤器状态显示
    double testProb = CalculateEnhancedBreakoutProbability(g_tfCompression, g_indicators, g_trendAnalysis);
    bool filterPassed = ApplyAdvancedTrendFilter(testProb);
    ObjectSetString(0, "TrendFilterLabel", OBJPROP_TEXT,
                   StringFormat("智能过滤: %s", Enable_Advanced_Trend_Filter ?
                               (filterPassed ? "通过" : "拒绝") : "关闭"));
    ObjectSetInteger(0, "TrendFilterLabel", OBJPROP_COLOR,
                    Enable_Advanced_Trend_Filter ?
                    (filterPassed ? clrLime : clrRed) : clrGray);

    // 根据连续Bar数设置颜色
    color consColor = clrGray;
    if(g_trendAnalysis.consecutiveBars >= 3) consColor = clrGreen;
    if(g_trendAnalysis.consecutiveBars >= 5) consColor = clrLime;
    ObjectSetInteger(0, TREND_CONSECUTIVE_LABEL, OBJPROP_COLOR, consColor);

    // 根据动量评分设置颜色
    color momentumColor = clrGray;
    if(g_trendAnalysis.momentumScore > 65) momentumColor = clrLime;
    else if(g_trendAnalysis.momentumScore > 50) momentumColor = clrGreen;
    else if(g_trendAnalysis.momentumScore < 35) momentumColor = clrRed;
    else if(g_trendAnalysis.momentumScore < 50) momentumColor = clrOrange;
    ObjectSetInteger(0, MOMENTUM_SCORE_LABEL, OBJPROP_COLOR, momentumColor);

    // 分析预期爆发方向
    AnalyzeExpectedDirection();
}

//+------------------------------------------------------------------+
//| Expert tick function                                               |
//+------------------------------------------------------------------+
void OnTick() {
    if(!g_initialized) return;

    // 仅在OnTick中更新基础指标数据（每个tick更新）
    g_indicators.volatility = CalculateVolatilityCompression(MediumPeriod);
    g_indicators.bbWidth = CalculateBollingerBandWidth(MediumPeriod, BW_StdDev);
    g_indicators.atrValue = CalculateATRDynamics(ShortPeriod);
    g_indicators.volumeCompress = CalculateVolumeCompression(MediumPeriod);
    g_indicators.candlePattern = IdentifyCandlePattern();

    // 注意：显示更新已移至OnTimer中进行分层管理
    // 这样可以避免每个tick都更新UI，提升性能
}

//+------------------------------------------------------------------+
//| Timer function                                                     |
//+------------------------------------------------------------------+
void OnTimer() {
    if(!g_initialized) return;

    static datetime lastLightUpdate = 0;
    static datetime lastMediumUpdate = 0;
    static datetime lastHeavyUpdate = 0;
    static datetime lastParamUpdate = 0;

    datetime currentTime = TimeCurrent();

    // 更新动态参数（每分钟更新一次）
    if(currentTime - lastParamUpdate >= 60) {
        AdjustDynamicParameters();
        lastParamUpdate = currentTime;
    }

    // 轻量级更新（每30秒）
    if(currentTime - lastLightUpdate >= 30) {
        UpdateDisplay();
        lastLightUpdate = currentTime;
    }

    // 中等负载更新（每60秒）
    if(currentTime - lastMediumUpdate >= 60) {
        UpdateTrendDisplay();
        ShowMarketStateInUI();
        lastMediumUpdate = currentTime;
    }

    // 重量级更新（每120秒）
    if(currentTime - lastHeavyUpdate >= 120) {
        if(EnableMultiTimeframe) {
            UpdateMultiTimeframeAnalysis();
        }
        CheckAndAlertBreakout();
        lastHeavyUpdate = currentTime;
    }

    // 智能健康检查（动态频率）
    CheckTrendHealth();

    // 清理过期警报箭头
    CleanupExpiredAlerts();
}

//+------------------------------------------------------------------+
//| 智能健康检查机制                                                   |
//+------------------------------------------------------------------+
void CheckTrendHealth() {
    static datetime lastCheck = 0;
    datetime now = TimeCurrent();

    // 基础检查间隔（15分钟）
    int baseInterval = 900;

    // 动态调整检查频率
    int dynamicInterval = baseInterval;

    // 当趋势强度高或市场波动大时增加检查频率
    if(g_trendAnalysis.strength >= STRENGTH_MODERATE ||
       g_CurrentMarketState == MARKET_HIGH_VOL) {
        dynamicInterval = baseInterval / 2; // 7.5分钟
    }

    // 当有连续趋势时进一步增加频率
    if(g_trendAnalysis.consecutiveBars >= 3) {
        dynamicInterval = baseInterval / 3; // 5分钟
    }

    // 事件驱动市场特别关注
    if(g_CurrentMarketState == MARKET_EVENT_DRIVEN) {
        dynamicInterval = 300; // 5分钟
    }

    if(now - lastCheck < dynamicInterval) return;

    // 健康检查逻辑（静默执行，仅在异常情况下输出）
    if(g_trendAnalysis.consecutiveBars >= 8) {
        // 仅在极端连续趋势时输出警告
        Print("警告：检测到极端连续趋势 ", g_trendAnalysis.consecutiveBars, " 根K线，请注意风险");
    }

    lastCheck = now;
}

//+------------------------------------------------------------------+
//| 清理过期警报箭头                                                   |
//+------------------------------------------------------------------+
void CleanupExpiredAlerts() {
    datetime now = TimeCurrent();
    int total = ObjectsTotal(0);
    for(int i = total-1; i >= 0; i--) {
        string name = ObjectName(0, i);
        if(StringFind(name, "Alert_") == 0) {
            datetime createTime = (datetime)StringToInteger(StringSubstr(name, 6));
            if(now - createTime > 30) {
                ObjectDelete(0, name);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查并发出爆发警报                                                 |
//+------------------------------------------------------------------+
void CheckAndAlertBreakout() {
    if(!EnableMultiTimeframe) return;

    static datetime lastAlert = 0;
    datetime now = TimeCurrent();

    // 最小警报间隔3分钟
    if(now - lastAlert < 180) return;
    
    double breakoutProb = CalculateEnhancedBreakoutProbability(g_tfCompression, g_indicators, g_trendAnalysis);

    // === 应用Deepseek建议的智能过滤器 ===
    if(!ApplyAdvancedTrendFilter(breakoutProb)) {
        return; // 过滤器拒绝信号，直接返回
    }

    // 主警报条件（高置信度）
    bool mainAlert = breakoutProb >= 80.0;

    // 次警报条件（中等置信度+趋势确认）
    bool secondaryAlert = (breakoutProb >= 65.0) &&
                         (g_trendAnalysis.consecutiveBars >= 3) &&
                         (g_trendAnalysis.strength >= STRENGTH_MODERATE);

    // 特殊条件（低概率但多时间框架高度一致）
    bool specialAlert = (breakoutProb >= 55.0) &&
                       (g_tfCompression.consistencyScore >= 90.0);

    if(mainAlert || secondaryAlert || specialAlert) {
        string alertType = "主警报";
        color alertColor = clrRed;

        if(secondaryAlert && !mainAlert) {
            alertType = "次警报";
            alertColor = clrOrange;
        }
        else if(specialAlert && !mainAlert && !secondaryAlert) {
            alertType = "特殊警报";
            alertColor = clrMagenta;
        }

        string alertMessage = StringFormat("[%s] %s: 爆发概率 %.1f%%\n",
                                          alertType, _Symbol, breakoutProb);
        alertMessage += StringFormat("趋势连续: %d根K线, 强度: %s\n",
                                   g_trendAnalysis.consecutiveBars,
                                   GetTrendStrengthDescription(g_trendAnalysis.strength));
        alertMessage += StringFormat("时间框架一致性: %.1f%%",
                                   g_tfCompression.consistencyScore);

        // 添加市场状态信息
        alertMessage += StringFormat("\n市场状态: %s", EnumToString(g_CurrentMarketState));

        // 不同级别警报使用不同方式通知
        if(mainAlert) {
            Alert(alertMessage);
            SendNotification(alertMessage);
        }
        else {
            Print(alertMessage);
        }

        // 在图表上显示可视化警报
        CreateAlertArrow(alertColor, alertType);

        lastAlert = now;
    }
}

//+------------------------------------------------------------------+
//| 创建警报箭头                                                       |
//+------------------------------------------------------------------+
void CreateAlertArrow(color clr, string type) {
    string objName = "Alert_" + IntegerToString(TimeCurrent());

    if(ObjectCreate(0, objName, OBJ_ARROW_BUY, 0, TimeCurrent(), iLow(_Symbol, PERIOD_CURRENT, 0))) {
        ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
        ObjectSetInteger(0, objName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
        ObjectSetString(0, objName, OBJPROP_TEXT, type);
    }
}




