//+------------------------------------------------------------------+
//|                                          非对称订单权重系统测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "非对称订单权重系统功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input double TargetProfitLossRatio = 3.8; // 目标盈损比

//+------------------------------------------------------------------+
//| 非对称订单权重系统测试脚本                                        |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始非对称订单权重系统测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("目标盈损比: ", DoubleToString(TargetProfitLossRatio, 2), ":1");
    Print("========================================");
    
    // 测试1: 趋势强度分析
    TestTrendStrengthAnalysis();
    
    // 测试2: 权重分配计算
    TestWeightAllocationCalculation();
    
    // 测试3: 订单数量分配
    TestOrderCountDistribution();
    
    // 测试4: 盈损比计算
    TestProfitLossRatioCalculation();
    
    // 测试5: 非对称权重应用
    TestAsymmetricWeightApplication();
    
    // 测试6: 完整权重优化流程
    TestCompleteWeightOptimizationWorkflow();
    
    Print("========================================");
    Print("✅ 非对称订单权重系统测试完成");
}

//+------------------------------------------------------------------+
//| 测试趋势强度分析                                                  |
//+------------------------------------------------------------------+
void TestTrendStrengthAnalysis()
{
    Print("📋 测试1: 趋势强度分析");
    
    // 测试不同趋势强度的分类
    struct TrendStrengthTest {
        double trendStrength;
        int expectedLevel;
        string description;
    };
    
    TrendStrengthTest tests[] = {
        {85.0, 4, "极强趋势"},
        {70.0, 3, "强趋势"},
        {50.0, 2, "中等趋势"},
        {30.0, 1, "弱趋势"},
        {10.0, 0, "极弱趋势"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        TrendStrengthTest tst = tests[i];
        
        // 计算趋势强度等级
        int calculatedLevel = ClassifyTestTrendStrength(tst.trendStrength);
        
        Print("场景: ", tst.description);
        Print("  趋势强度: ", DoubleToString(tst.trendStrength, 1), "%");
        Print("  计算等级: ", calculatedLevel, " (", GetTestTrendStrengthDescription(calculatedLevel), ")");
        Print("  预期等级: ", tst.expectedLevel, " (", GetTestTrendStrengthDescription(tst.expectedLevel), ")");
        
        bool levelCorrect = (calculatedLevel == tst.expectedLevel);
        Print("  等级验证: ", levelCorrect ? "✓" : "✗");
    }
    
    Print("✅ 趋势强度分析完成\n");
}

//+------------------------------------------------------------------+
//| 分类测试趋势强度                                                  |
//+------------------------------------------------------------------+
int ClassifyTestTrendStrength(double strength) {
    if(strength >= 80.0) return 4; // 极强趋势
    if(strength >= 60.0) return 3; // 强趋势
    if(strength >= 40.0) return 2; // 中等趋势
    if(strength >= 20.0) return 1; // 弱趋势
    return 0; // 极弱趋势
}

//+------------------------------------------------------------------+
//| 获取测试趋势强度描述                                              |
//+------------------------------------------------------------------+
string GetTestTrendStrengthDescription(int level) {
    switch(level) {
        case 4: return "极强趋势";
        case 3: return "强趋势";
        case 2: return "中等趋势";
        case 1: return "弱趋势";
        case 0: return "极弱趋势";
        default: return "未知趋势";
    }
}

//+------------------------------------------------------------------+
//| 测试权重分配计算                                                  |
//+------------------------------------------------------------------+
void TestWeightAllocationCalculation()
{
    Print("📋 测试2: 权重分配计算");
    
    // 测试不同趋势强度下的权重分配
    struct WeightAllocationTest {
        double trendStrength;
        double trendConfidence;
        double expectedWithTrendWeight;
        double expectedAgainstTrendWeight;
        string description;
    };
    
    WeightAllocationTest tests[] = {
        {85.0, 80.0, 0.85, 0.15, "极强趋势高置信度"},
        {70.0, 75.0, 0.81, 0.19, "强趋势高置信度"},
        {50.0, 70.0, 0.72, 0.28, "中等趋势高置信度"},
        {30.0, 65.0, 0.60, 0.40, "弱趋势低置信度"},
        {20.0, 45.0, 0.60, 0.40, "极弱趋势低置信度"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        WeightAllocationTest wat = tests[i];
        
        // 计算权重分配
        double withTrendWeight = CalculateTestWithTrendWeight(wat.trendStrength, wat.trendConfidence);
        double againstTrendWeight = 1.0 - withTrendWeight;
        
        Print("场景: ", wat.description);
        Print("  趋势强度: ", DoubleToString(wat.trendStrength, 1), "%");
        Print("  趋势置信度: ", DoubleToString(wat.trendConfidence, 1), "%");
        Print("  计算顺趋势权重: ", DoubleToString(withTrendWeight*100, 1), "%");
        Print("  计算逆趋势权重: ", DoubleToString(againstTrendWeight*100, 1), "%");
        Print("  预期顺趋势权重: ", DoubleToString(wat.expectedWithTrendWeight*100, 1), "%");
        Print("  预期逆趋势权重: ", DoubleToString(wat.expectedAgainstTrendWeight*100, 1), "%");
        
        bool weightCorrect = (MathAbs(withTrendWeight - wat.expectedWithTrendWeight) < 0.05);
        Print("  权重验证: ", weightCorrect ? "✓" : "✗");
    }
    
    Print("✅ 权重分配计算完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试顺趋势权重                                                |
//+------------------------------------------------------------------+
double CalculateTestWithTrendWeight(double trendStrength, double trendConfidence) {
    double maxWithTrendWeight = 0.85; // 最大顺趋势权重85%
    double trendConfidenceThreshold = 70.0; // 趋势置信度阈值70%
    
    // 检查趋势置信度
    if(trendConfidence < trendConfidenceThreshold) {
        return 0.6; // 低置信度时使用中性权重
    }
    
    // 根据趋势强度调整权重
    if(trendStrength >= 80.0) {
        return maxWithTrendWeight; // 极强趋势，使用最大权重
    } else if(trendStrength >= 60.0) {
        return maxWithTrendWeight * 0.95; // 强趋势，略微降低
    } else if(trendStrength >= 40.0) {
        return maxWithTrendWeight * 0.85; // 中等趋势，适度降低
    } else if(trendStrength >= 20.0) {
        return maxWithTrendWeight * 0.70; // 弱趋势，显著降低
    } else {
        return 0.5; // 极弱趋势，接近平衡
    }
}

//+------------------------------------------------------------------+
//| 测试订单数量分配                                                  |
//+------------------------------------------------------------------+
void TestOrderCountDistribution()
{
    Print("📋 测试3: 订单数量分配");
    
    // 测试不同总订单数和权重下的分配
    struct OrderDistributionTest {
        int totalOrders;
        double withTrendWeight;
        int expectedWithTrendOrders;
        int expectedAgainstTrendOrders;
        string description;
    };
    
    OrderDistributionTest tests[] = {
        {5, 0.85, 4, 1, "5个订单85%权重"},
        {6, 0.80, 5, 1, "6个订单80%权重"},
        {8, 0.75, 6, 2, "8个订单75%权重"},
        {10, 0.70, 7, 3, "10个订单70%权重"},
        {4, 0.60, 2, 2, "4个订单60%权重"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        OrderDistributionTest odt = tests[i];
        
        // 计算订单分配
        int withTrendOrders = (int)(odt.totalOrders * odt.withTrendWeight);
        int againstTrendOrders = odt.totalOrders - withTrendOrders;
        
        // 确保至少有一个订单
        withTrendOrders = MathMax(1, withTrendOrders);
        againstTrendOrders = MathMax(1, againstTrendOrders);
        
        Print("场景: ", odt.description);
        Print("  总订单数: ", odt.totalOrders);
        Print("  顺趋势权重: ", DoubleToString(odt.withTrendWeight*100, 1), "%");
        Print("  计算顺趋势订单: ", withTrendOrders);
        Print("  计算逆趋势订单: ", againstTrendOrders);
        Print("  预期顺趋势订单: ", odt.expectedWithTrendOrders);
        Print("  预期逆趋势订单: ", odt.expectedAgainstTrendOrders);
        
        bool distributionCorrect = (withTrendOrders == odt.expectedWithTrendOrders) && 
                                  (againstTrendOrders == odt.expectedAgainstTrendOrders);
        Print("  分配验证: ", distributionCorrect ? "✓" : "✗");
    }
    
    Print("✅ 订单数量分配完成\n");
}

//+------------------------------------------------------------------+
//| 测试盈损比计算                                                    |
//+------------------------------------------------------------------+
void TestProfitLossRatioCalculation()
{
    Print("📋 测试4: 盈损比计算");
    
    // 测试不同权重和趋势强度下的盈损比
    struct ProfitLossRatioTest {
        double withTrendWeight;
        double trendStrength;
        double expectedProfitLossRatio;
        string description;
    };
    
    ProfitLossRatioTest tests[] = {
        {0.85, 80.0, 3.5, "85%权重强趋势"},
        {0.80, 70.0, 3.2, "80%权重中强趋势"},
        {0.75, 60.0, 2.8, "75%权重中等趋势"},
        {0.70, 50.0, 2.4, "70%权重弱趋势"},
        {0.60, 30.0, 1.8, "60%权重极弱趋势"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        ProfitLossRatioTest plrt = tests[i];
        
        // 计算盈损比
        double calculatedRatio = CalculateTestProfitLossRatio(plrt.withTrendWeight, plrt.trendStrength);
        
        Print("场景: ", plrt.description);
        Print("  顺趋势权重: ", DoubleToString(plrt.withTrendWeight*100, 1), "%");
        Print("  趋势强度: ", DoubleToString(plrt.trendStrength, 1), "%");
        Print("  计算盈损比: ", DoubleToString(calculatedRatio, 2), ":1");
        Print("  预期盈损比: ", DoubleToString(plrt.expectedProfitLossRatio, 2), ":1");
        
        bool ratioCorrect = (MathAbs(calculatedRatio - plrt.expectedProfitLossRatio) < 0.3);
        Print("  盈损比验证: ", ratioCorrect ? "✓" : "✗");
    }
    
    Print("✅ 盈损比计算完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试盈损比                                                    |
//+------------------------------------------------------------------+
double CalculateTestProfitLossRatio(double withTrendWeight, double trendStrength) {
    // 基础盈损比
    double baseProfitLossRatio = 1.5;
    
    // 根据顺趋势权重调整
    baseProfitLossRatio = 1.5 + (withTrendWeight - 0.5) * 4.6; // 最高可达3.8
    
    // 根据趋势强度调整
    double strengthFactor = trendStrength / 100.0;
    baseProfitLossRatio *= (0.8 + strengthFactor * 0.4); // 0.8-1.2倍调整
    
    return MathMax(1.0, MathMin(5.0, baseProfitLossRatio));
}

//+------------------------------------------------------------------+
//| 测试非对称权重应用                                                |
//+------------------------------------------------------------------+
void TestAsymmetricWeightApplication()
{
    Print("📋 测试5: 非对称权重应用");
    
    // 基础订单参数
    struct TestOrderParams {
        int orderCount;
        int intervalSeconds;
        double orderSize;
    };
    
    TestOrderParams baseParams = {5, 600, 0.05};
    
    Print("基础参数: ", baseParams.orderCount, "个订单, ", 
          baseParams.intervalSeconds, "秒间隔, ", 
          DoubleToString(baseParams.orderSize, 3), "手");
    Print("");
    
    // 测试不同权重配置的参数调整
    struct WeightApplicationTest {
        int primaryDirection; // 0=顺趋势, 1=逆趋势, 2=中性
        double primaryWeight;
        string description;
    };
    
    WeightApplicationTest tests[] = {
        {0, 0.85, "顺趋势主导85%"},
        {0, 0.75, "顺趋势主导75%"},
        {1, 0.70, "逆趋势主导70%"},
        {2, 0.60, "中性分配60%"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        WeightApplicationTest wat = tests[i];
        
        // 模拟权重应用逻辑
        TestOrderParams adjustedParams = baseParams;
        
        if(wat.primaryDirection == 0) { // 顺趋势为主
            adjustedParams.orderCount = (int)(adjustedParams.orderCount * wat.primaryWeight / 0.5);
            adjustedParams.orderSize *= (1.0 + wat.primaryWeight * 0.2); // 增加规模
            adjustedParams.intervalSeconds = (int)(adjustedParams.intervalSeconds * 0.9); // 减少间隔
        } else if(wat.primaryDirection == 1) { // 逆趋势为主
            adjustedParams.orderCount = (int)(adjustedParams.orderCount * (1.0 - wat.primaryWeight) / 0.5);
            adjustedParams.orderSize *= (1.0 - (1.0 - wat.primaryWeight) * 0.1); // 减少规模
            adjustedParams.intervalSeconds = (int)(adjustedParams.intervalSeconds * 1.1); // 增加间隔
        } else { // 中性分配
            adjustedParams.orderCount = (int)(adjustedParams.orderCount * 0.8); // 减少总数
        }
        
        // 确保参数在合理范围内
        adjustedParams.orderCount = MathMax(1, MathMin(10, adjustedParams.orderCount));
        adjustedParams.orderSize = MathMax(0.01, MathMin(0.1, adjustedParams.orderSize));
        adjustedParams.intervalSeconds = MathMax(300, MathMin(900, adjustedParams.intervalSeconds));
        
        Print("权重配置: ", wat.description);
        Print("  订单数量: ", baseParams.orderCount, " → ", adjustedParams.orderCount);
        Print("  订单间隔: ", baseParams.intervalSeconds, "秒 → ", adjustedParams.intervalSeconds, "秒");
        Print("  订单规模: ", DoubleToString(baseParams.orderSize, 3), "手 → ", 
              DoubleToString(adjustedParams.orderSize, 3), "手");
        
        // 验证参数合理性
        bool paramsValid = (adjustedParams.orderCount >= 1 && adjustedParams.orderCount <= 10) &&
                          (adjustedParams.intervalSeconds >= 300 && adjustedParams.intervalSeconds <= 900) &&
                          (adjustedParams.orderSize >= 0.01 && adjustedParams.orderSize <= 0.1);
        Print("  参数验证: ", paramsValid ? "✓" : "✗");
    }
    
    Print("✅ 非对称权重应用完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整权重优化流程                                              |
//+------------------------------------------------------------------+
void TestCompleteWeightOptimizationWorkflow()
{
    Print("📋 测试6: 完整权重优化流程");
    
    // 模拟一个完整的权重优化周期
    Print("模拟权重优化流程:");
    
    // 场景：市场从震荡 → 趋势形成 → 趋势加强 → 趋势衰减 → 回归震荡
    struct WorkflowStep {
        string stepName;
        double trendStrength;
        double trendConfidence;
        double expectedWithTrendWeight;
        double expectedProfitLossRatio;
    };
    
    WorkflowStep steps[] = {
        {"震荡市场", 25.0, 45.0, 0.60, 1.8},
        {"趋势萌芽", 45.0, 65.0, 0.72, 2.4},
        {"趋势确立", 65.0, 75.0, 0.81, 3.0},
        {"趋势加强", 80.0, 85.0, 0.85, 3.6},
        {"趋势衰减", 60.0, 70.0, 0.81, 2.8},
        {"回归震荡", 30.0, 50.0, 0.60, 1.9}
    };
    
    double currentProfitLossRatio = 1.5;
    
    for(int i = 0; i < ArraySize(steps); i++) {
        WorkflowStep step = steps[i];
        
        // 计算权重和盈损比
        double withTrendWeight = CalculateTestWithTrendWeight(step.trendStrength, step.trendConfidence);
        double profitLossRatio = CalculateTestProfitLossRatio(withTrendWeight, step.trendStrength);
        
        // 模拟盈损比改进
        currentProfitLossRatio = currentProfitLossRatio * 0.8 + profitLossRatio * 0.2;
        
        Print("步骤", i+1, ": ", step.stepName);
        Print("  趋势强度: ", DoubleToString(step.trendStrength, 1), "%");
        Print("  趋势置信度: ", DoubleToString(step.trendConfidence, 1), "%");
        Print("  顺趋势权重: ", DoubleToString(withTrendWeight*100, 1), "%");
        Print("  当前盈损比: ", DoubleToString(currentProfitLossRatio, 2), ":1");
        Print("  目标达成: ", (currentProfitLossRatio >= TargetProfitLossRatio) ? "是" : "否");
        
        bool stepCorrect = (MathAbs(withTrendWeight - step.expectedWithTrendWeight) < 0.05) &&
                          (MathAbs(profitLossRatio - step.expectedProfitLossRatio) < 0.3);
        Print("  流程验证: ", stepCorrect ? "✓" : "✗");
        Print("");
    }
    
    Print("最终盈损比: ", DoubleToString(currentProfitLossRatio, 2), ":1");
    Print("目标盈损比: ", DoubleToString(TargetProfitLossRatio, 2), ":1");
    Print("目标达成率: ", DoubleToString((currentProfitLossRatio / TargetProfitLossRatio) * 100, 1), "%");
    
    Print("✅ 完整权重优化流程完成\n");
}
