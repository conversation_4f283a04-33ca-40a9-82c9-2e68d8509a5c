{"cells": [{"cell_type": "code", "execution_count": 196, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 导入MT5的模块\n", "import MetaTrader5 as mt5\n", "from pprint import pprint\n", "import datetime\n", "import pytz\n", "import numpy as np\n", "import pandas as pd\n", "\n", "if __name__ in \"__main__\":\n", "    path = \"D:\\\\MetaTrader 5\\\\terminal64.exe\"\n", "    # 第一种连接方式\n", "    is_ok = mt5.initialize(path=path,\n", "                           server=\"Exness-MT5Trial5\",\n", "                           login=76314481,\n", "                           password=\"6ixuhbWp\",\n", "                           timeout=2000)\n", "\n", "    if not is_ok:\n", "        print(\"连接MT5失败, 错误原因: \", mt5.last_error())\n", "        mt5.shutdown()\n", "        quit()"]}, {"cell_type": "code", "execution_count": 197, "outputs": [], "source": ["utc_tzone = pytz.timezone(\"UTC\") # Asia/Shanghai"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 198, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-04-19 06:47:36.551237+00:00\n"]}], "source": ["utc_time = datetime.datetime.now(tz=utc_tzone)\n", "print(utc_time)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 199, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-04-19 02:47:36.551237+00:00\n"]}], "source": ["# 从指定的时间往前获取指定数据行\n", "# rates = mt5.copy_rates_from(\"EURUSDz\",mt5.TIMEFRAME_M10,utc_time,100)\n", "# print(type(rates))\n", "\n", "# 获取指定范围的数据\n", "# rates = mt5.copy_rates_from_pos(\"EURUSDz\",mt5.TIMEFRAME_M10,0,100)\n", "\n", "# 获取指定时间范围的时间\n", "delta = datetime.<PERSON><PERSON><PERSON>(hours=-4)\n", "start_time = utc_time + delta\n", "stop_time = utc_time\n", "rates = mt5.copy_rates_range(\"EURUSDz\",mt5.TIMEFRAME_M10,start_time,stop_time)\n", "print(start_time)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 200, "outputs": [], "source": ["# 将数据转化为pandas数据格式\n", "df = pd.DataFrame(rates)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 201, "outputs": [{"data": {"text/plain": "          time     open     high      low    close  tick_volume  spread   \n0   1681872600  1.09727  1.09740  1.09723  1.09739           67       0  \\\n1   1681873200  1.09741  1.09754  1.09732  1.09732           87       0   \n2   1681873800  1.09731  1.09731  1.09710  1.09716           77       0   \n3   1681874400  1.09717  1.09738  1.09706  1.09737           59       0   \n4   1681875000  1.09738  1.09747  1.09715  1.09720           71       0   \n5   1681875600  1.09721  1.09724  1.09700  1.09707           55       0   \n6   1681876200  1.09709  1.09747  1.09709  1.09741           78       0   \n7   1681876800  1.09742  1.09746  1.09728  1.09736           80       0   \n8   1681877400  1.09735  1.09740  1.09729  1.09734           42       0   \n9   1681878000  1.09735  1.09736  1.09719  1.09720           39       0   \n10  1681878600  1.09719  1.09722  1.09692  1.09696           49       0   \n11  1681879200  1.09697  1.09699  1.09678  1.09683           58       0   \n12  1681879800  1.09686  1.09690  1.09674  1.09687           62       0   \n13  1681880400  1.09686  1.09704  1.09676  1.09680           96       0   \n14  1681881000  1.09678  1.09681  1.09663  1.09679           70       0   \n15  1681881600  1.09681  1.09696  1.09669  1.09669           90       0   \n16  1681882200  1.09670  1.09675  1.09634  1.09634          142       0   \n17  1681882800  1.09635  1.09639  1.09607  1.09614          194       0   \n18  1681883400  1.09615  1.09626  1.09602  1.09611          126       0   \n19  1681884000  1.09612  1.09704  1.09612  1.09634          723       0   \n20  1681884600  1.09631  1.09634  1.09548  1.09548          608       0   \n21  1681885200  1.09550  1.09561  1.09466  1.09521          529       0   \n22  1681885800  1.09522  1.09551  1.09506  1.09523          458       0   \n23  1681886400  1.09521  1.09554  1.09497  1.09550          341       0   \n\n    real_volume  \n0             0  \n1             0  \n2             0  \n3             0  \n4             0  \n5             0  \n6             0  \n7             0  \n8             0  \n9             0  \n10            0  \n11            0  \n12            0  \n13            0  \n14            0  \n15            0  \n16            0  \n17            0  \n18            0  \n19            0  \n20            0  \n21            0  \n22            0  \n23            0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>open</th>\n      <th>high</th>\n      <th>low</th>\n      <th>close</th>\n      <th>tick_volume</th>\n      <th>spread</th>\n      <th>real_volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1681872600</td>\n      <td>1.09727</td>\n      <td>1.09740</td>\n      <td>1.09723</td>\n      <td>1.09739</td>\n      <td>67</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>1681873200</td>\n      <td>1.09741</td>\n      <td>1.09754</td>\n      <td>1.09732</td>\n      <td>1.09732</td>\n      <td>87</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>1681873800</td>\n      <td>1.09731</td>\n      <td>1.09731</td>\n      <td>1.09710</td>\n      <td>1.09716</td>\n      <td>77</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>1681874400</td>\n      <td>1.09717</td>\n      <td>1.09738</td>\n      <td>1.09706</td>\n      <td>1.09737</td>\n      <td>59</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>1681875000</td>\n      <td>1.09738</td>\n      <td>1.09747</td>\n      <td>1.09715</td>\n      <td>1.09720</td>\n      <td>71</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>1681875600</td>\n      <td>1.09721</td>\n      <td>1.09724</td>\n      <td>1.09700</td>\n      <td>1.09707</td>\n      <td>55</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>1681876200</td>\n      <td>1.09709</td>\n      <td>1.09747</td>\n      <td>1.09709</td>\n      <td>1.09741</td>\n      <td>78</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>1681876800</td>\n      <td>1.09742</td>\n      <td>1.09746</td>\n      <td>1.09728</td>\n      <td>1.09736</td>\n      <td>80</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>1681877400</td>\n      <td>1.09735</td>\n      <td>1.09740</td>\n      <td>1.09729</td>\n      <td>1.09734</td>\n      <td>42</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>1681878000</td>\n      <td>1.09735</td>\n      <td>1.09736</td>\n      <td>1.09719</td>\n      <td>1.09720</td>\n      <td>39</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>1681878600</td>\n      <td>1.09719</td>\n      <td>1.09722</td>\n      <td>1.09692</td>\n      <td>1.09696</td>\n      <td>49</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>1681879200</td>\n      <td>1.09697</td>\n      <td>1.09699</td>\n      <td>1.09678</td>\n      <td>1.09683</td>\n      <td>58</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>1681879800</td>\n      <td>1.09686</td>\n      <td>1.09690</td>\n      <td>1.09674</td>\n      <td>1.09687</td>\n      <td>62</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>1681880400</td>\n      <td>1.09686</td>\n      <td>1.09704</td>\n      <td>1.09676</td>\n      <td>1.09680</td>\n      <td>96</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>1681881000</td>\n      <td>1.09678</td>\n      <td>1.09681</td>\n      <td>1.09663</td>\n      <td>1.09679</td>\n      <td>70</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>1681881600</td>\n      <td>1.09681</td>\n      <td>1.09696</td>\n      <td>1.09669</td>\n      <td>1.09669</td>\n      <td>90</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>1681882200</td>\n      <td>1.09670</td>\n      <td>1.09675</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>142</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>1681882800</td>\n      <td>1.09635</td>\n      <td>1.09639</td>\n      <td>1.09607</td>\n      <td>1.09614</td>\n      <td>194</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>1681883400</td>\n      <td>1.09615</td>\n      <td>1.09626</td>\n      <td>1.09602</td>\n      <td>1.09611</td>\n      <td>126</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>1681884000</td>\n      <td>1.09612</td>\n      <td>1.09704</td>\n      <td>1.09612</td>\n      <td>1.09634</td>\n      <td>723</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>1681884600</td>\n      <td>1.09631</td>\n      <td>1.09634</td>\n      <td>1.09548</td>\n      <td>1.09548</td>\n      <td>608</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>1681885200</td>\n      <td>1.09550</td>\n      <td>1.09561</td>\n      <td>1.09466</td>\n      <td>1.09521</td>\n      <td>529</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>1681885800</td>\n      <td>1.09522</td>\n      <td>1.09551</td>\n      <td>1.09506</td>\n      <td>1.09523</td>\n      <td>458</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>1681886400</td>\n      <td>1.09521</td>\n      <td>1.09554</td>\n      <td>1.09497</td>\n      <td>1.09550</td>\n      <td>341</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 201, "metadata": {}, "output_type": "execute_result"}], "source": ["df"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 202, "outputs": [{"data": {"text/plain": "                  time     open     high      low    close  tick_volume   \n0  2023-04-19 02:50:00  1.09727  1.09740  1.09723  1.09739           67  \\\n1  2023-04-19 03:00:00  1.09741  1.09754  1.09732  1.09732           87   \n2  2023-04-19 03:10:00  1.09731  1.09731  1.09710  1.09716           77   \n3  2023-04-19 03:20:00  1.09717  1.09738  1.09706  1.09737           59   \n4  2023-04-19 03:30:00  1.09738  1.09747  1.09715  1.09720           71   \n5  2023-04-19 03:40:00  1.09721  1.09724  1.09700  1.09707           55   \n6  2023-04-19 03:50:00  1.09709  1.09747  1.09709  1.09741           78   \n7  2023-04-19 04:00:00  1.09742  1.09746  1.09728  1.09736           80   \n8  2023-04-19 04:10:00  1.09735  1.09740  1.09729  1.09734           42   \n9  2023-04-19 04:20:00  1.09735  1.09736  1.09719  1.09720           39   \n10 2023-04-19 04:30:00  1.09719  1.09722  1.09692  1.09696           49   \n11 2023-04-19 04:40:00  1.09697  1.09699  1.09678  1.09683           58   \n12 2023-04-19 04:50:00  1.09686  1.09690  1.09674  1.09687           62   \n13 2023-04-19 05:00:00  1.09686  1.09704  1.09676  1.09680           96   \n14 2023-04-19 05:10:00  1.09678  1.09681  1.09663  1.09679           70   \n15 2023-04-19 05:20:00  1.09681  1.09696  1.09669  1.09669           90   \n16 2023-04-19 05:30:00  1.09670  1.09675  1.09634  1.09634          142   \n17 2023-04-19 05:40:00  1.09635  1.09639  1.09607  1.09614          194   \n18 2023-04-19 05:50:00  1.09615  1.09626  1.09602  1.09611          126   \n19 2023-04-19 06:00:00  1.09612  1.09704  1.09612  1.09634          723   \n20 2023-04-19 06:10:00  1.09631  1.09634  1.09548  1.09548          608   \n21 2023-04-19 06:20:00  1.09550  1.09561  1.09466  1.09521          529   \n22 2023-04-19 06:30:00  1.09522  1.09551  1.09506  1.09523          458   \n23 2023-04-19 06:40:00  1.09521  1.09554  1.09497  1.09550          341   \n\n    spread  real_volume  \n0        0            0  \n1        0            0  \n2        0            0  \n3        0            0  \n4        0            0  \n5        0            0  \n6        0            0  \n7        0            0  \n8        0            0  \n9        0            0  \n10       0            0  \n11       0            0  \n12       0            0  \n13       0            0  \n14       0            0  \n15       0            0  \n16       0            0  \n17       0            0  \n18       0            0  \n19       0            0  \n20       0            0  \n21       0            0  \n22       0            0  \n23       0            0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>open</th>\n      <th>high</th>\n      <th>low</th>\n      <th>close</th>\n      <th>tick_volume</th>\n      <th>spread</th>\n      <th>real_volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2023-04-19 02:50:00</td>\n      <td>1.09727</td>\n      <td>1.09740</td>\n      <td>1.09723</td>\n      <td>1.09739</td>\n      <td>67</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2023-04-19 03:00:00</td>\n      <td>1.09741</td>\n      <td>1.09754</td>\n      <td>1.09732</td>\n      <td>1.09732</td>\n      <td>87</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2023-04-19 03:10:00</td>\n      <td>1.09731</td>\n      <td>1.09731</td>\n      <td>1.09710</td>\n      <td>1.09716</td>\n      <td>77</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2023-04-19 03:20:00</td>\n      <td>1.09717</td>\n      <td>1.09738</td>\n      <td>1.09706</td>\n      <td>1.09737</td>\n      <td>59</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2023-04-19 03:30:00</td>\n      <td>1.09738</td>\n      <td>1.09747</td>\n      <td>1.09715</td>\n      <td>1.09720</td>\n      <td>71</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>5</th>\n      <td>2023-04-19 03:40:00</td>\n      <td>1.09721</td>\n      <td>1.09724</td>\n      <td>1.09700</td>\n      <td>1.09707</td>\n      <td>55</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>6</th>\n      <td>2023-04-19 03:50:00</td>\n      <td>1.09709</td>\n      <td>1.09747</td>\n      <td>1.09709</td>\n      <td>1.09741</td>\n      <td>78</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>2023-04-19 04:00:00</td>\n      <td>1.09742</td>\n      <td>1.09746</td>\n      <td>1.09728</td>\n      <td>1.09736</td>\n      <td>80</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>8</th>\n      <td>2023-04-19 04:10:00</td>\n      <td>1.09735</td>\n      <td>1.09740</td>\n      <td>1.09729</td>\n      <td>1.09734</td>\n      <td>42</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>2023-04-19 04:20:00</td>\n      <td>1.09735</td>\n      <td>1.09736</td>\n      <td>1.09719</td>\n      <td>1.09720</td>\n      <td>39</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>10</th>\n      <td>2023-04-19 04:30:00</td>\n      <td>1.09719</td>\n      <td>1.09722</td>\n      <td>1.09692</td>\n      <td>1.09696</td>\n      <td>49</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>2023-04-19 04:40:00</td>\n      <td>1.09697</td>\n      <td>1.09699</td>\n      <td>1.09678</td>\n      <td>1.09683</td>\n      <td>58</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>12</th>\n      <td>2023-04-19 04:50:00</td>\n      <td>1.09686</td>\n      <td>1.09690</td>\n      <td>1.09674</td>\n      <td>1.09687</td>\n      <td>62</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>13</th>\n      <td>2023-04-19 05:00:00</td>\n      <td>1.09686</td>\n      <td>1.09704</td>\n      <td>1.09676</td>\n      <td>1.09680</td>\n      <td>96</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>14</th>\n      <td>2023-04-19 05:10:00</td>\n      <td>1.09678</td>\n      <td>1.09681</td>\n      <td>1.09663</td>\n      <td>1.09679</td>\n      <td>70</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>15</th>\n      <td>2023-04-19 05:20:00</td>\n      <td>1.09681</td>\n      <td>1.09696</td>\n      <td>1.09669</td>\n      <td>1.09669</td>\n      <td>90</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>16</th>\n      <td>2023-04-19 05:30:00</td>\n      <td>1.09670</td>\n      <td>1.09675</td>\n      <td>1.09634</td>\n      <td>1.09634</td>\n      <td>142</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>17</th>\n      <td>2023-04-19 05:40:00</td>\n      <td>1.09635</td>\n      <td>1.09639</td>\n      <td>1.09607</td>\n      <td>1.09614</td>\n      <td>194</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>18</th>\n      <td>2023-04-19 05:50:00</td>\n      <td>1.09615</td>\n      <td>1.09626</td>\n      <td>1.09602</td>\n      <td>1.09611</td>\n      <td>126</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>19</th>\n      <td>2023-04-19 06:00:00</td>\n      <td>1.09612</td>\n      <td>1.09704</td>\n      <td>1.09612</td>\n      <td>1.09634</td>\n      <td>723</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>2023-04-19 06:10:00</td>\n      <td>1.09631</td>\n      <td>1.09634</td>\n      <td>1.09548</td>\n      <td>1.09548</td>\n      <td>608</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>2023-04-19 06:20:00</td>\n      <td>1.09550</td>\n      <td>1.09561</td>\n      <td>1.09466</td>\n      <td>1.09521</td>\n      <td>529</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>2023-04-19 06:30:00</td>\n      <td>1.09522</td>\n      <td>1.09551</td>\n      <td>1.09506</td>\n      <td>1.09523</td>\n      <td>458</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>2023-04-19 06:40:00</td>\n      <td>1.09521</td>\n      <td>1.09554</td>\n      <td>1.09497</td>\n      <td>1.09550</td>\n      <td>341</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 202, "metadata": {}, "output_type": "execute_result"}], "source": ["# 清洗时间\n", "df['time'] = pd.to_datetime(df['time'],unit='s')\n", "df"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 203, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['time', 'open', 'high', 'low', 'close', 'tick_volume', 'spread',\n", "       'real_volume'],\n", "      dtype='object')\n"]}], "source": ["# 查询所有字段\n", "print(df.columns)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 204, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time           datetime64[ns]\n", "open                  float64\n", "high                  float64\n", "low                   float64\n", "close                 float64\n", "tick_volume            uint64\n", "spread                  int32\n", "real_volume            uint64\n", "dtype: object\n"]}], "source": ["# 查询所有字段的数据类型\n", "print(df.dtypes)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 205, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24\n"]}], "source": ["# 查询有多少行数据\n", "print(df.shape[0])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 206, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8\n"]}], "source": ["# 查询多少列字段\n", "print(df.shape[1])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 207, "outputs": [{"data": {"text/plain": "                 time     open     high      low    close  tick_volume   \n0 2023-04-19 02:50:00  1.09727  1.09740  1.09723  1.09739           67  \\\n1 2023-04-19 03:00:00  1.09741  1.09754  1.09732  1.09732           87   \n2 2023-04-19 03:10:00  1.09731  1.09731  1.09710  1.09716           77   \n3 2023-04-19 03:20:00  1.09717  1.09738  1.09706  1.09737           59   \n4 2023-04-19 03:30:00  1.09738  1.09747  1.09715  1.09720           71   \n\n   spread  real_volume  \n0       0            0  \n1       0            0  \n2       0            0  \n3       0            0  \n4       0            0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>open</th>\n      <th>high</th>\n      <th>low</th>\n      <th>close</th>\n      <th>tick_volume</th>\n      <th>spread</th>\n      <th>real_volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2023-04-19 02:50:00</td>\n      <td>1.09727</td>\n      <td>1.09740</td>\n      <td>1.09723</td>\n      <td>1.09739</td>\n      <td>67</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2023-04-19 03:00:00</td>\n      <td>1.09741</td>\n      <td>1.09754</td>\n      <td>1.09732</td>\n      <td>1.09732</td>\n      <td>87</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2023-04-19 03:10:00</td>\n      <td>1.09731</td>\n      <td>1.09731</td>\n      <td>1.09710</td>\n      <td>1.09716</td>\n      <td>77</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2023-04-19 03:20:00</td>\n      <td>1.09717</td>\n      <td>1.09738</td>\n      <td>1.09706</td>\n      <td>1.09737</td>\n      <td>59</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2023-04-19 03:30:00</td>\n      <td>1.09738</td>\n      <td>1.09747</td>\n      <td>1.09715</td>\n      <td>1.09720</td>\n      <td>71</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 207, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询前几个数据\n", "df.head(5)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 208, "outputs": [{"data": {"text/plain": "                  time     open     high      low    close  tick_volume   \n19 2023-04-19 06:00:00  1.09612  1.09704  1.09612  1.09634          723  \\\n20 2023-04-19 06:10:00  1.09631  1.09634  1.09548  1.09548          608   \n21 2023-04-19 06:20:00  1.09550  1.09561  1.09466  1.09521          529   \n22 2023-04-19 06:30:00  1.09522  1.09551  1.09506  1.09523          458   \n23 2023-04-19 06:40:00  1.09521  1.09554  1.09497  1.09550          341   \n\n    spread  real_volume  \n19       0            0  \n20       0            0  \n21       0            0  \n22       0            0  \n23       0            0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>open</th>\n      <th>high</th>\n      <th>low</th>\n      <th>close</th>\n      <th>tick_volume</th>\n      <th>spread</th>\n      <th>real_volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>19</th>\n      <td>2023-04-19 06:00:00</td>\n      <td>1.09612</td>\n      <td>1.09704</td>\n      <td>1.09612</td>\n      <td>1.09634</td>\n      <td>723</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>2023-04-19 06:10:00</td>\n      <td>1.09631</td>\n      <td>1.09634</td>\n      <td>1.09548</td>\n      <td>1.09548</td>\n      <td>608</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>2023-04-19 06:20:00</td>\n      <td>1.09550</td>\n      <td>1.09561</td>\n      <td>1.09466</td>\n      <td>1.09521</td>\n      <td>529</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>2023-04-19 06:30:00</td>\n      <td>1.09522</td>\n      <td>1.09551</td>\n      <td>1.09506</td>\n      <td>1.09523</td>\n      <td>458</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>2023-04-19 06:40:00</td>\n      <td>1.09521</td>\n      <td>1.09554</td>\n      <td>1.09497</td>\n      <td>1.09550</td>\n      <td>341</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 208, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询后几行数据\n", "df.tail(5)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 209, "outputs": [{"data": {"text/plain": "       open    close\n19  1.09612  1.09634\n20  1.09631  1.09548\n21  1.09550  1.09521\n22  1.09522  1.09523\n23  1.09521  1.09550", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>open</th>\n      <th>close</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>19</th>\n      <td>1.09612</td>\n      <td>1.09634</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>1.09631</td>\n      <td>1.09548</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>1.09550</td>\n      <td>1.09521</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>1.09522</td>\n      <td>1.09523</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>1.09521</td>\n      <td>1.09550</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 209, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取指定字段数据\n", "df[['open','close']].tail(5)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 210, "outputs": [{"data": {"text/plain": "Timestamp('2023-04-19 06:40:00')"}, "execution_count": 210, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查询指定一行数据\n", "df.loc[df.shape[0]-1]['time']"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 211, "outputs": [{"data": {"text/plain": "                  time     open     high      low    close  tick_volume   \n19 2023-04-19 06:00:00  1.09612  1.09704  1.09612  1.09634          723  \\\n20 2023-04-19 06:10:00  1.09631  1.09634  1.09548  1.09548          608   \n21 2023-04-19 06:20:00  1.09550  1.09561  1.09466  1.09521          529   \n22 2023-04-19 06:30:00  1.09522  1.09551  1.09506  1.09523          458   \n23 2023-04-19 06:40:00  1.09521  1.09554  1.09497  1.09550          341   \n\n    spread  real_volume  \n19       0            0  \n20       0            0  \n21       0            0  \n22       0            0  \n23       0            0  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>time</th>\n      <th>open</th>\n      <th>high</th>\n      <th>low</th>\n      <th>close</th>\n      <th>tick_volume</th>\n      <th>spread</th>\n      <th>real_volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>19</th>\n      <td>2023-04-19 06:00:00</td>\n      <td>1.09612</td>\n      <td>1.09704</td>\n      <td>1.09612</td>\n      <td>1.09634</td>\n      <td>723</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>2023-04-19 06:10:00</td>\n      <td>1.09631</td>\n      <td>1.09634</td>\n      <td>1.09548</td>\n      <td>1.09548</td>\n      <td>608</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>2023-04-19 06:20:00</td>\n      <td>1.09550</td>\n      <td>1.09561</td>\n      <td>1.09466</td>\n      <td>1.09521</td>\n      <td>529</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>22</th>\n      <td>2023-04-19 06:30:00</td>\n      <td>1.09522</td>\n      <td>1.09551</td>\n      <td>1.09506</td>\n      <td>1.09523</td>\n      <td>458</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>2023-04-19 06:40:00</td>\n      <td>1.09521</td>\n      <td>1.09554</td>\n      <td>1.09497</td>\n      <td>1.09550</td>\n      <td>341</td>\n      <td>0</td>\n      <td>0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 211, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df.shape[0]-5:df.shape[0]-1]\n", "# df.loc[95:99]"], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}