//+------------------------------------------------------------------+
//|                                                   无常境v3.mq5   |
//|                                                   优化整合版本   |
//+------------------------------------------------------------------+
#property copyright "Rz"
#property link      ""
#property version   "3.0"

//================================================输入参数==============================================
input double InitialLots = 0.01;       // 初始手数
input int TimeInterval = 300;          // 开单间隔(秒)
input int TakeProfit = 0;              // 止盈点数(0=禁用)
input int StopLoss = 400;              // 止损点数(0=禁用)
input int BreakevenPoints = 400;       // 保本点数(0=禁用)
input int AdditionalPoints = 10;       // 附加盈利点数(0=禁用)
input string StartTime = "00:00";      // 交易开始时间
input string EndTime = "24:00";        // 交易结束时间
input int MagicNumber = 16878;         // 魔术号
input string OrderComment = "";        // 订单备注

//================================================包含文件==============================================
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Arrays\ArrayLong.mqh>
#include <Generic\HashMap.mqh>

//================================================数据结构==============================================
struct SymbolCache {
    double point;
    int digits;
    double minLots;
    double maxLots;
    double lotStep;
    int lotDigits;
};  // 品种参数缓存

struct TimeCache {
    int startMinutes;
    int endMinutes;
};  // 时间参数缓存

//================================================全局变量==============================================
SymbolCache symCache;                 // 品种缓存实例
TimeCache timeCache;                  // 时间缓存实例
CTrade trade;                         // 交易对象
CPositionInfo m_position;             // 持仓信息对象
CArrayLong m_arr_tickets;             // 订单号数组
CHashMap<ulong, bool> ProcessedOrders; // 记录已处理订单
datetime lastOrderTime = 0;           // 最后开仓时间
datetime lastClearTime = 0;           // 上次清理时间
string orderComment;                  // 订单注释
string processedOrdersFile;           // 已处理订单文件名

// 日志级别定义
#define LOG_LEVEL_ERROR   0             // 只记录错误
#define LOG_LEVEL_WARNING 1             // 记录错误和警告
#define LOG_LEVEL_INFO    2             // 记录错误、警告和一般信息
#define LOG_LEVEL_DEBUG   3             // 记录所有信息，包括调试信息

// 当前日志级别设置
int currentLogLevel = LOG_LEVEL_WARNING;  // 默认记录错误和警告

//+------------------------------------------------------------------+
//| 生成安全的文件名                                                 |
//+------------------------------------------------------------------+
string GenerateSafeFilename(string symbol, long magicNumber)
{
    // 1. 处理symbol名称
    string safeSymbol = symbol;

    // 替换所有可能的问题字符为下划线
    string forbiddenChars = "/\\:*?\"<>|";
    for(int i = 0; i < StringLen(forbiddenChars); i++)
    {
        string charStr = StringSubstr(forbiddenChars, i, 1);
        StringReplace(safeSymbol, charStr, "_");
    }

    // 限制symbol名称长度(最多20字符)
    safeSymbol = StringSubstr(safeSymbol, 0, 20);
    if(StringLen(safeSymbol) == 0) safeSymbol = "UnknownSymbol";

    // 2. 处理magic number
    string magicStr = IntegerToString((int)MathAbs(magicNumber));

    // 3. 添加时间戳确保唯一性 (格式:YYYYMMDD)
    MqlDateTime dt;
    TimeCurrent(dt);
    string timeStr = StringFormat("%04d%02d%02d", dt.year, dt.mon, dt.day);

    // 4. 组合最终文件名
    string filename = StringFormat("WCJ_%s_%s_%s.bin",
                                 safeSymbol,
                                 magicStr,
                                 timeStr);

    // 再次检查文件名长度(最多64字符)
    return StringSubstr(filename, 0, 64);
}

//+------------------------------------------------------------------+
//| 统一日志输出函数                                                 |
//+------------------------------------------------------------------+
void LogMessage(int level, string message)
{
    // 只有当消息级别小于或等于当前日志级别时才输出
    if(level <= currentLogLevel)
    {
        string levelText = "";
        color messageColor = clrWhite;

        switch(level)
        {
            case LOG_LEVEL_ERROR:
                levelText = "[错误] ";
                messageColor = clrRed;
                break;
            case LOG_LEVEL_WARNING:
                levelText = "[警告] ";
                messageColor = clrYellow;
                break;
            case LOG_LEVEL_INFO:
                levelText = "[信息] ";
                messageColor = clrWhite;
                break;
            case LOG_LEVEL_DEBUG:
                levelText = "[调试] ";
                messageColor = clrGray;
                break;
        }

        // 添加时间戳
        string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + " ";

        // 输出到日志
        Print(timestamp + levelText + message);
    }
}

//================================================事件处理函数============================================
//+------------------------------------------------------------------+
//| EA初始化函数                                                    |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化伪随机数生成器的种子
    MathSrand(GetTickCount());

    // 初始化品种参数
    symCache.point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    symCache.digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    symCache.minLots = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    symCache.maxLots = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    symCache.lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    symCache.lotDigits = (symCache.lotStep == 0.01) ? 2 :
                        (symCache.lotStep == 0.1) ? 1 : 0;

    // 解析交易时间
    StringToTimeCache(StartTime, EndTime, timeCache);

    // 配置交易对象
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(5); // 合理滑点
    trade.SetTypeFillingBySymbol(_Symbol); // 默认填充类型
    trade.LogLevel(LOG_LEVEL_ERRORS);

    orderComment = OrderComment;

    // 初始化上次开单时间和清理时间
    lastOrderTime = 0;
    lastClearTime = TimeCurrent();

    // 检查并创建Files目录（如果不存在）
    string filesDir = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL5\\Files";
    if(!FileIsExist("", FILE_COMMON))
    {
        if(FolderCreate("MQL5\\Files", FILE_COMMON))
            LogMessage(LOG_LEVEL_INFO, "已创建Files目录");
        else
            LogMessage(LOG_LEVEL_WARNING, StringFormat("创建Files目录失败，错误代码=%d", GetLastError()));
    }

    // 检查文件系统权限
    string terminalPath = TerminalInfoString(TERMINAL_PATH);
    string dataPath = TerminalInfoString(TERMINAL_DATA_PATH);
    string commonPath = TerminalInfoString(TERMINAL_COMMONDATA_PATH);

    LogMessage(LOG_LEVEL_INFO, StringFormat("终端路径: %s", terminalPath));
    LogMessage(LOG_LEVEL_INFO, StringFormat("数据路径: %s", dataPath));
    LogMessage(LOG_LEVEL_INFO, StringFormat("通用路径: %s", commonPath));

    // 测试文件写入权限
    string testFile = "test_permission.tmp";
    int testHandle = FileOpen(testFile, FILE_WRITE);
    if(testHandle != INVALID_HANDLE) {
        FileClose(testHandle);
        FileDelete(testFile);
        LogMessage(LOG_LEVEL_INFO, "文件系统权限测试通过");
    } else {
        LogMessage(LOG_LEVEL_WARNING, StringFormat("文件系统权限测试失败，错误代码=%d", GetLastError()));
    }

    // 从文件加载已处理订单
    LoadProcessedOrders();

    // 设置定时器，每秒触发一次
    EventSetTimer(1);

    LogMessage(LOG_LEVEL_INFO, StringFormat("EA初始化成功 | 品种=%s 点值=%.5f 时段=%02d:%02d-%02d:%02d",
              _Symbol, symCache.point,
              timeCache.startMinutes/60, timeCache.startMinutes%60,
              timeCache.endMinutes/60, timeCache.endMinutes%60));

    return INIT_SUCCEEDED;
}

//================================================工具函数================================================
//+------------------------------------------------------------------+
//| 时间参数转换函数                                                |
//+------------------------------------------------------------------+
void StringToTimeCache(const string start, const string end, TimeCache &cache)
{
    string t[2];
    // 处理开始时间
    if(StringSplit(start, ':', t) == 2) {
        int hours = (int)StringToInteger(t[0]);
        int minutes = (int)StringToInteger(t[1]);

        // 确保时间在有效范围内
        hours = MathMin(MathMax(hours, 0), 23);
        minutes = MathMin(MathMax(minutes, 0), 59);

        cache.startMinutes = hours * 60 + minutes;
    }

    // 处理结束时间
    if(StringSplit(end, ':', t) == 2) {
        int hours = (int)StringToInteger(t[0]);
        int minutes = (int)StringToInteger(t[1]);

        // 特殊处理"24:00"，将其转换为"23:59"
        if(hours == 24) {
            hours = 23;
            minutes = 59;
        } else {
            // 确保时间在有效范围内
            hours = MathMin(MathMax(hours, 0), 23);
            minutes = MathMin(MathMax(minutes, 0), 59);
        }

        cache.endMinutes = hours * 60 + minutes;
    }

    LogMessage(LOG_LEVEL_INFO, StringFormat("交易时间设置: 开始时间=%s (分钟=%d), 结束时间=%s (分钟=%d)",
              start, cache.startMinutes, end, cache.endMinutes));
}

//+------------------------------------------------------------------+
//| 交易时间验证函数                                                |
//+------------------------------------------------------------------+
bool IsTradeTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // 计算当前分钟数
    int current = dt.hour * 60 + dt.min;

    // 处理跨午夜的时间段
    if(timeCache.startMinutes < timeCache.endMinutes) {
        // 正常时间段（例如 09:00-17:00）
        return (current >= timeCache.startMinutes && current <= timeCache.endMinutes);
    } else {
        // 跨午夜时间段（例如 22:00-06:00）
        return (current >= timeCache.startMinutes || current <= timeCache.endMinutes);
    }
}

//+------------------------------------------------------------------+
//| 格式化手数函数                                                   |
//+------------------------------------------------------------------+
double FormatLots(string symbol, double lots)
{
    double minLots = symCache.minLots;
    double maxLots = symCache.maxLots;
    double stepLots = symCache.lotStep;

    // 确保手数在最小和最大范围内
    lots = MathMax(minLots, MathMin(maxLots, lots));

    // 确保手数是步进的整数倍
    int steps = (int)MathRound(lots / stepLots);
    lots = steps * stepLots;

    // 格式化到正确的小数位
    return NormalizeDouble(lots, symCache.lotDigits);
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CalculateSL(double price, ENUM_ORDER_TYPE orderType)
{
    if(StopLoss <= 0) return 0; // 如果止损点数为0，则禁用止损

    double sl = 0;
    if(orderType == ORDER_TYPE_BUY) {
        sl = price - StopLoss * symCache.point;
    } else {
        sl = price + StopLoss * symCache.point;
    }

    // 确保止损符合经纪商要求
    int stops_level = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    double min_distance = stops_level * symCache.point;

    MqlTick last_tick;
    if(SymbolInfoTick(_Symbol, last_tick)) {
        double current_price = (orderType == ORDER_TYPE_BUY) ? last_tick.bid : last_tick.ask;
        if(MathAbs(current_price - sl) < min_distance) {
            sl = (orderType == ORDER_TYPE_BUY) ?
                current_price - min_distance :
                current_price + min_distance;
        }
    }

    return NormalizeDouble(sl, symCache.digits);
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                     |
//+------------------------------------------------------------------+
double CalculateTP(double price, ENUM_ORDER_TYPE orderType)
{
    if(TakeProfit <= 0) return 0; // 如果止盈点数为0，则禁用止盈

    double tp = 0;
    if(orderType == ORDER_TYPE_BUY) {
        tp = price + TakeProfit * symCache.point;
    } else {
        tp = price - TakeProfit * symCache.point;
    }

    return NormalizeDouble(tp, symCache.digits);
}

//+------------------------------------------------------------------+
//| 保存已处理订单到文件                                             |
//+------------------------------------------------------------------+
void SaveProcessedOrders()
{
    if(processedOrdersFile == "")
        processedOrdersFile = "WCJ_orders.bin";

    string filePath = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL5\\Files\\" + processedOrdersFile;

    // 获取所有票号
    ulong tickets[];
    int count = ProcessedOrders.Count();
    if(count == 0) return;

    ulong keys[];
    bool values[];
    ProcessedOrders.CopyTo(keys, values);
    ArrayCopy(tickets, keys);

    // 记录详细信息以便调试
    LogMessage(LOG_LEVEL_INFO, StringFormat("保存文件: %s", filePath));

    // 保存文件
    int handle = FileOpen(filePath, FILE_WRITE|FILE_BIN|FILE_COMMON);
    if(handle != INVALID_HANDLE)
    {
        FileWriteArray(handle, tickets);
        FileClose(handle);
        LogMessage(LOG_LEVEL_INFO, StringFormat("已保存%d个已处理订单到文件", count));
    }
    else
    {
        int errorCode = GetLastError();
        LogMessage(LOG_LEVEL_ERROR, StringFormat("保存文件失败，错误代码=%d", errorCode));
    }
}

//+------------------------------------------------------------------+
//| 从文件加载已处理订单                                             |
//+------------------------------------------------------------------+
void LoadProcessedOrders()
{
    // 使用统一文件名
    processedOrdersFile = "WCJ_orders.bin";

    // 统一使用MQL5标准路径
    string filePath = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL5\\Files\\" + processedOrdersFile;

    // 清空当前哈希表
    ProcessedOrders.Clear();

    // 记录详细信息以便调试
    LogMessage(LOG_LEVEL_INFO, StringFormat("加载文件: %s", filePath));

    // 尝试打开文件
    int handle = FileOpen(filePath, FILE_READ|FILE_BIN|FILE_COMMON);
    if(handle != INVALID_HANDLE)
    {
        ulong tickets[];
        int count = (int)FileReadArray(handle, tickets);
        FileClose(handle);

        // 添加到哈希表
        for(int i = 0; i < count; i++)
        {
            // 验证订单是否仍然存在
            if(PositionSelectByTicket(tickets[i])) {
                ProcessedOrders.Add(tickets[i], true);
            }
        }
        LogMessage(LOG_LEVEL_INFO, StringFormat("已从文件加载%d个已处理订单", ProcessedOrders.Count()));
    }
    else
    {
        int errorCode = GetLastError();
        // 文件不存在是正常情况，不记录为错误
        if(errorCode == ERR_FILE_NOT_EXIST)
            LogMessage(LOG_LEVEL_INFO, "未找到已处理订单文件，将创建新文件");
        else
            LogMessage(LOG_LEVEL_WARNING, StringFormat("加载文件失败，错误代码=%d", errorCode));
    }
}

//================================================核心逻辑================================================
//+------------------------------------------------------------------+
//| 价格跳动处理主函数                                              |
//+------------------------------------------------------------------+
void OnTick()
{
    // 获取当前时间
    datetime now = TimeCurrent();

    // 检查是否在交易时段内
    bool isInTradingHours = IsTradeTime();

    // 在交易时段内才执行开单逻辑
    if(isInTradingHours)
    {
        // 检查开单时间间隔
        if(now - lastOrderTime >= TimeInterval)
        {
            if(ExecuteOptimizedTrade())
                ManagePositions();
            else
                ManagePositions(); // 即使开单失败也管理持仓
        }
        else
        {
            // 管理现有持仓，即使不开新单
            ManagePositions();
        }
    }
    else
    {
        // 非交易时段仍然管理现有持仓
        ManagePositions();
    }
}

//+------------------------------------------------------------------+
//| 优化版交易执行函数                                               |
//+------------------------------------------------------------------+
bool ExecuteOptimizedTrade()
{
    // 1. 获取市场数据
    MqlTick tick;
    if(!SymbolInfoTick(_Symbol, tick)) {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("获取市场报价失败, 错误代码=%d", GetLastError()));
        return false;
    }

    // 2. 生成交易方向
    static ulong randSeed = GetTickCount();
    randSeed ^= randSeed << 13;
    randSeed ^= randSeed >> 17;
    randSeed ^= randSeed << 5;
    ENUM_ORDER_TYPE type = (randSeed%2 == 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

    // 3. 计算交易参数
    double lots = FormatLots(_Symbol, InitialLots);
    double price = (type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
    double sl = CalculateSL(price, type);
    double tp = CalculateTP(price, type);

    // 4. 发送交易指令
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(20);
    trade.SetTypeFillingBySymbol(_Symbol);
    trade.SetAsyncMode(false); // 使用同步模式，确保能获取到订单号

    bool result = false;
    string orderTypeStr = (type == ORDER_TYPE_BUY) ? "多单" : "空单";

    if(type == ORDER_TYPE_BUY) {
        result = trade.Buy(lots, _Symbol, price, sl, tp, orderComment);
    } else {
        result = trade.Sell(lots, _Symbol, price, sl, tp, orderComment);
    }

    uint resultCode = trade.ResultRetcode();
    if(result && resultCode == TRADE_RETCODE_DONE) {
        lastOrderTime = TimeCurrent();
        LogMessage(LOG_LEVEL_INFO, StringFormat("开仓成功: 类型=%s, 票号=%d, 手数=%.2f, 价格=%.5f, 止损=%.5f, 止盈=%.5f",
                                              orderTypeStr, trade.ResultOrder(), lots, price, sl, tp));
        return true;
    } else {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("开仓失败: 类型=%s, 错误代码=%d, 描述=%s",
                                               orderTypeStr, resultCode, trade.ResultRetcodeDescription()));
        return false;
    }
}

//+------------------------------------------------------------------+
//| 持仓管理函数                                                     |
//+------------------------------------------------------------------+
void ManagePositions()
{
    int total = PositionsTotal();
    if(total == 0) {
        // 没有持仓时清空处理记录
        ProcessedOrders.Clear();
        return;
    }

    // 获取最新市场报价
    MqlTick currentTick;
    if(!SymbolInfoTick(_Symbol, currentTick)) {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("获取报价失败！错误代码：%d", GetLastError()));
        return;
    }

    // 遍历所有持仓
    for(int i = 0; i < total; i++) {
        ulong ticket = PositionGetTicket(i);
        if(ticket == 0) continue;

        // 检查是否为当前品种和魔术号
        string symbol = PositionGetString(POSITION_SYMBOL);
        long magic = PositionGetInteger(POSITION_MAGIC);
        if(symbol != _Symbol || magic != MagicNumber) continue;

        // 跳过已处理的订单
        if(ProcessedOrders.ContainsKey(ticket)) continue;

        // 获取持仓信息
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentPrice = (posType == POSITION_TYPE_BUY) ? currentTick.bid : currentTick.ask;
        double profit = MathAbs(currentPrice - openPrice);
        double currentSL = PositionGetDouble(POSITION_SL);
        double currentTP = PositionGetDouble(POSITION_TP);

        // 检查是否达到保本条件
        if(BreakevenPoints > 0 && profit >= BreakevenPoints * symCache.point) {
            // 计算新的止损价格
            double newSL = (posType == POSITION_TYPE_BUY) ?
                          openPrice + AdditionalPoints * symCache.point :
                          openPrice - AdditionalPoints * symCache.point;

            // 确保新止损不会使订单立即平仓
            int stops_level = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
            double min_distance = stops_level * symCache.point;

            if((posType == POSITION_TYPE_BUY && newSL >= currentPrice - min_distance) ||
               (posType == POSITION_TYPE_SELL && newSL <= currentPrice + min_distance)) {
                newSL = (posType == POSITION_TYPE_BUY) ?
                      currentPrice - min_distance :
                      currentPrice + min_distance;
            }

            // 避免无效更新
            bool needUpdate = (posType == POSITION_TYPE_BUY) ?
                            (newSL > currentSL + symCache.point/2 || currentSL == 0) :
                            (newSL < currentSL - symCache.point/2 || currentSL == 0);

            // 执行订单修改
            if(needUpdate) {
                trade.SetExpertMagicNumber(MagicNumber);
                if(trade.PositionModify(ticket, NormalizeDouble(newSL, symCache.digits), NormalizeDouble(currentTP, symCache.digits))) {
                    // 添加到已处理订单哈希表
                    ProcessedOrders.Add(ticket, true);

                    LogMessage(LOG_LEVEL_INFO, StringFormat("保本止损设置成功: 票号=%d, 类型=%s, 开仓价=%.5f, 新止损=%.5f",
                                                          ticket,
                                                          (posType == POSITION_TYPE_BUY ? "多单" : "空单"),
                                                          openPrice,
                                                          newSL));
                } else {
                    LogMessage(LOG_LEVEL_ERROR, StringFormat("保本止损设置失败: 票号=%d, 错误=%d: %s",
                                                           ticket,
                                                           trade.ResultRetcode(),
                                                           trade.ResultRetcodeDescription()));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 定时器函数                                                       |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 每小时清理一次已处理订单
    datetime now = TimeCurrent();
    if(now - lastClearTime >= 3600) // 3600秒 = 1小时
    {
        // 创建临时数组存储需要删除的票号
        ulong expiredTickets[];
        int expiredCount = 0;

        // 获取所有已处理订单的票号
        ulong keys[];
        bool values[];
        ProcessedOrders.CopyTo(keys, values);

        // 检查每个订单是否存在
        for(int i = 0; i < ArraySize(keys); i++)
        {
            ulong ticket = keys[i];
            // 检查订单是否存在
            if(!PositionSelectByTicket(ticket))
            {
                ArrayResize(expiredTickets, expiredCount + 1);
                expiredTickets[expiredCount++] = ticket;
            }
        }

        // 从哈希表中删除过期订单
        for(int i = 0; i < expiredCount; i++)
        {
            ProcessedOrders.Remove(expiredTickets[i]);
        }

        // 保存到文件
        SaveProcessedOrders();

        // 更新清理时间
        lastClearTime = now;

        LogMessage(LOG_LEVEL_INFO, StringFormat("已清理%d个过期订单，当前处理订单数量：%d",
                                              expiredCount, ProcessedOrders.Count()));
    }
}

//+------------------------------------------------------------------+
//| 逆初始化函数                                                     |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();

    // 保存已处理订单到文件
    SaveProcessedOrders();

    LogMessage(LOG_LEVEL_INFO, StringFormat("EA停止 | 原因代码: %d", reason));
}
