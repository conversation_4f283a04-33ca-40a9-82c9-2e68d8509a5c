# 导入MT5的模块
import MetaTrader5 as mt5
from pprint import pprint
import datetime

if __name__ in "__main__":
    path = "D:\\MetaTrader 5\\terminal64.exe"
    # 第一种连接方式
    is_ok = mt5.initialize(path=path,
                           server="Exness-MT5Trial5",
                           login=76314481,
                           password="6ixuhbWp",
                           timeout=2000)

    if not is_ok:
        print("连接MT5失败, 错误原因: ", mt5.last_error())
        mt5.shutdown()
        quit()

    # 获取指定交易品种的详情信息
    symbol_info = mt5.symbol_info("EURUSDz")._asdict()
    # pprint(symbol_info)
    # print("Name: ", symbol_info['name'])
    # print("Ask: ", symbol_info['ask'])
    # print("Bid: ", symbol_info['bid'])
    # print("Digits", symbol_info['digits'])

    # 添加指定交易品种到可见列表
    mt5.symbol_select("XAUUSDz", True)

    # 获取指定交易品种最后报价信息
    symbol_tick = mt5.symbol_info_tick("EURUSDz")._asdict()
    # pprint(symbol_tick)
    time = datetime.datetime.fromtimestamp(symbol_tick["time"])
    # print(time)
    time_str = time.strftime("%Y年%m月%d日 %H:%M:%S")
    # print(time_str)

    # 模糊查询指定范围内的交易品种
    symbols: tuple = mt5.symbols_get("*")
    # pprint(type(symbols))

    # 查询包含指定字符串名称的交易品种
    # * 表示匹配 0 到 N个字符串的匹配
    symbols = mt5.symbols_get("*USD*")

    # 查询指定开头的字符串
    symbols = mt5.symbols_get("USD*")
    # 查询结尾包含指定字符串
    symbols = mt5.symbols_get("*USDz")

    # 查询不包含指定字符串
    symbols = mt5.symbols_get("*,!*USD*,!*EUR*,!GBP*")

    for symbol in symbols:
        # pprint(symbol._asdict())
        symbol_info = symbol._asdict()
        # print("交易品种名称: ", symbol_info['name'])

    # 获取所有交易品种的数量
    print(mt5.symbols_total())

    # 查询交易品种深度行情
    if mt5.market_book_add("EURUSDz"):
        print("进入")
        for i in range(10):
            item = mt5.market_book_get("EURUSDz")
            pprint(item)

        # 取消监听
        mt5.market_book_release("EURUSDz")

    mt5.shutdown()
