//+------------------------------------------------------------------+
//|                                          多时间框架协同分析测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "多时间框架协同分析系统功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input double ConsensusThreshold = 70.0; // 共识阈值

//+------------------------------------------------------------------+
//| 多时间框架协同分析系统测试脚本                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始多时间框架协同分析系统测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("共识阈值: ", DoubleToString(ConsensusThreshold, 1), "%");
    Print("========================================");
    
    // 测试1: 时间框架权重配置
    TestTimeframeWeightConfiguration();
    
    // 测试2: 单时间框架信号分析
    TestSingleTimeframeSignalAnalysis();
    
    // 测试3: 多时间框架共识计算
    TestMultiTimeframeConsensusCalculation();
    
    // 测试4: 主导时间框架确定
    TestDominantTimeframeDetermination();
    
    // 测试5: 压缩末端确认
    TestCompressionEndConfirmation();
    
    // 测试6: 完整协同分析流程
    TestCompleteCooperativeAnalysisWorkflow();
    
    Print("========================================");
    Print("✅ 多时间框架协同分析系统测试完成");
}

//+------------------------------------------------------------------+
//| 测试时间框架权重配置                                              |
//+------------------------------------------------------------------+
void TestTimeframeWeightConfiguration()
{
    Print("📋 测试1: 时间框架权重配置");
    
    // 测试时间框架权重配置
    struct TimeframeWeightTest {
        int timeframeIndex;
        double expectedWeight;
        string description;
    };
    
    TimeframeWeightTest tests[] = {
        {0, 0.15, "M15时间框架"},
        {1, 0.25, "H1时间框架"},
        {2, 0.35, "H4时间框架"},
        {3, 0.25, "D1时间框架"}
    };
    
    double totalWeight = 0.0;
    
    for(int i = 0; i < ArraySize(tests); i++) {
        TimeframeWeightTest twt = tests[i];
        
        Print("时间框架: ", twt.description);
        Print("  索引: ", twt.timeframeIndex);
        Print("  权重: ", DoubleToString(twt.expectedWeight*100, 1), "%");
        
        totalWeight += twt.expectedWeight;
        
        bool weightValid = (twt.expectedWeight > 0.0 && twt.expectedWeight <= 1.0);
        Print("  权重有效性: ", weightValid ? "✓" : "✗");
    }
    
    Print("总权重: ", DoubleToString(totalWeight*100, 1), "%");
    bool totalWeightCorrect = (MathAbs(totalWeight - 1.0) < 0.001);
    Print("总权重验证: ", totalWeightCorrect ? "✓" : "✗");
    
    Print("✅ 时间框架权重配置测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试单时间框架信号分析                                            |
//+------------------------------------------------------------------+
void TestSingleTimeframeSignalAnalysis()
{
    Print("📋 测试2: 单时间框架信号分析");
    
    // 模拟时间框架信号数据
    struct TestTimeframeSignal {
        string timeframeName;
        double compressionLevel;
        double breakoutProbability;
        double trendStrength;
        double volatilityLevel;
        bool expectedCompressionEnd;
        int expectedSignalStrength;
    };
    
    TestTimeframeSignal tests[] = {
        {"M15", 85.0, 80.0, 70.0, 45.0, true, 4},   // 极强信号
        {"H1", 75.0, 70.0, 60.0, 50.0, true, 3},    // 强信号
        {"H4", 60.0, 55.0, 50.0, 40.0, false, 2},   // 中等信号
        {"D1", 40.0, 35.0, 30.0, 35.0, false, 1}    // 弱信号
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        TestTimeframeSignal tts = tests[i];
        
        // 计算信号强度
        int calculatedSignalStrength = CalculateTestSignalStrength(tts.compressionLevel, 
                                                                  tts.breakoutProbability, 
                                                                  tts.trendStrength);
        
        // 判断压缩末端
        bool calculatedCompressionEnd = IsTestCompressionEnd(tts.compressionLevel, tts.breakoutProbability);
        
        // 计算置信度
        double confidenceScore = CalculateTestConfidenceScore(tts.compressionLevel, 
                                                             tts.breakoutProbability, 
                                                             tts.trendStrength, 
                                                             tts.volatilityLevel);
        
        Print("时间框架: ", tts.timeframeName);
        Print("  压缩水平: ", DoubleToString(tts.compressionLevel, 1), "%");
        Print("  爆发概率: ", DoubleToString(tts.breakoutProbability, 1), "%");
        Print("  趋势强度: ", DoubleToString(tts.trendStrength, 1), "%");
        Print("  波动率: ", DoubleToString(tts.volatilityLevel, 1), "%");
        Print("  计算信号强度: ", calculatedSignalStrength, " (", GetTestSignalStrengthDescription(calculatedSignalStrength), ")");
        Print("  预期信号强度: ", tts.expectedSignalStrength, " (", GetTestSignalStrengthDescription(tts.expectedSignalStrength), ")");
        Print("  计算压缩末端: ", calculatedCompressionEnd ? "是" : "否");
        Print("  预期压缩末端: ", tts.expectedCompressionEnd ? "是" : "否");
        Print("  置信度评分: ", DoubleToString(confidenceScore, 1), "%");
        
        bool signalStrengthCorrect = (calculatedSignalStrength == tts.expectedSignalStrength);
        bool compressionEndCorrect = (calculatedCompressionEnd == tts.expectedCompressionEnd);
        
        Print("  信号强度验证: ", signalStrengthCorrect ? "✓" : "✗");
        Print("  压缩末端验证: ", compressionEndCorrect ? "✓" : "✗");
    }
    
    Print("✅ 单时间框架信号分析测试完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试信号强度                                                  |
//+------------------------------------------------------------------+
int CalculateTestSignalStrength(double compressionLevel, double breakoutProbability, double trendStrength) {
    double combinedScore = (compressionLevel * 0.4 + 
                           breakoutProbability * 0.4 + 
                           trendStrength * 0.2);
    
    if(combinedScore >= 80.0) return 4; // 极强
    if(combinedScore >= 65.0) return 3; // 强
    if(combinedScore >= 50.0) return 2; // 中等
    if(combinedScore >= 35.0) return 1; // 弱
    return 0; // 极弱
}

//+------------------------------------------------------------------+
//| 判断测试压缩末端                                                  |
//+------------------------------------------------------------------+
bool IsTestCompressionEnd(double compressionLevel, double breakoutProbability) {
    return (compressionLevel >= 70.0 && breakoutProbability >= 70.0);
}

//+------------------------------------------------------------------+
//| 计算测试置信度评分                                                |
//+------------------------------------------------------------------+
double CalculateTestConfidenceScore(double compressionLevel, double breakoutProbability, 
                                   double trendStrength, double volatilityLevel) {
    // 基础置信度
    double baseConfidence = (compressionLevel + breakoutProbability) / 2.0;
    
    // 趋势强度调整
    double trendAdjustment = (trendStrength - 50.0) * 0.2;
    
    // 波动率调整 (适中波动率最好)
    double volatilityAdjustment = 0.0;
    if(volatilityLevel >= 30.0 && volatilityLevel <= 70.0) {
        volatilityAdjustment = 10.0;
    } else {
        volatilityAdjustment = -5.0;
    }
    
    double confidenceScore = baseConfidence + trendAdjustment + volatilityAdjustment;
    
    return MathMax(0.0, MathMin(100.0, confidenceScore));
}

//+------------------------------------------------------------------+
//| 获取测试信号强度描述                                              |
//+------------------------------------------------------------------+
string GetTestSignalStrengthDescription(int strength) {
    switch(strength) {
        case 4: return "极强";
        case 3: return "强";
        case 2: return "中等";
        case 1: return "弱";
        case 0: return "极弱";
        default: return "未知";
    }
}

//+------------------------------------------------------------------+
//| 测试多时间框架共识计算                                            |
//+------------------------------------------------------------------+
void TestMultiTimeframeConsensusCalculation()
{
    Print("📋 测试3: 多时间框架共识计算");
    
    // 测试不同共识场景
    struct ConsensusTest {
        double timeframeScores[4];
        double timeframeWeights[4];
        int expectedAgreeingFrames;
        int expectedConsensusLevel;
        string description;
    };
    
    ConsensusTest tests[] = {
        {{85.0, 80.0, 90.0, 85.0}, {0.15, 0.25, 0.35, 0.25}, 4, 4, "一致共识场景"},
        {{75.0, 70.0, 80.0, 65.0}, {0.15, 0.25, 0.35, 0.25}, 3, 3, "强共识场景"},
        {{65.0, 60.0, 70.0, 55.0}, {0.15, 0.25, 0.35, 0.25}, 2, 2, "中等共识场景"},
        {{55.0, 50.0, 60.0, 45.0}, {0.15, 0.25, 0.35, 0.25}, 1, 1, "弱共识场景"},
        {{45.0, 40.0, 50.0, 35.0}, {0.15, 0.25, 0.35, 0.25}, 0, 0, "无共识场景"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        ConsensusTest ct = tests[i];
        
        // 计算加权评分
        double weightedScore = 0.0;
        double totalWeight = 0.0;
        int agreeingFrames = 0;
        
        for(int j = 0; j < 4; j++) {
            weightedScore += ct.timeframeScores[j] * ct.timeframeWeights[j];
            totalWeight += ct.timeframeWeights[j];
            
            // 判断是否同意 (简化阈值为60%)
            if(ct.timeframeScores[j] >= 60.0) {
                agreeingFrames++;
            }
        }
        
        double finalScore = (totalWeight > 0) ? (weightedScore / totalWeight) : 50.0;
        
        // 确定共识水平
        int consensusLevel = DetermineTestConsensusLevel(agreeingFrames, finalScore);
        
        // 计算共识强度
        double consensusStrength = CalculateTestConsensusStrength(agreeingFrames, finalScore);
        
        Print("场景: ", ct.description);
        Print("  时间框架评分: M15=", DoubleToString(ct.timeframeScores[0], 1), 
              " H1=", DoubleToString(ct.timeframeScores[1], 1),
              " H4=", DoubleToString(ct.timeframeScores[2], 1),
              " D1=", DoubleToString(ct.timeframeScores[3], 1));
        Print("  加权评分: ", DoubleToString(finalScore, 1));
        Print("  同意框架数: ", agreeingFrames, "/4");
        Print("  计算共识水平: ", consensusLevel, " (", GetTestConsensusLevelDescription(consensusLevel), ")");
        Print("  预期共识水平: ", ct.expectedConsensusLevel, " (", GetTestConsensusLevelDescription(ct.expectedConsensusLevel), ")");
        Print("  共识强度: ", DoubleToString(consensusStrength, 1), "%");
        
        bool consensusCorrect = (consensusLevel == ct.expectedConsensusLevel);
        bool agreeingCorrect = (agreeingFrames == ct.expectedAgreeingFrames);
        
        Print("  共识水平验证: ", consensusCorrect ? "✓" : "✗");
        Print("  同意框架验证: ", agreeingCorrect ? "✓" : "✗");
    }
    
    Print("✅ 多时间框架共识计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 确定测试共识水平                                                  |
//+------------------------------------------------------------------+
int DetermineTestConsensusLevel(int agreeingTimeframes, double finalScore) {
    if(agreeingTimeframes == 4 && finalScore >= 85.0) {
        return 4; // 一致共识
    } else if(agreeingTimeframes >= 3 && finalScore >= 75.0) {
        return 3; // 强共识
    } else if(agreeingTimeframes >= 2 && finalScore >= 65.0) {
        return 2; // 中等共识
    } else if(agreeingTimeframes >= 1 && finalScore >= 55.0) {
        return 1; // 弱共识
    } else {
        return 0; // 无共识
    }
}

//+------------------------------------------------------------------+
//| 计算测试共识强度                                                  |
//+------------------------------------------------------------------+
double CalculateTestConsensusStrength(int agreeingTimeframes, double finalScore) {
    // 基于同意时间框架数的强度
    double agreementStrength = (double)agreeingTimeframes / 4.0 * 50.0;
    
    // 基于评分的强度
    double scoreStrength = (finalScore / 100.0) * 50.0;
    
    return agreementStrength + scoreStrength;
}

//+------------------------------------------------------------------+
//| 获取测试共识水平描述                                              |
//+------------------------------------------------------------------+
string GetTestConsensusLevelDescription(int level) {
    switch(level) {
        case 4: return "一致共识";
        case 3: return "强共识";
        case 2: return "中等共识";
        case 1: return "弱共识";
        case 0: return "无共识";
        default: return "未知共识";
    }
}

//+------------------------------------------------------------------+
//| 测试主导时间框架确定                                              |
//+------------------------------------------------------------------+
void TestDominantTimeframeDetermination()
{
    Print("📋 测试4: 主导时间框架确定");
    
    // 测试不同主导时间框架场景
    struct DominantTest {
        double timeframeScores[4];
        double timeframeWeights[4];
        int expectedDominantIndex;
        string description;
    };
    
    DominantTest tests[] = {
        {{90.0, 70.0, 80.0, 75.0}, {0.15, 0.25, 0.35, 0.25}, 2, "H4主导场景"},
        {{85.0, 90.0, 70.0, 75.0}, {0.15, 0.25, 0.35, 0.25}, 1, "H1主导场景"},
        {{75.0, 70.0, 80.0, 95.0}, {0.15, 0.25, 0.35, 0.25}, 3, "D1主导场景"},
        {{95.0, 70.0, 75.0, 80.0}, {0.15, 0.25, 0.35, 0.25}, 0, "M15主导场景"}
    };
    
    string timeframeNames[] = {"M15", "H1", "H4", "D1"};
    
    for(int i = 0; i < ArraySize(tests); i++) {
        DominantTest dt = tests[i];
        
        // 确定主导时间框架
        double maxScore = 0.0;
        int dominantIndex = 2; // 默认H4
        
        for(int j = 0; j < 4; j++) {
            double weightedScore = dt.timeframeScores[j] * dt.timeframeWeights[j];
            
            if(weightedScore > maxScore) {
                maxScore = weightedScore;
                dominantIndex = j;
            }
        }
        
        Print("场景: ", dt.description);
        Print("  时间框架评分: M15=", DoubleToString(dt.timeframeScores[0], 1), 
              " H1=", DoubleToString(dt.timeframeScores[1], 1),
              " H4=", DoubleToString(dt.timeframeScores[2], 1),
              " D1=", DoubleToString(dt.timeframeScores[3], 1));
        Print("  计算主导时间框架: ", timeframeNames[dominantIndex], " (索引", dominantIndex, ")");
        Print("  预期主导时间框架: ", timeframeNames[dt.expectedDominantIndex], " (索引", dt.expectedDominantIndex, ")");
        Print("  最高加权评分: ", DoubleToString(maxScore, 2));
        
        bool dominantCorrect = (dominantIndex == dt.expectedDominantIndex);
        Print("  主导时间框架验证: ", dominantCorrect ? "✓" : "✗");
    }
    
    Print("✅ 主导时间框架确定测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试压缩末端确认                                                  |
//+------------------------------------------------------------------+
void TestCompressionEndConfirmation()
{
    Print("📋 测试5: 压缩末端确认");
    
    // 测试不同压缩末端确认场景
    struct CompressionEndTest {
        bool timeframeCompressionEnd[4];
        bool expectedConfirmation;
        string description;
    };
    
    CompressionEndTest tests[] = {
        {{true, true, true, true}, true, "全部确认场景"},
        {{true, true, true, false}, true, "三个确认场景"},
        {{true, true, false, false}, true, "两个确认场景"},
        {{true, false, false, false}, false, "一个确认场景"},
        {{false, false, false, false}, false, "无确认场景"}
    };
    
    string timeframeNames[] = {"M15", "H1", "H4", "D1"};
    
    for(int i = 0; i < ArraySize(tests); i++) {
        CompressionEndTest cet = tests[i];
        
        // 统计确认的时间框架数
        int confirmationCount = 0;
        for(int j = 0; j < 4; j++) {
            if(cet.timeframeCompressionEnd[j]) {
                confirmationCount++;
            }
        }
        
        // 判断是否确认 (至少需要2个时间框架确认)
        bool calculatedConfirmation = (confirmationCount >= 2);
        
        Print("场景: ", cet.description);
        Print("  时间框架确认状态: M15=", cet.timeframeCompressionEnd[0] ? "是" : "否",
              " H1=", cet.timeframeCompressionEnd[1] ? "是" : "否",
              " H4=", cet.timeframeCompressionEnd[2] ? "是" : "否",
              " D1=", cet.timeframeCompressionEnd[3] ? "是" : "否");
        Print("  确认时间框架数: ", confirmationCount, "/4");
        Print("  计算确认结果: ", calculatedConfirmation ? "确认" : "未确认");
        Print("  预期确认结果: ", cet.expectedConfirmation ? "确认" : "未确认");
        
        bool confirmationCorrect = (calculatedConfirmation == cet.expectedConfirmation);
        Print("  确认结果验证: ", confirmationCorrect ? "✓" : "✗");
    }
    
    Print("✅ 压缩末端确认测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整协同分析流程                                              |
//+------------------------------------------------------------------+
void TestCompleteCooperativeAnalysisWorkflow()
{
    Print("📋 测试6: 完整协同分析流程");
    
    // 模拟完整的协同分析流程
    Print("模拟协同分析流程:");
    
    // 步骤1: 初始化多时间框架系统
    Print("步骤1: 初始化多时间框架系统");
    Print("  时间框架权重: M15(15%) H1(25%) H4(35%) D1(25%)");
    Print("  共识阈值: ", DoubleToString(ConsensusThreshold, 1), "%");
    Print("  最小同意框架数: 2");
    
    // 步骤2: 分析各时间框架信号
    Print("步骤2: 分析各时间框架信号");
    double timeframeScores[] = {75.0, 80.0, 85.0, 70.0};
    bool compressionEnds[] = {true, true, true, false};
    
    for(int i = 0; i < 4; i++) {
        string tfName = (i == 0) ? "M15" : (i == 1) ? "H1" : (i == 2) ? "H4" : "D1";
        Print("  ", tfName, "时间框架: 评分", DoubleToString(timeframeScores[i], 1), 
              "% 压缩末端", compressionEnds[i] ? "是" : "否");
    }
    
    // 步骤3: 计算多时间框架共识
    Print("步骤3: 计算多时间框架共识");
    double weights[] = {0.15, 0.25, 0.35, 0.25};
    double weightedScore = 0.0;
    int agreeingFrames = 0;
    
    for(int i = 0; i < 4; i++) {
        weightedScore += timeframeScores[i] * weights[i];
        if(timeframeScores[i] >= 70.0) agreeingFrames++;
    }
    
    int consensusLevel = DetermineTestConsensusLevel(agreeingFrames, weightedScore);
    double consensusStrength = CalculateTestConsensusStrength(agreeingFrames, weightedScore);
    
    Print("  加权评分: ", DoubleToString(weightedScore, 1));
    Print("  同意框架数: ", agreeingFrames, "/4");
    Print("  共识水平: ", GetTestConsensusLevelDescription(consensusLevel));
    Print("  共识强度: ", DoubleToString(consensusStrength, 1), "%");
    
    // 步骤4: 确定主导时间框架
    Print("步骤4: 确定主导时间框架");
    double maxScore = 0.0;
    int dominantIndex = 2;
    
    for(int i = 0; i < 4; i++) {
        double score = timeframeScores[i] * weights[i];
        if(score > maxScore) {
            maxScore = score;
            dominantIndex = i;
        }
    }
    
    string dominantTF = (dominantIndex == 0) ? "M15" : (dominantIndex == 1) ? "H1" : (dominantIndex == 2) ? "H4" : "D1";
    Print("  主导时间框架: ", dominantTF);
    Print("  主导评分: ", DoubleToString(maxScore, 2));
    
    // 步骤5: 压缩末端确认
    Print("步骤5: 压缩末端确认");
    int compressionEndCount = 0;
    for(int i = 0; i < 4; i++) {
        if(compressionEnds[i]) compressionEndCount++;
    }
    
    bool compressionEndConfirmed = (compressionEndCount >= 2);
    Print("  确认时间框架数: ", compressionEndCount, "/4");
    Print("  压缩末端确认: ", compressionEndConfirmed ? "确认" : "未确认");
    
    // 步骤6: 生成交易建议
    Print("步骤6: 生成交易建议");
    string recommendation = "";
    if(consensusLevel >= 3) {
        recommendation = "强烈建议交易";
    } else if(consensusLevel >= 2) {
        recommendation = "谨慎交易";
    } else if(consensusLevel >= 1) {
        recommendation = "建议观望";
    } else {
        recommendation = "不建议交易";
    }
    
    Print("  交易建议: ", recommendation);
    Print("  建议依据: 共识水平", GetTestConsensusLevelDescription(consensusLevel), 
          " 强度", DoubleToString(consensusStrength, 1), "%");
    
    // 步骤7: 流程验证
    Print("步骤7: 流程验证");
    bool workflowValid = (consensusLevel >= 2 && compressionEndConfirmed && weightedScore >= 70.0);
    Print("  流程有效性: ", workflowValid ? "✓" : "✗");
    Print("  验证条件: 共识≥中等 压缩末端确认 加权评分≥70%");
    
    Print("✅ 完整协同分析流程完成\n");
}
