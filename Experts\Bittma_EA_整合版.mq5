//+------------------------------------------------------------------+
//| Bittma EA 整合版.mq5                                             |
//| 结合ATR动态加仓策略和账户信息面板功能                             |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.meta.com"
#property version   "1.20"

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/SymbolInfo.mqh>
#include <Arrays/ArrayLong.mqh>
#include <Indicators/Indicator.mqh>
#include <Indicators/Trend.mqh>
#include <Indicators/Oscilators.mqh>
#include <Guapit/controls/Button.mqh>

//--- 输入参数
input group "===== 资金管理设置 ====="
input bool       UseCompoundMode = true;             // 使用复利模式
input double BaseEquityPerLot = 5000.0;       // 每0.01手 所需的资金(复利模式) 
input double FixedLotSize = 0.01;                   // 固定手数(非复利模式) 
input double MaxRiskPercent = 10.0;               // 账户最大风险比例(触发平仓)
input int         MaxLayers = 15;                             // 最大加仓层数
input double BaseProfitTarget = 2000.0;       // 基础止盈目标(美元) 

//--- ATR 参数
input group "===== ATR 参数 ====="
input int         ATRPeriod = 14;                             // ATR 周期
input double ATRMultiplier = 5.0;                   // ATR 乘数(加仓间距)
input double InitialATRMultiplier = 10.0;   // 初始加仓ATR乘数
input double StopLossATRMultiplier = 3.0;   // 止损ATR乘数

//--- 加仓参数
input group "===== 加仓参数 =====" 
input double InitialLayerMultiplier = 1.5;   // 初期加仓倍数
input double MidLayerMultiplier = 1.3;           // 中期加仓倍数
input double LateLayerMultiplier = 1.1;         // 后期加仓倍数
input int         MidLayerStart = 2;                       // 中期加仓起始层数
input int         LateLayerStart = 6;                     // 后期加仓起始层数
input double MaxPositionSizePercent = 10.0;   // 最大仓位占比(%)

//--- 止盈止损参数
input group "===== 止盈止损参数 =====" 
input int         BreakEvenPoints = 300;               // 保本点数
input int         TrailPoints = 50;                         // 跟踪止损点数
input double MinStopDistanceMultiplier = 1.5;   // 最小止损距离乘数

//--- 全局变量
CTrade               trade;
CPositionInfo        *positionInfo;
CSymbolInfo          symbolInfo;
CArrayLong           m_arr_tickets;
Button               btnBuySell, btnCloseAll;

double               initialEquity;
double               currentEquity;
double               highestEquity;
bool                 trendDirection;
int                  currentLayer;
double               lastBuyPrice;
double               lastSellPrice;
int                  atrHandle;
int                  macdHandle;
double               symbolPoint;
double               symbolMinLot;
double               symbolLotStep;
double               symbolMaxLot;
int                  symbolTradeStopsLevel;
int                  symbolTradeFreezeLevel;
double               account_equity;
double               account_balance;
double               account_profit;
double               point_value;
double               long_volume;
double               short_volume;
double               total_volume;
double               daily_profit;
double               daily_start_balance;
datetime             last_daily_check;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
      // 初始化指标句柄
      atrHandle = iATR(_Symbol, _Period, ATRPeriod);
      macdHandle = iMACD(_Symbol, _Period, 12, 26, 9, PRICE_CLOSE);
      
      // 初始化持仓信息对象
      positionInfo = new CPositionInfo();
      
      // 设置初始资金
      initialEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      currentEquity = initialEquity;
      highestEquity = initialEquity;
      
      // 初始化交易变量
      currentLayer = 0;
      lastBuyPrice = 0;
      lastSellPrice = 0;
      trendDirection = false;
      
      // 设置交易参数
      trade.SetDeviationInPoints(10);
      trade.SetTypeFilling(ORDER_FILLING_FOK);
      
      // 获取交易品种属性
      symbolPoint = _Point;
      symbolMinLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
      symbolLotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
      symbolMaxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

      // 获取交易属性的数值
      symbolTradeStopsLevel = GetSymbolIntegerInfo(_Symbol, SYMBOL_TRADE_STOPS_LEVEL, "交易止损级别");
      symbolTradeFreezeLevel = GetSymbolIntegerInfo(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL, "交易冻结级别");

      // 输出交易品种属性
      Print(_Symbol, " 交易属性 - 最小手数: ", symbolMinLot, 
                                    ", 手数步长: ", symbolLotStep,
                                    ", 最大手数: ", symbolMaxLot);
      Print("最小止损距离: ", symbolTradeStopsLevel, " points");
      Print("止损冻结区域: ", symbolTradeFreezeLevel, " points");
      
      // 初始化当日收益相关变量
      daily_profit = 0.0;
      daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      last_daily_check = 0;
      
      // 创建UI界面
      CreateUI();
      
      // 创建信息面板
      CreateInfoPanel();
      
      return(INIT_SUCCEEDED);
}

// 获取整型交易属性的数值   
int GetSymbolIntegerInfo(const string symbol, const ENUM_SYMBOL_INFO_INTEGER property_id, const string info_name) 
{
        long value = 0;
        if (SymbolInfoInteger(symbol, property_id, value))
        {
                Print("获取交易", info_name, ": ", value);
                return (int)value;
        }
        else
        {
                Print("获取", info_name, "失败: ", GetLastError());
                return 0;
        }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
      // 释放指标句柄
      IndicatorRelease(atrHandle);
      IndicatorRelease(macdHandle);
      
      // 释放持仓信息对象
      if(CheckPointer(positionInfo) == POINTER_DYNAMIC)
            delete positionInfo;
      
      // 删除信息面板和UI界面
      ObjectsDeleteAll(0, "InfoPanel_");
      ObjectsDeleteAll(0, "gp_button_");
}

//+------------------------------------------------------------------+
//| 开多单函数                                                       |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double atr = GetATRValue();
    double lotSize = CalculateLotSize();
    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = price - atr * StopLossATRMultiplier;
    double tp = price + atr * ATRMultiplier;
    
    trade.Buy(lotSize, _Symbol, price, sl, tp, "Bittma EA Buy Order");
    lastBuyPrice = price;
    currentLayer++;
}

//+------------------------------------------------------------------+
//| 开空单函数                                                       |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double atr = GetATRValue();
    double lotSize = CalculateLotSize();
    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = price + atr * StopLossATRMultiplier;
    double tp = price - atr * ATRMultiplier;
    
    trade.Sell(lotSize, _Symbol, price, sl, tp, "Bittma EA Sell Order");
    lastSellPrice = price;
    currentLayer++;
}

//+------------------------------------------------------------------+
//| 平仓所有仓位函数                                                 |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i=PositionsTotal()-1; i>=0; i--)
    {
        if(positionInfo.SelectByIndex(i))
        {
            if(positionInfo.Symbol() == _Symbol)
            {
                trade.PositionClose(positionInfo.Ticket());
            }
        }
    }
    currentLayer = 0;
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理按钮点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "gp_button_buysell")
        {
            // 多空双向下单按钮
            OpenBuyOrder();
            OpenSellOrder();
            btnBuySell.State(false);
            btnBuySell.Update();
        }
        else if(sparam == "gp_button_close")
        {
            // 平仓按钮点击
            CloseAllPositions();
            btnCloseAll.State(false);
            btnCloseAll.Update();
        }
    }
}

//+------------------------------------------------------------------+
//| 创建UI界面                                                       |
//+------------------------------------------------------------------+
void CreateUI()
{
    // 创建多空双向按钮
    btnBuySell = new Button();
    btnBuySell.Create("gp_button_buysell", 150, 250-50);
    btnBuySell.Size(100, 32);
    btnBuySell.Text("多空开仓");
    btnBuySell.Font("极影毁片荧圆", 12);
    btnBuySell.Color(clrWhite);
    btnBuySell.BGColor(clrMediumBlue);
    btnBuySell.BorderColor(clrDarkBlue);
    btnBuySell.State(false);
    btnBuySell.Update();
    
    // 创建平仓按钮
    btnCloseAll = new Button();
    btnCloseAll.Create("gp_button_close", 150, 200-50);
    btnCloseAll.Size(100, 32);
    btnCloseAll.Text("一键平仓");
    btnCloseAll.Font("极影毁片荧圆", 12);
    btnCloseAll.Color(clrWhite);
    btnCloseAll.BGColor(clrCrimson);
    btnCloseAll.BorderColor(clrBrown);
    btnCloseAll.State(false);
    btnCloseAll.Update();
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
    // 创建信息面板背景
    ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 5);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 25);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 220);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 210);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrMediumBlue);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
    
    // 创建标题
    ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 35);
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "账户信息");
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrWhiteSmoke);
    
    // 创建账户余额标签
    ObjectCreate(0, "InfoPanel_Balance", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_YDISTANCE, 60 + 8);
    ObjectSetString(0, "InfoPanel_Balance", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_COLOR, clrWhiteSmoke);
    
    // 创建账户净值标签
    ObjectCreate(0, "InfoPanel_Equity", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_YDISTANCE, 80 + 10);
    ObjectSetString(0, "InfoPanel_Equity", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_COLOR, clrLightYellow);
    
    // 创建浮动盈亏标签
    ObjectCreate(0, "InfoPanel_Profit", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_YDISTANCE, 100 + 13);
    ObjectSetString(0, "InfoPanel_Profit", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, clrBlack);
    
    // 创建多单总量标签
    ObjectCreate(0, "InfoPanel_LongVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_YDISTANCE, 120 + 16);
    ObjectSetString(0, "InfoPanel_LongVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建空单总量标签
    ObjectCreate(0, "InfoPanel_ShortVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_YDISTANCE, 140 + 17);
    ObjectSetString(0, "InfoPanel_ShortVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建总仓位标签
    ObjectCreate(0, "InfoPanel_TotalVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_YDISTANCE, 160 + 19);
    ObjectSetString(0, "InfoPanel_TotalVolume", OBJPROP_FONT, "极影毁片荧圆");

    // 创建信息面板背景
    ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 5);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 25);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 220);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 210);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrMediumBlue);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
    
    // 创建标题
    ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 35);
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "账户信息");
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrWhiteSmoke);
    
    // 创建账户余额标签
    ObjectCreate(0, "InfoPanel_Balance", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_YDISTANCE, 60 + 8);
    ObjectSetString(0, "InfoPanel_Balance", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_COLOR, clrWhiteSmoke);
    
    // 创建账户净值标签
    ObjectCreate(0, "InfoPanel_Equity", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_YDISTANCE, 80 + 10);
    ObjectSetString(0, "InfoPanel_Equity", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_COLOR, clrLightYellow);
    
    // 创建浮动盈亏标签
    ObjectCreate(0, "InfoPanel_Profit", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_YDISTANCE, 100 + 13);
    ObjectSetString(0, "InfoPanel_Profit", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, clrBlack);
    
    // 创建多单总量标签
    ObjectCreate(0, "InfoPanel_LongVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_YDISTANCE, 120 + 16);
    ObjectSetString(0, "InfoPanel_LongVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_LongVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建空单总量标签
    ObjectCreate(0, "InfoPanel_ShortVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_YDISTANCE, 140 + 17);
    ObjectSetString(0, "InfoPanel_ShortVolume", OBJPROP_FONT, "极影毁片荧圆");
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_FONTSIZE, 16);
    ObjectSetInteger(0, "InfoPanel_ShortVolume", OBJPROP_COLOR, clrLightYellow);
    
    // 创建总仓位标签
    ObjectCreate(0, "InfoPanel_TotalVolume", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_TotalVolume", OBJPROP_YDISTANCE, 160 + 19);
    ObjectSetString(0, "InfoPanel_TotalVolume", OBJPROP_FONT, "极影毁片荧圆");
}