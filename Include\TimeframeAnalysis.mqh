//+------------------------------------------------------------------+
//|                                              TimeframeAnalysis.mqh |
//|                                      Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.metaquotes.net |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net"
#property version   "1.00"

#include <TrendAnalysis.mqh>

#include "CompressionDetection.mqh"

// 时间框架压缩结构
struct TimeframeCompression {
    COMPRESSION_STATE h1State;    // 1小时压缩状态
    COMPRESSION_STATE h4State;    // 4小时压缩状态
    COMPRESSION_STATE d1State;    // 日线压缩状态
    COMPRESSION_STATE w1State;    // 周线压缩状态
    COMPRESSION_STATE mnState;    // 月线压缩状态
    double consistencyScore;      // 一致性评分
};

//+------------------------------------------------------------------+
//| 计算指定时间框架的压缩指标                                          |
//+------------------------------------------------------------------+
CompressionIndicators CalculateTimeframeIndicators(ENUM_TIMEFRAMES timeframe,
                                                  int volatilityPeriod,
                                                  int bollingerPeriod,
                                                  double bollingerDeviation,
                                                  int atrPeriod,
                                                  int volumePeriod) {
    CompressionIndicators result;

    // 直接使用指定时间框架计算指标，不切换图表
    result.volatility = CalculateVolatilityCompressionTF(timeframe, volatilityPeriod);
    result.bbWidth = CalculateBollingerBandWidthTF(timeframe, bollingerPeriod, bollingerDeviation);
    result.atrValue = CalculateATRDynamicsTF(timeframe, atrPeriod);
    result.volumeCompress = CalculateVolumeCompressionTF(timeframe, volumePeriod);
    result.candlePattern = IdentifyCandlePatternTF(timeframe);

    return result;
}

//+------------------------------------------------------------------+
//| 分析多时间框架压缩状态                                             |
//+------------------------------------------------------------------+
TimeframeCompression AnalyzeTimeframes(int volatilityPeriod, 
                                      int bollingerPeriod, 
                                      double bollingerDeviation, 
                                      int atrPeriod, 
                                      int volumePeriod) {
    TimeframeCompression result;
    
    // 计算各个时间框架的压缩指标
    CompressionIndicators h1Indicators = CalculateTimeframeIndicators(PERIOD_H1, 
                                                                     volatilityPeriod, 
                                                                     bollingerPeriod, 
                                                                     bollingerDeviation, 
                                                                     atrPeriod, 
                                                                     volumePeriod);
                                                                     
    CompressionIndicators h4Indicators = CalculateTimeframeIndicators(PERIOD_H4, 
                                                                     volatilityPeriod, 
                                                                     bollingerPeriod, 
                                                                     bollingerDeviation, 
                                                                     atrPeriod, 
                                                                     volumePeriod);
                                                                     
    CompressionIndicators d1Indicators = CalculateTimeframeIndicators(PERIOD_D1, 
                                                                     volatilityPeriod, 
                                                                     bollingerPeriod, 
                                                                     bollingerDeviation, 
                                                                     atrPeriod, 
                                                                     volumePeriod);
                                                                     
    CompressionIndicators w1Indicators = CalculateTimeframeIndicators(PERIOD_W1, 
                                                                     volatilityPeriod, 
                                                                     bollingerPeriod, 
                                                                     bollingerDeviation, 
                                                                     atrPeriod, 
                                                                     volumePeriod);
                                                                     
    CompressionIndicators mnIndicators = CalculateTimeframeIndicators(PERIOD_MN1, 
                                                                     volatilityPeriod, 
                                                                     bollingerPeriod, 
                                                                     bollingerDeviation, 
                                                                     atrPeriod, 
                                                                     volumePeriod);
    
    // 评估各个时间框架的压缩状态
    result.h1State = EvaluateCompression(h1Indicators);
    result.h4State = EvaluateCompression(h4Indicators);
    result.d1State = EvaluateCompression(d1Indicators);
    result.w1State = EvaluateCompression(w1Indicators);
    result.mnState = EvaluateCompression(mnIndicators);
    
    // 计算时间框架一致性评分
    result.consistencyScore = CalculateConsistencyScore(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| 计算时间框架一致性评分                                             |
//+------------------------------------------------------------------+
double CalculateConsistencyScore(const TimeframeCompression &tfCompression) {
    // 将压缩状态转换为数值
    int h1Value = (int)tfCompression.h1State;
    int h4Value = (int)tfCompression.h4State;
    int d1Value = (int)tfCompression.d1State;
    int w1Value = (int)tfCompression.w1State;
    int mnValue = (int)tfCompression.mnState;
    
    // 计算平均值
    double avgValue = (h1Value + h4Value + d1Value + w1Value + mnValue) / 5.0;
    
    // 计算标准差
    double sumSquared = MathPow(h1Value - avgValue, 2) +
                       MathPow(h4Value - avgValue, 2) +
                       MathPow(d1Value - avgValue, 2) +
                       MathPow(w1Value - avgValue, 2) +
                       MathPow(mnValue - avgValue, 2);
    double stdDev = MathSqrt(sumSquared / 5.0);
    
    // 计算一致性评分（标准差越小，一致性越高）
    double consistencyScore = 100.0 - (stdDev * 20.0); // 将标准差转换为0-100的评分
    
    // 限制在0-100范围内
    consistencyScore = MathMax(0.0, MathMin(100.0, consistencyScore));
    
    return consistencyScore;
}

//+------------------------------------------------------------------+
//| 计算爆发概率                                                       |
//+------------------------------------------------------------------+
double CalculateBreakoutProbability(const TimeframeCompression &tfCompression, 
                                   const CompressionIndicators &currentIndicators) {
    // 基础概率（基于当前时间框架的压缩状态）
    double baseProbability = 0.0;
    COMPRESSION_STATE currentState = EvaluateCompression(currentIndicators);
    
    switch(currentState) {
        case EXTREME_COMPRESSION: baseProbability = 90.0; break;
        case HEAVY_COMPRESSION:   baseProbability = 70.0; break;
        case MEDIUM_COMPRESSION:  baseProbability = 50.0; break;
        case LIGHT_COMPRESSION:   baseProbability = 30.0; break;
        default:                  baseProbability = 10.0; break;
    }
    
    // 多时间框架一致性加成
    double consistencyBonus = tfCompression.consistencyScore * 0.2; // 最多加20%
    
    // 高级时间框架加成（日线、周线、月线压缩状态越高，概率越大）
    double higherTfBonus = 0.0;
    
    // 日线加成
    switch(tfCompression.d1State) {
        case EXTREME_COMPRESSION: higherTfBonus += 10.0; break;
        case HEAVY_COMPRESSION:   higherTfBonus += 7.0; break;
        case MEDIUM_COMPRESSION:  higherTfBonus += 4.0; break;
        case LIGHT_COMPRESSION:   higherTfBonus += 2.0; break;
        default:                  break;
    }
    
    // 周线加成
    switch(tfCompression.w1State) {
        case EXTREME_COMPRESSION: higherTfBonus += 7.0; break;
        case HEAVY_COMPRESSION:   higherTfBonus += 5.0; break;
        case MEDIUM_COMPRESSION:  higherTfBonus += 3.0; break;
        case LIGHT_COMPRESSION:   higherTfBonus += 1.0; break;
        default:                  break;
    }
    
    // 月线加成
    switch(tfCompression.mnState) {
        case EXTREME_COMPRESSION: higherTfBonus += 5.0; break;
        case HEAVY_COMPRESSION:   higherTfBonus += 3.0; break;
        case MEDIUM_COMPRESSION:  higherTfBonus += 2.0; break;
        case LIGHT_COMPRESSION:   higherTfBonus += 1.0; break;
        default:                  break;
    }
    
    // 计算总概率
    double totalProbability = baseProbability + consistencyBonus + higherTfBonus;
    
    // 限制在0-100范围内
    totalProbability = MathMax(0.0, MathMin(100.0, totalProbability));
    
    return totalProbability;
}

//+------------------------------------------------------------------+
//| 获取时间框架的字符串描述                                           |
//+------------------------------------------------------------------+
string GetTimeframeDescription(ENUM_TIMEFRAMES timeframe) {
    switch(timeframe) {
        case PERIOD_M1:  return "M1";
        case PERIOD_M5:  return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1:  return "H1";
        case PERIOD_H4:  return "H4";
        case PERIOD_D1:  return "D1";
        case PERIOD_W1:  return "W1";
        case PERIOD_MN1: return "MN";
        default:         return "未知";
    }
}

//+------------------------------------------------------------------+
//| 日线区间分析结构                                                   |
//+------------------------------------------------------------------+
struct DailyRangeAnalysis {
    double currentRange;
    double historicalAvgRange;
    bool isContracted;
};

//+------------------------------------------------------------------+
//| 日线区间分析函数                                                   |
//+------------------------------------------------------------------+
DailyRangeAnalysis AnalyzeDailyRange(int dailyRangePeriod = 5,
                                    int dailyHistoryPeriod = 180,
                                    double dailyRangeThreshold = 0.35) {
    DailyRangeAnalysis analysis;

    // 计算当前区间
    double dailyHighs[], dailyLows[];
    if(CopyHigh(_Symbol, PERIOD_D1, 0, dailyRangePeriod, dailyHighs) != dailyRangePeriod ||
       CopyLow(_Symbol, PERIOD_D1, 0, dailyRangePeriod, dailyLows) != dailyRangePeriod) {
        Print("日线数据获取失败! 错误代码 = ", GetLastError());
        analysis.isContracted = false;
        return analysis;
    }

    int maxIndex = ArrayMaximum(dailyHighs);
    int minIndex = ArrayMinimum(dailyLows);
    analysis.currentRange = dailyHighs[maxIndex] - dailyLows[minIndex];

    // 计算历史平均
    double dailyATRs[];
    int atrHandle = iATR(_Symbol, PERIOD_D1, 1);
    if(atrHandle == INVALID_HANDLE) {
        Print("ATR指标创建失败! 错误代码 = ", GetLastError());
        analysis.isContracted = false;
        return analysis;
    }

    if(CopyBuffer(atrHandle, 0, 0, dailyHistoryPeriod, dailyATRs) != dailyHistoryPeriod) {
        Print("ATR数据复制失败! 错误代码 = ", GetLastError());
        IndicatorRelease(atrHandle);
        analysis.isContracted = false;
        return analysis;
    }

    IndicatorRelease(atrHandle);

    double sum = 0;
    for(int i=0; i<dailyHistoryPeriod; i++) sum += dailyATRs[i];
    analysis.historicalAvgRange = (sum / dailyHistoryPeriod) * dailyRangePeriod;

    analysis.isContracted = analysis.currentRange < (analysis.historicalAvgRange * dailyRangeThreshold);

    return analysis;
}

//+------------------------------------------------------------------+
//| 增强版爆发概率计算（整合趋势增强指标）                               |
//+------------------------------------------------------------------+
double CalculateEnhancedBreakoutProbability(const TimeframeCompression &tfCompression,
                                          const CompressionIndicators &currentIndicators,
                                          const TrendAnalysis &trendAnalysis) {
    // 获取基础概率
    double baseProb = CalculateBreakoutProbability(tfCompression, currentIndicators);

    // === 原有因子 ===
    // 趋势连续性因子 (0-20分)
    double continuityFactor = MathMin(trendAnalysis.consecutiveBars / 5.0, 1.0) * 20.0;

    // 动量因子 (0-15分)
    double momentumFactor = (trendAnalysis.momentumScore / 100.0) * 15.0;

    // 成交量确认因子 (0-10分或-5分)
    double volumeFactor = trendAnalysis.volumeConfirmation ? 10.0 : -5.0;

    // 回撤深度因子 (0-10分)
    double retracementFactor = 0.0;
    if(trendAnalysis.retracementDepth < 0.382) {
        retracementFactor = 10.0 * (1.0 - trendAnalysis.retracementDepth / 0.382);
    } else if(trendAnalysis.retracementDepth > 0.618) {
        retracementFactor = -5.0;
    } else {
        retracementFactor = 5.0 * (1.0 - (trendAnalysis.retracementDepth - 0.382) / (0.618 - 0.382));
    }

    // === 新增：Deepseek优化因子 ===
    // 趋势质量因子 (0-25分)
    double qualityFactor = 0.0;
    switch(trendAnalysis.quality) {
        case VERY_STRONG_TREND_QUALITY: qualityFactor = 25.0; break;
        case STRONG_TREND_QUALITY:      qualityFactor = 15.0; break;
        case EARLY_TREND_QUALITY:       qualityFactor = 5.0; break;
        case NO_TREND_QUALITY:          qualityFactor = -20.0; break;
    }

    // 斐波那契回撤因子 (0-15分或-15分)
    double fibFactor = 0.0;
    if(trendAnalysis.fibonacciRetrace > 0.382 && trendAnalysis.fibonacciRetrace < 0.618) {
        fibFactor = 15.0; // 黄金回撤区间
    } else if(trendAnalysis.fibonacciRetrace < 0.236) {
        fibFactor = -15.0; // 回撤过浅，可能假突破
    } else if(trendAnalysis.fibonacciRetrace > 0.786) {
        fibFactor = -10.0; // 回撤过深
    } else {
        fibFactor = 5.0; // 其他区间
    }

    // 多时间框架一致性因子 (0-10分或-10分)
    double mtfFactor = trendAnalysis.multiTimeframeAlign ? 10.0 : -10.0;

    // MACD健康度因子 (0-8分)
    double macdFactor = 0.0;
    if(MathAbs(trendAnalysis.macdValue) > 0) {
        macdFactor = MathMin(8.0, MathAbs(trendAnalysis.macdValue) * 1000); // 放大MACD值
    }

    // RSI健康度因子 (0-7分或-7分)
    double rsiFactor = 0.0;
    if(trendAnalysis.rsiValue > 45 && trendAnalysis.rsiValue < 75) {
        rsiFactor = 7.0; // 健康RSI区间
    } else if(trendAnalysis.rsiValue < 25 || trendAnalysis.rsiValue > 85) {
        rsiFactor = -7.0; // 极端RSI区间
    }

    // === 增强概率计算（调整权重分配）===
    double enhancedProb = baseProb * 0.55 +           // 基础概率权重55%
                         qualityFactor * 0.15 +       // 趋势质量权重15%
                         continuityFactor * 0.10 +    // 连续性权重10%
                         momentumFactor * 0.08 +      // 动量权重8%
                         fibFactor * 0.05 +           // 斐波那契权重5%
                         mtfFactor * 0.03 +           // 多时间框架权重3%
                         volumeFactor * 0.02 +        // 成交量权重2%
                         macdFactor * 0.01 +          // MACD权重1%
                         rsiFactor * 0.01;            // RSI权重1%

    // 限制在0-100范围内
    return MathMax(0.0, MathMin(100.0, enhancedProb));
}