//+------------------------------------------------------------------+
//|                                           CompressionDetectionEA.mq5 |
//|                                      Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.metaquotes.net |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net"
#property version   "1.10"

#include <CompressionDetection.mqh>
#include <TimeframeAnalysis.mqh>
#include <TrendAnalysis.mqh>

// 市场状态枚举
enum MARKET_STATE {
    MARKET_TRENDING,    // 趋势市场
    MARKET_RANGING,     // 震荡市场
    MARKET_LOW_VOL,     // 低波动市场
    MARKET_HIGH_VOL,    // 高波动市场
    MARKET_EVENT_DRIVEN // 事件驱动市场
};

// 基础周期参数 (统一管理相同用途的周期)
input int    ShortPeriod = 14;               // 短期周期 (ATR, ADX等)
input int    MediumPeriod = 20;              // 中期周期 (波动率, 成交量, 布林带等)
input int    LongPeriod = 50;                // 长期周期 (趋势分析等)
input bool EnableMultiTimeframe = true;      // 启用多时间框架分析
input int UpdateInterval = 60;               // 多时间框架更新间隔(秒)

// === 新增：Deepseek建议的增强参数 ===
input string Deepseek_Settings = "------- Deepseek优化设置 -------";
input bool   Enable_Advanced_Trend_Filter = true;  // 启用高级趋势过滤
input double Fibonacci_Retrace_Threshold = 0.618;  // 斐波那契回撤阈值
input int    Trend_Confirmation_Bars = 3;          // 趋势确认K线数量
input double RSI_Momentum_Threshold = 45;          // RSI动量阈值
input int    ATR_History = 100;              // ATR历史均值长度 (100-150最佳)
input double BW_StdDev = 1.8;                // 布林带标准差 (1.8-2.2最佳)
input int    BW_History = 200;               // 布林带宽度历史长度 (200最佳)
input double ADX_Threshold = 16;             // ADX极低阈值 (15-18最佳)
input double MA_Entangle_Threshold = 0.004;  // 均线缠绕阈值 (0.4%最佳)
input int    MA_Period1 = 10;                // 短期均线
input int    MA_Period2 = 20;                // 中期均线
input int    MA_Period3 = 50;                // 中期均线
input int    MA_Period4 = 144;               // 长期均线 
input int    MA_Period5 = 275;               // 长期均线 
input int    DailyRangePeriod = 5;           // 日线区间分析周期 (5-7最佳)
input int    Daily_History_Period = 180;     // 日线历史数据周期 (180-200最佳)
input double Daily_Range_Threshold = 0.35;   // 日线区间收缩阈值 (35%最佳)
input int    H4_Body_Avg_N = 6;              // H4实体均值根数 (5-6最佳)
input double H4_Body_Threshold = 0.25;       // H4实体极小阈值 (25%最佳)
input bool   EnableAdaptiveParameters = true; // 启用自适应参数
input double LearningRate = 0.01;            // 学习率 (0.01-0.05)

// 界面元素名称
#define PANEL_NAME "CompressionPanel"
#define VOLATILITY_LABEL "VolatilityLabel"
#define BB_WIDTH_LABEL "BBWidthLabel"
#define ATR_LABEL "ATRLabel"
#define VOLUME_LABEL "VolumeLabel"
#define PATTERN_LABEL "PatternLabel"
#define STATE_LABEL "StateLabel"
#define COMPRESSION_METER "CompressionMeter"
#define H1_LABEL "H1Label"
#define H4_LABEL "H4Label"
#define D1_LABEL "D1Label"
#define W1_LABEL "W1Label"
#define MN_LABEL "MNLabel"
#define CONSISTENCY_LABEL "ConsistencyLabel"
#define BREAKOUT_PROB_LABEL "BreakoutProbLabel"
#define BREAKOUT_METER "BreakoutMeter"
#define TREND_DIRECTION_LABEL "TrendDirectionLabel"
#define TREND_STRENGTH_LABEL "TrendStrengthLabel"
#define TREND_SCORE_LABEL "TrendScoreLabel"
#define TREND_METER "TrendMeter"
#define EXPECTED_DIRECTION_LABEL "ExpectedDirectionLabel"
#define MARKET_STATE_LABEL "MarketStateLabel"
#define TREND_CONSECUTIVE_LABEL "TrendConsecutiveLabel"
#define MOMENTUM_SCORE_LABEL "MomentumScoreLabel"
#define VOLUME_CONFIRM_LABEL "VolumeConfirmLabel"
#define RETRACEMENT_LABEL "RetracementLabel"

// 全局变量
CompressionIndicators g_indicators;   // 当前时间框架指标数据
TimeframeCompression g_tfCompression; // 多时间框架压缩数据
TrendAnalysis g_trendAnalysis;       // 趋势分析数据
bool g_initialized = false;           // 初始化标志
datetime g_lastMultiTfUpdate = 0;     // 上次多时间框架更新时间

// 市场状态相关全局变量
MARKET_STATE g_CurrentMarketState = MARKET_RANGING;  // 当前市场状态
MARKET_STATE g_LastMarketState = MARKET_RANGING;     // 上次市场状态
double g_Dynamic_ADX_Threshold;       // 动态ADX阈值
double g_Dynamic_BW_Threshold;        // 动态布林带阈值
double g_Dynamic_Entangle_Threshold;  // 动态均线缠绕阈值
datetime g_lastStateUpdate = 0;       // 上次状态更新时间
double g_Learning_ADX_Adjust = 1.0;   // ADX学习调整系数
double g_Learning_BW_Adjust = 1.0;    // 布林带学习调整系数

// 统一指标管理器类
class UnifiedIndicatorManager {
private:
    double m_atr;
    double m_atr_ma;
    double m_adx;
    double m_di_plus;
    double m_di_minus;
    datetime m_lastCalc;

    // 缓存的ATR句柄和数据
    int m_atrHandle;
    double m_atrValues[];

public:
    UnifiedIndicatorManager() {
        m_lastCalc = 0;
        m_atrHandle = INVALID_HANDLE;
        ArraySetAsSeries(m_atrValues, true);
    }

    ~UnifiedIndicatorManager() {
        if(m_atrHandle != INVALID_HANDLE) {
            IndicatorRelease(m_atrHandle);
        }
    }
    
    // 获取ATR值
    double GetATR(int period) {
        if(TimeCurrent() - m_lastCalc > 300) { // 5分钟刷新一次
            int handle = iATR(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double atr[];
                ArraySetAsSeries(atr, true);
                if(CopyBuffer(handle, 0, 0, 1, atr) > 0) {
                    m_atr = atr[0];
                }
                IndicatorRelease(handle);
            }
            m_lastCalc = TimeCurrent();
        }
        return m_atr;
    }

    // 获取ATR均值
    double GetATRMA(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iMA(_Symbol, PERIOD_D1, period, 0, MODE_SMA, PRICE_TYPICAL);
            if(handle != INVALID_HANDLE) {
                double ma[];
                ArraySetAsSeries(ma, true);
                if(CopyBuffer(handle, 0, 0, 1, ma) > 0) {
                    m_atr_ma = ma[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_atr_ma;
    }

    // 获取ADX值
    double GetADX(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double adx[];
                ArraySetAsSeries(adx, true);
                if(CopyBuffer(handle, 0, 0, 1, adx) > 0) {
                    m_adx = adx[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_adx;
    }

    // 获取DI+值
    double GetDIPlus(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double di_plus[];
                ArraySetAsSeries(di_plus, true);
                if(CopyBuffer(handle, 1, 0, 1, di_plus) > 0) {
                    m_di_plus = di_plus[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_di_plus;
    }

    // 获取DI-值
    double GetDIMinus(int period) {
        if(TimeCurrent() - m_lastCalc > 300) {
            int handle = iADX(_Symbol, PERIOD_D1, period);
            if(handle != INVALID_HANDLE) {
                double di_minus[];
                ArraySetAsSeries(di_minus, true);
                if(CopyBuffer(handle, 2, 0, 1, di_minus) > 0) {
                    m_di_minus = di_minus[0];
                }
                IndicatorRelease(handle);
            }
        }
        return m_di_minus;
    }
    
    // 重置缓存
    void Reset() {
        m_lastCalc = 0;
    }
};

// 创建统一指标管理器实例
UnifiedIndicatorManager g_indicatorManager;

// 交易时段枚举
enum TRADING_SESSION {
    SESSION_ASIA = 0x01,
    SESSION_LONDON = 0x02,
    SESSION_NEWYORK = 0x04,
    SESSION_OVERLAP = 0x08
};

//+------------------------------------------------------------------+
//| 自定义函数：创建区域背景框                                       |
//+------------------------------------------------------------------+
void CreateZoneBackgrounds() {
    // 创建四个功能区域的背景框和标题
    
    // === 第一区域：基础压缩指标 (Y: 20-160) ===
    if(ObjectCreate(0, "Zone1_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_YDISTANCE, 15);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_XSIZE, 220);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_YSIZE, 160);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_BGCOLOR, C'15,25,35');
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_BORDER_COLOR, clrDeepSkyBlue);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_GLOW_SIZE, 2);
        ObjectSetInteger(0, "Zone1_BG", OBJPROP_GLOW_COLOR, clrRoyalBlue);
    }
    
    // 区域标题
    if(ObjectCreate(0, "Zone1_Title", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "Zone1_Title", OBJPROP_TEXT, "基础压缩指标");
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_XDISTANCE, 25);
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_YDISTANCE, 15);
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_COLOR, clrDodgerBlue);
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, "Zone1_Title", OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_GLOW_SIZE, 2);
        ObjectSetInteger(0, "Zone1_Title", OBJPROP_GLOW_COLOR, clrRoyalBlue);
    }
    
    // === 第二区域：多时间框架分析 (Y: 195-375) ===
    if(EnableMultiTimeframe) {
        if(ObjectCreate(0, "Zone2_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_XDISTANCE, 15);
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_YDISTANCE, 190);
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_XSIZE, 220);
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_YSIZE, 185);
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_BGCOLOR, C'25,25,25');
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_BORDER_COLOR, clrDimGray);
            ObjectSetInteger(0, "Zone2_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }
        
        // 区域标题
        if(ObjectCreate(0, "Zone2_Title", OBJ_LABEL, 0, 0, 0)) {
            ObjectSetString(0, "Zone2_Title", OBJPROP_TEXT, "多时间框架分析");
            ObjectSetInteger(0, "Zone2_Title", OBJPROP_XDISTANCE, 25);
            ObjectSetInteger(0, "Zone2_Title", OBJPROP_YDISTANCE, 190);
            ObjectSetInteger(0, "Zone2_Title", OBJPROP_COLOR, clrGold);
            ObjectSetInteger(0, "Zone2_Title", OBJPROP_FONTSIZE, 10);
            ObjectSetString(0, "Zone2_Title", OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, "Zone2_Title", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }
    }
    
    // === 第三区域：趋势分析 (Y: 385-585) ===
    if(ObjectCreate(0, "Zone3_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_YDISTANCE, 385);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_XSIZE, 380);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_YSIZE, 200);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_BGCOLOR, C'25,25,25');
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_BORDER_COLOR, clrDimGray);
        ObjectSetInteger(0, "Zone3_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
    
    // 区域标题
    if(ObjectCreate(0, "Zone3_Title", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "Zone3_Title", OBJPROP_TEXT, "趋势分析");
        ObjectSetInteger(0, "Zone3_Title", OBJPROP_XDISTANCE, 25);
        ObjectSetInteger(0, "Zone3_Title", OBJPROP_YDISTANCE, 385);
        ObjectSetInteger(0, "Zone3_Title", OBJPROP_COLOR, clrGold);
        ObjectSetInteger(0, "Zone3_Title", OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, "Zone3_Title", OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, "Zone3_Title", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
    
    // === 第四区域：市场状态 (Y: 595-665) ===
    if(ObjectCreate(0, "Zone4_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_YDISTANCE, 595);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_XSIZE, 380);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_YSIZE, 70);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_BGCOLOR, C'25,25,25');
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_BORDER_COLOR, clrDimGray);
        ObjectSetInteger(0, "Zone4_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
    
    // 区域标题
    if(ObjectCreate(0, "Zone4_Title", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "Zone4_Title", OBJPROP_TEXT, "市场状态");
        ObjectSetInteger(0, "Zone4_Title", OBJPROP_XDISTANCE, 25);
        ObjectSetInteger(0, "Zone4_Title", OBJPROP_YDISTANCE, 595);
        ObjectSetInteger(0, "Zone4_Title", OBJPROP_COLOR, clrGold);
        ObjectSetInteger(0, "Zone4_Title", OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, "Zone4_Title", OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, "Zone4_Title", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}

//+------------------------------------------------------------------+
//| 自定义函数：创建标签                                               |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color textColor) {
    if(ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, name, OBJPROP_TEXT, text);
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
        ObjectSetInteger(0, name, OBJPROP_COLOR, clrDodgerBlue);
        ObjectSetInteger(0, name, OBJPROP_BGCOLOR, clrDarkSlateGray);
        ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, clrDeepSkyBlue);
        ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_RIGHT_LOWER);
        ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
        ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, name, OBJPROP_GLOW_SIZE, 3);
        ObjectSetInteger(0, name, OBJPROP_GLOW_COLOR, clrRoyalBlue);
    }
}

//+------------------------------------------------------------------+
//| 通用函数：创建度量表（Deepseek增强版 - 带关键阈值标记）             |
//+------------------------------------------------------------------+
void CreateMeter(string meterName, int yPosition) {
    // 创建背景框
    string bgName = meterName + "_BG";
    if(ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, 15);
        ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, yPosition - 5);
        ObjectSetInteger(0, bgName, OBJPROP_XSIZE, 210);
        ObjectSetInteger(0, bgName, OBJPROP_YSIZE, 30);
        ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, C'15,15,15');
        ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, bgName, OBJPROP_BORDER_COLOR, clrDimGray);
        ObjectSetInteger(0, bgName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
    
    // 创建度量表
    if(ObjectCreate(0, meterName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(0, meterName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, meterName, OBJPROP_YDISTANCE, yPosition);
        ObjectSetInteger(0, meterName, OBJPROP_XSIZE, 200);
        ObjectSetInteger(0, meterName, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, meterName, OBJPROP_BGCOLOR, clrDarkGray);
        ObjectSetInteger(0, meterName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, meterName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    // === Deepseek建议：添加关键阈值标记线 ===
    CreateThresholdMarks(meterName, yPosition);
}

//+------------------------------------------------------------------+
//| 创建关键阈值标记线（Deepseek敏感度增强功能）                       |
//+------------------------------------------------------------------+
void CreateThresholdMarks(string meterName, int yPosition) {
    // 关键阈值位置计算（基于200像素宽度）
    int thresholds[6];
    thresholds[0] = 30; thresholds[1] = 45; thresholds[2] = 60;
    thresholds[3] = 70; thresholds[4] = 85; thresholds[5] = 95;

    color markColors[6];
    markColors[0] = clrDimGray; markColors[1] = clrGray; markColors[2] = clrSilver;
    markColors[3] = clrWhite; markColors[4] = clrYellow; markColors[5] = clrRed;

    for(int i = 0; i < 6; i++) {
        string markName = meterName + "_Mark" + IntegerToString(thresholds[i]);
        int xPos = 20 + (int)(200 * thresholds[i] / 100.0); // 计算X位置

        // 创建垂直标记线
        if(ObjectCreate(0, markName, OBJ_VLINE, 0, 0, 0)) {
            ObjectSetInteger(0, markName, OBJPROP_XDISTANCE, xPos);
            ObjectSetInteger(0, markName, OBJPROP_YDISTANCE, yPosition);
            ObjectSetInteger(0, markName, OBJPROP_COLOR, markColors[i]);
            ObjectSetInteger(0, markName, OBJPROP_WIDTH, 1);
            ObjectSetInteger(0, markName, OBJPROP_STYLE, STYLE_DOT);
        }

        // 创建数值标签
        string labelName = meterName + "_Label" + IntegerToString(thresholds[i]);
        if(ObjectCreate(0, labelName, OBJ_LABEL, 0, 0, 0)) {
            ObjectSetString(0, labelName, OBJPROP_TEXT, IntegerToString(thresholds[i]));
            ObjectSetInteger(0, labelName, OBJPROP_XDISTANCE, xPos - 5);
            ObjectSetInteger(0, labelName, OBJPROP_YDISTANCE, yPosition + 22);
            ObjectSetInteger(0, labelName, OBJPROP_COLOR, markColors[i]);
            ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
            ObjectSetString(0, labelName, OBJPROP_FONT, "Arial");
            ObjectSetInteger(0, labelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
        }
    }
}

//+------------------------------------------------------------------+
//| 清理阈值标记（Deepseek增强功能清理）                               |
//+------------------------------------------------------------------+
void CleanupThresholdMarks(string meterName) {
    int thresholds[6];
    thresholds[0] = 30; thresholds[1] = 45; thresholds[2] = 60;
    thresholds[3] = 70; thresholds[4] = 85; thresholds[5] = 95;

    for(int i = 0; i < 6; i++) {
        string markName = meterName + "_Mark" + IntegerToString(thresholds[i]);
        string labelName = meterName + "_Label" + IntegerToString(thresholds[i]);

        ObjectDelete(0, markName);
        ObjectDelete(0, labelName);
    }
    
    // 清理背景框
    string bgName = meterName + "_BG";
    ObjectDelete(0, bgName);
    
    // 清理填充矩形
    string fillName = meterName + "_Fill";
    ObjectDelete(0, fillName);
}

//+------------------------------------------------------------------+
//| 自定义函数：创建压缩度量表                                         |
//+------------------------------------------------------------------+
void CreateCompressionMeter() {
    CreateMeter(COMPRESSION_METER, 155);
    
    // 添加刻度标签
    if(ObjectCreate(0, "CompressionMeterLabel0", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "CompressionMeterLabel0", OBJPROP_TEXT, "0");
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_YDISTANCE, 180);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "CompressionMeterLabel0", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "CompressionMeterLabel0", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
    
    if(ObjectCreate(0, "CompressionMeterLabel50", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "CompressionMeterLabel50", OBJPROP_TEXT, "50");
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_XDISTANCE, 120);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_YDISTANCE, 180);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "CompressionMeterLabel50", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "CompressionMeterLabel50", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
    
    if(ObjectCreate(0, "CompressionMeterLabel100", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "CompressionMeterLabel100", OBJPROP_TEXT, "100");
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_XDISTANCE, 220);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_YDISTANCE, 180);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "CompressionMeterLabel100", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "CompressionMeterLabel100", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
}

//+------------------------------------------------------------------+
//| 自定义函数：创建爆发概率度量表                                     |
//+------------------------------------------------------------------+
void CreateBreakoutMeter() {
    CreateMeter(BREAKOUT_METER, 345);
    
    // 添加刻度标签
    if(ObjectCreate(0, "BreakoutMeterLabel0", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "BreakoutMeterLabel0", OBJPROP_TEXT, "0");
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_YDISTANCE, 370);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "BreakoutMeterLabel0", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "BreakoutMeterLabel0", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
    
    if(ObjectCreate(0, "BreakoutMeterLabel50", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "BreakoutMeterLabel50", OBJPROP_TEXT, "50");
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_XDISTANCE, 120);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_YDISTANCE, 370);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "BreakoutMeterLabel50", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "BreakoutMeterLabel50", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
    
    if(ObjectCreate(0, "BreakoutMeterLabel100", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "BreakoutMeterLabel100", OBJPROP_TEXT, "100");
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_XDISTANCE, 220);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_YDISTANCE, 370);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "BreakoutMeterLabel100", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "BreakoutMeterLabel100", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
}

//+------------------------------------------------------------------+
//| 自定义函数：创建趋势度量表                                         |
//+------------------------------------------------------------------+
void CreateTrendMeter() {
    CreateMeter(TREND_METER, 550);
    
    // 添加刻度标签
    if(ObjectCreate(0, "TrendMeterLabel0", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "TrendMeterLabel0", OBJPROP_TEXT, "0");
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_YDISTANCE, 575);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "TrendMeterLabel0", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "TrendMeterLabel0", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
    
    if(ObjectCreate(0, "TrendMeterLabel50", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "TrendMeterLabel50", OBJPROP_TEXT, "50");
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_XDISTANCE, 120);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_YDISTANCE, 575);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "TrendMeterLabel50", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "TrendMeterLabel50", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
    
    if(ObjectCreate(0, "TrendMeterLabel100", OBJ_LABEL, 0, 0, 0)) {
        ObjectSetString(0, "TrendMeterLabel100", OBJPROP_TEXT, "100");
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_XDISTANCE, 220);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_YDISTANCE, 575);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_COLOR, clrSilver);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_FONTSIZE, 8);
        ObjectSetString(0, "TrendMeterLabel100", OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, "TrendMeterLabel100", OBJPROP_ANCHOR, ANCHOR_CENTER); // 居中对齐
    }
}

//+------------------------------------------------------------------+
//| 通用函数：更新度量表（增强版颜色逻辑）                              |
//+------------------------------------------------------------------+
void UpdateMeter(string meterName, double value, double minValue = 0.0, double maxValue = 100.0) {
    // 计算标准化值
    double normalizedValue = (value - minValue) / (maxValue - minValue) * 100.0;
    normalizedValue = MathMax(0.0, MathMin(100.0, normalizedValue));

    // 智能颜色分配
    color meterColor;
    if(StringFind(meterName, "Trend") >= 0) {
        // 趋势度量表专用颜色方案（9级莫兰迪色系）
        if(normalizedValue >= 95) meterColor = 0xE74C3C;    // 朱红
        else if(normalizedValue >= 85) meterColor = 0xE67E22;  // 南瓜橙
        else if(normalizedValue >= 75) meterColor = 0xF1C40F;  // 向日葵黄
        else if(normalizedValue >= 65) meterColor = 0x2ECC71;  // 翡翠绿
        else if(normalizedValue >= 55) meterColor = 0x3498DB;  // 勿忘我蓝
        else if(normalizedValue >= 45) meterColor = 0x9B59B6;  // 紫水晶
        else if(normalizedValue >= 35) meterColor = 0x34495E;  // 湿沥青
        else if(normalizedValue >= 25) meterColor = 0x95A5A6;  // 灰云
        else meterColor = 0xECF0F1;                            // 云白
    } else {
        // 压缩度量表颜色方案（7级渐变）
        if(normalizedValue >= 95) meterColor = 0xC0392B;      // 深红
        else if(normalizedValue >= 85) meterColor = 0xD35400;  // 橘红
        else if(normalizedValue >= 70) meterColor = 0xF39C12;  // 亮橙
        else if(normalizedValue >= 60) meterColor = 0xF1C40F;  // 明黄
        else if(normalizedValue >= 50) meterColor = 0x27AE60;  // 深绿
        else if(normalizedValue >= 40) meterColor = 0x2980B9;  // 深蓝
        else meterColor = 0x2C3E50;                          // 午夜蓝
    }

    // 计算填充宽度
    int width = (int)(200 * normalizedValue / 100);
    width = MathMax(0, MathMin(200, width));

    // 创建或更新填充矩形
    string fillName = meterName + "_Fill";
    if(ObjectFind(0, fillName) < 0) {
        if(ObjectCreate(0, fillName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
            ObjectSetInteger(0, fillName, OBJPROP_XDISTANCE, 20);
            ObjectSetInteger(0, fillName, OBJPROP_YDISTANCE, ObjectGetInteger(0, meterName, OBJPROP_YDISTANCE));
            ObjectSetInteger(0, fillName, OBJPROP_XSIZE, width);
            ObjectSetInteger(0, fillName, OBJPROP_YSIZE, 20);
            ObjectSetInteger(0, fillName, OBJPROP_BGCOLOR, meterColor);
            ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
            ObjectSetInteger(0, fillName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }
    } else {
        ObjectSetInteger(0, fillName, OBJPROP_XSIZE, width);
        ObjectSetInteger(0, fillName, OBJPROP_BGCOLOR, meterColor);
    }

    // 添加渐变光晕效果（当值超过70时）
    if(normalizedValue > 70) {
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_RAISED);
        ObjectSetInteger(0, fillName, OBJPROP_WIDTH, 2);
    } else {
        ObjectSetInteger(0, fillName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(0, fillName, OBJPROP_WIDTH, 1);
    }
}

//+------------------------------------------------------------------+
//| 自定义函数：更新压缩度量表                                         |
//+------------------------------------------------------------------+
void UpdateCompressionMeter(double compressionLevel) {
    UpdateMeter(COMPRESSION_METER, compressionLevel, 0.0, 100.0);
}

//+------------------------------------------------------------------+
//| 自定义函数：更新爆发概率度量表                                     |
//+------------------------------------------------------------------+
void UpdateBreakoutMeter(double breakoutProb) {
    UpdateMeter(BREAKOUT_METER, breakoutProb, 0.0, 100.0);
}

//+------------------------------------------------------------------+
//| 自定义函数：更新趋势度量表                                         |
//+------------------------------------------------------------------+
void UpdateTrendMeter(double trendScore) {
    // 将趋势评分转换为0-100的范围
    double normalizedScore = MathAbs(trendScore);
    UpdateMeter(TREND_METER, normalizedScore, 0.0, 100.0);
}

//+------------------------------------------------------------------+
//| 获取当前交易时段                                                  |
//+------------------------------------------------------------------+
int GetCurrentSession() {
    MqlDateTime tm;
    TimeCurrent(tm);
    int hour = tm.hour;
    
    int session = 0;
    if(hour >= 1 && hour < 8) session |= SESSION_ASIA;
    if(hour >= 8 && hour < 16) session |= SESSION_LONDON;
    if(hour >= 13 && hour < 17) session |= SESSION_OVERLAP;
    if(hour >= 14 && hour < 22) session |= SESSION_NEWYORK;
    
    return session;
}

//+------------------------------------------------------------------+
//| 判断是否为重大事件日                                             |
//+------------------------------------------------------------------+
bool IsMajorNewsDay() {
    // 使用H1 ATR检测重大波动
    double h1_atr = iATR(_Symbol, PERIOD_H1, ShortPeriod);
    double h1_atr_ma = iMA(_Symbol, PERIOD_H1, ShortPeriod*2, 0, MODE_SMA, PRICE_TYPICAL);
    
    // 如果当前ATR显著高于均值，认为是重大事件日
    return (h1_atr > h1_atr_ma * 1.5);
}

//+------------------------------------------------------------------+
//| 分析市场状态                                                     |
//+------------------------------------------------------------------+
MARKET_STATE AnalyzeMarketState() {
    // 1. 获取波动率比率
    double atr = g_indicatorManager.GetATR(ShortPeriod);
    double atr_ma = g_indicatorManager.GetATRMA(ShortPeriod*2);
    double volatility_ratio = atr / atr_ma;
    
    // 2. 获取ADX和DI值
    double adx = g_indicatorManager.GetADX(ShortPeriod);
    double di_plus = g_indicatorManager.GetDIPlus(ShortPeriod);
    double di_minus = g_indicatorManager.GetDIMinus(ShortPeriod);
    double di_diff = MathAbs(di_plus - di_minus);
    
    // 3. 获取日内区间与ATR的比值
    MqlRates rates[];
    ArraySetAsSeries(rates, true);
    int copied = CopyRates(_Symbol, PERIOD_D1, 0, DailyRangePeriod, rates);
    
    double avg_range = 0;
    if(copied > 0) {
        for(int i = 0; i < copied; i++) {
            avg_range += (rates[i].high - rates[i].low);
        }
        avg_range /= copied;
    }
    
    double range_atr_ratio = avg_range / atr;
    
    // 4. 判断市场状态
    if(IsMajorNewsDay()) {
        return MARKET_EVENT_DRIVEN; // 重大事件日
    }
    else if(adx > 25 && di_diff > 10) {
        return MARKET_TRENDING; // 趋势市场
    }
    else if(volatility_ratio > 1.3) {
        return MARKET_HIGH_VOL; // 高波动市场
    }
    else if(volatility_ratio < 0.7 || range_atr_ratio < 0.8) {
        return MARKET_LOW_VOL; // 低波动市场
    }
    else {
        return MARKET_RANGING; // 震荡市场
    }
}

//+------------------------------------------------------------------+
//| 根据市场状态调整动态参数                                          |
//+------------------------------------------------------------------+
void AdjustDynamicParameters() {
    // 更新市场状态
    g_LastMarketState = g_CurrentMarketState;
    g_CurrentMarketState = AnalyzeMarketState();
    
    // 根据市场状态调整参数
    switch(g_CurrentMarketState) {
        case MARKET_TRENDING:
            g_Dynamic_ADX_Threshold = ADX_Threshold * 1.2 * g_Learning_ADX_Adjust;
            g_Dynamic_BW_Threshold = BW_StdDev * 1.3 * g_Learning_BW_Adjust;
            g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * 1.5;
            break;
            
        case MARKET_RANGING:
            g_Dynamic_ADX_Threshold = ADX_Threshold * 1.0 * g_Learning_ADX_Adjust;
            g_Dynamic_BW_Threshold = BW_StdDev * 1.0 * g_Learning_BW_Adjust;
            g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * 1.0;
            break;
            
        case MARKET_LOW_VOL:
            g_Dynamic_ADX_Threshold = ADX_Threshold * 0.8 * g_Learning_ADX_Adjust;
            g_Dynamic_BW_Threshold = BW_StdDev * 0.7 * g_Learning_BW_Adjust;
            g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * 0.6;
            break;
            
        case MARKET_HIGH_VOL:
            g_Dynamic_ADX_Threshold = ADX_Threshold * 1.5 * g_Learning_ADX_Adjust;
            g_Dynamic_BW_Threshold = BW_StdDev * 1.8 * g_Learning_BW_Adjust;
            g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * 2.0;
            break;
            
        case MARKET_EVENT_DRIVEN:
            g_Dynamic_ADX_Threshold = ADX_Threshold * 2.0 * g_Learning_ADX_Adjust;
            g_Dynamic_BW_Threshold = BW_StdDev * 2.5 * g_Learning_BW_Adjust;
            g_Dynamic_Entangle_Threshold = MA_Entangle_Threshold * 3.0;
            break;
    }
    
    // 显示市场状态和动态参数
    ShowMarketStateInUI();
}

//+------------------------------------------------------------------+
//| 在UI中显示市场状态和动态参数                                      |
//+------------------------------------------------------------------+
void ShowMarketStateInUI() {
    string stateDesc = "";
    color stateColor = clrWhite;
    
    switch(g_CurrentMarketState) {
        case MARKET_TRENDING:
            stateDesc = "趋势市场";
            stateColor = clrDodgerBlue;
            break;
        case MARKET_RANGING:
            stateDesc = "震荡市场";
            stateColor = clrYellow;
            break;
        case MARKET_LOW_VOL:
            stateDesc = "低波动市场";
            stateColor = clrLime;
            break;
        case MARKET_HIGH_VOL:
            stateDesc = "高波动市场";
            stateColor = clrOrange;
            break;
        case MARKET_EVENT_DRIVEN:
            stateDesc = "事件驱动市场";
            stateColor = clrRed;
            break;
    }
    
    // 创建或更新市场状态标签
    if(ObjectFind(0, MARKET_STATE_LABEL) < 0) {
        CreateLabel(MARKET_STATE_LABEL, "市场状态: " + stateDesc, 30, 615, stateColor);
    } else {
        ObjectSetString(0, MARKET_STATE_LABEL, OBJPROP_TEXT, "市场状态: " + stateDesc);
        ObjectSetInteger(0, MARKET_STATE_LABEL, OBJPROP_COLOR, stateColor);
    }
    
    // 创建或更新动态参数标签
    string dynamicParams = StringFormat("动态参数: ADX阈值=%.1f, BB宽度=%.1f, 均线缠绕=%.1f%%", 
                                      g_Dynamic_ADX_Threshold, 
                                      g_Dynamic_BW_Threshold, 
                                      g_Dynamic_Entangle_Threshold * 100);
                                      
    if(ObjectFind(0, "DynamicParamsLabel") < 0) {
        CreateLabel("DynamicParamsLabel", dynamicParams, 30, 635, clrSilver);
    } else {
        ObjectSetString(0, "DynamicParamsLabel", OBJPROP_TEXT, dynamicParams);
    }
}

//+------------------------------------------------------------------+
//| 判断是否为爆发信号                                               |
//+------------------------------------------------------------------+
bool IsBreakoutSignal(double breakoutProb) {
    double dynamicThreshold = 65.0; // 基础阈值

    // 根据市场状态调整阈值
    switch(g_CurrentMarketState) {
        case MARKET_HIGH_VOL:
            dynamicThreshold = 70.0; // 高波动市场提高阈值
            break;
        case MARKET_EVENT_DRIVEN:
            dynamicThreshold = 75.0; // 事件驱动市场进一步提升阈值
            break;
        case MARKET_LOW_VOL:
            dynamicThreshold = 60.0; // 低波动市场降低阈值
            break;
    }

    return breakoutProb >= dynamicThreshold;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                      |
//+------------------------------------------------------------------+
int OnInit() {
    // 创建区域背景框
    CreateZoneBackgrounds();
    
    // === 第一区域：基础压缩指标 (Y: 20-160) ===
    CreateLabel(VOLATILITY_LABEL, "波动率压缩: 0.00%", 30, 35, clrWhite);
    CreateLabel(BB_WIDTH_LABEL, "布林带压缩: 0.00%", 30, 55, clrWhite);
    CreateLabel(ATR_LABEL, "ATR压缩: 0.00%", 30, 75, clrWhite);
    CreateLabel(VOLUME_LABEL, "成交量压缩: 0.00%", 30, 95, clrWhite);
    CreateLabel(PATTERN_LABEL, "K线形态: -", 30, 115, clrWhite);
    CreateLabel(STATE_LABEL, "压缩状态: 无压缩", 30, 135, clrYellow);

    // 创建压缩度量表 (Y: 155)
    CreateCompressionMeter();
    
    // === 第二区域：多时间框架分析 (Y: 195-375) ===
    if(EnableMultiTimeframe) {
        CreateLabel(H1_LABEL, "H1: 无压缩", 30, 215, clrWhite);
        CreateLabel(H4_LABEL, "H4: 无压缩", 30, 235, clrWhite);
        CreateLabel(D1_LABEL, "D1: 无压缩", 30, 255, clrWhite);
        CreateLabel(W1_LABEL, "W1: 无压缩", 30, 275, clrWhite);
        CreateLabel(MN_LABEL, "MN: 无压缩", 30, 295, clrWhite);
        CreateLabel(CONSISTENCY_LABEL, "时间框架一致性: 0.00%", 30, 315, clrYellow);
        CreateLabel(BREAKOUT_PROB_LABEL, "爆发概率: 0.00%", 30, 335, clrMagenta);

        // 创建爆发概率度量表 (Y: 345)
        CreateBreakoutMeter();
    }
    
    // === 第三区域：趋势分析 (Y: 385-585) ===
    // 左侧列
    CreateLabel(TREND_DIRECTION_LABEL, "趋势方向: 中性", 30, 405, clrWhite);
    CreateLabel(TREND_STRENGTH_LABEL, "趋势强度: 弱", 30, 425, clrWhite);
    CreateLabel(TREND_SCORE_LABEL, "趋势评分: 0.00", 30, 445, clrWhite);
    CreateLabel(EXPECTED_DIRECTION_LABEL, "预期方向: 未知", 30, 465, clrWhite);
    CreateLabel(TREND_CONSECUTIVE_LABEL, "连续Bar数: 0", 30, 485, clrWhite);
    CreateLabel(MOMENTUM_SCORE_LABEL, "动量评分: 0.00", 30, 505, clrWhite);
    CreateLabel(VOLUME_CONFIRM_LABEL, "成交量确认: 否", 30, 525, clrWhite);
    CreateLabel(RETRACEMENT_LABEL, "回撤深度: 0.00%", 30, 545, clrWhite);

    // 右侧列 - Deepseek优化显示
    CreateLabel("TrendQualityLabel", "趋势质量: 无", 250, 405, clrWhite);
    CreateLabel("FibRetracementLabel", "斐波回撤: 0.000", 250, 425, clrWhite);
    CreateLabel("MTFAlignLabel", "多框架一致: 否", 250, 445, clrWhite);
    CreateLabel("TrendFilterLabel", "智能过滤: 关闭", 250, 465, clrWhite);

    // 创建趋势度量表 (Y: 550)
    CreateTrendMeter();

    // === 第四区域：市场状态 (Y: 595-665) ===
    // 初始化市场状态显示
    AdjustDynamicParameters();  // 初始化市场状态
    ShowMarketStateInUI();      // 立即显示市场状态

    // 设置定时器（每秒更新一次）
    EventSetTimer(1);

    g_initialized = true;
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // 删除定时器
    EventKillTimer();

    // === 清理第一区域：基础压缩指标 ===
    ObjectDelete(0, VOLATILITY_LABEL);
    ObjectDelete(0, BB_WIDTH_LABEL);
    ObjectDelete(0, ATR_LABEL);
    ObjectDelete(0, VOLUME_LABEL);
    ObjectDelete(0, PATTERN_LABEL);
    ObjectDelete(0, STATE_LABEL);
    ObjectDelete(0, COMPRESSION_METER);
    ObjectDelete(0, COMPRESSION_METER + "_Fill");

    // === 清理第二区域：多时间框架分析 ===
    if(EnableMultiTimeframe) {
        ObjectDelete(0, H1_LABEL);
        ObjectDelete(0, H4_LABEL);
        ObjectDelete(0, D1_LABEL);
        ObjectDelete(0, W1_LABEL);
        ObjectDelete(0, MN_LABEL);
        ObjectDelete(0, CONSISTENCY_LABEL);
        ObjectDelete(0, BREAKOUT_PROB_LABEL);
        ObjectDelete(0, BREAKOUT_METER);
        ObjectDelete(0, BREAKOUT_METER + "_Fill");
    }

    // === 清理第三区域：趋势分析 ===
    ObjectDelete(0, TREND_DIRECTION_LABEL);
    ObjectDelete(0, TREND_STRENGTH_LABEL);
    ObjectDelete(0, TREND_SCORE_LABEL);
    ObjectDelete(0, TREND_METER);
    ObjectDelete(0, TREND_METER + "_Fill");
    ObjectDelete(0, EXPECTED_DIRECTION_LABEL);
    ObjectDelete(0, TREND_CONSECUTIVE_LABEL);
    ObjectDelete(0, MOMENTUM_SCORE_LABEL);
    ObjectDelete(0, VOLUME_CONFIRM_LABEL);
    ObjectDelete(0, RETRACEMENT_LABEL);

    // === 清理第四区域：市场状态 ===
    ObjectDelete(0, MARKET_STATE_LABEL);
    ObjectDelete(0, "DynamicParamsLabel");

    // === 清理Deepseek优化显示 ===
    ObjectDelete(0, "TrendQualityLabel");
    ObjectDelete(0, "FibRetracementLabel");
    ObjectDelete(0, "MTFAlignLabel");
    ObjectDelete(0, "TrendFilterLabel");

    // === 清理趋势度量表刻度标签 ===
    ObjectDelete(0, "TrendMeterLabel0");
    ObjectDelete(0, "TrendMeterLabel50");
    ObjectDelete(0, "TrendMeterLabel100");

    // === 清理压缩度量表刻度标签 ===
    ObjectDelete(0, "CompressionMeterLabel0");
    ObjectDelete(0, "CompressionMeterLabel50");
    ObjectDelete(0, "CompressionMeterLabel100");

    // === 清理爆发概率度量表刻度标签 ===
    ObjectDelete(0, "BreakoutMeterLabel0");
    ObjectDelete(0, "BreakoutMeterLabel50");
    ObjectDelete(0, "BreakoutMeterLabel100");

    // === 清理Deepseek增强的阈值标记 ===
    CleanupThresholdMarks(TREND_METER);
    CleanupThresholdMarks(COMPRESSION_METER);
    CleanupThresholdMarks(BREAKOUT_METER);

    // === 清理区域背景框 ===
    ObjectDelete(0, "Zone1_BG");
    ObjectDelete(0, "Zone2_BG");
    ObjectDelete(0, "Zone3_BG");
    ObjectDelete(0, "Zone4_BG");
    ObjectDelete(0, "Zone1_Title");
    ObjectDelete(0, "Zone2_Title");
    ObjectDelete(0, "Zone3_Title");
    ObjectDelete(0, "Zone4_Title");

    // 重置全局状态变量
    g_initialized = false;
    g_CurrentMarketState = MARKET_RANGING;
    g_LastMarketState = MARKET_RANGING;

    // 打印退出信息
    Print("压缩检测EA已停止，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick() {
    // 如果EA未初始化，则不执行任何操作
    if(!g_initialized) return;

    // 获取当前时间框架的最新数据
    if(!RefreshRates()) return;

    // 分析市场状态并更新UI
    AnalyzeMarketState();
    ShowMarketStateInUI();

    // 更新压缩指标显示
    UpdateCompressionDisplay();

    // 如果启用了多时间框架分析，则更新多时间框架分析
    if(EnableMultiTimeframe) {
        UpdateMultiTimeframeAnalysis();
    }

    // 更新趋势分析显示
    UpdateTrendDisplay();

    // 检查是否有爆发信号
    if(IsBreakoutSignal()) {
        // 如果有爆发信号，则发送通知
        string message = Symbol() + " " + EnumToString(Period()) + " 检测到爆发信号！";
        if(EnableAlerts) Alert(message);
        if(EnableNotifications) SendNotification(message);
        if(EnableEmails) SendMail("MT5压缩检测EA爆发信号", message);
    }
}

//+------------------------------------------------------------------+
//| Timer function                                                    |
//+------------------------------------------------------------------+
void OnTimer() {
    // 如果EA未初始化，则不执行任何操作
    if(!g_initialized) return;

    // 更新压缩指标显示
    UpdateCompressionDisplay();

    // 如果启用了多时间框架分析，则更新多时间框架分析
    if(EnableMultiTimeframe) {
        UpdateMultiTimeframeAnalysis();
    }

    // 更新趋势分析显示
    UpdateTrendDisplay();

    // 分析市场状态并更新UI
    AnalyzeMarketState();
    ShowMarketStateInUI();
}