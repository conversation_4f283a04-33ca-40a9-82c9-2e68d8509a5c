//+------------------------------------------------------------------+
//|                                                       Guapit.mq5 |
//|                                           Copyright 2023, Guapit |
//|                                          https://www.guapit.com/ |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, Guapit"
#property link      "https://www.guapit.com/"
#property version   "1.00"

class TradeRequest
{
  public:
    TradeRequest(void);
    ~TradeRequest(void);

    // --- 获取成员属性
    ulong getID(void) const { return m_id; }
    ulong getOrderID(void) const { return m_order_id; }
    ulong getByID(void) const {return m_by_id; }
    ulong getMagic(void) const {return m_magic; }
    ulong getDev(void) const { return m_dev; }
    string getSymbol(void) const { return m_symbol; }
    string getComment(void) const { return m_comment; }
    int getType(void) const { return m_type; }
    double getPrice(void) const {return m_price; }
    double getPriceLimit(void) const {return m_stop_limit; }
    double getVolume(void) const { return m_volume; }
    double getSL(void) const { return m_sl; }
    double getTP(void) const { return m_tp;}
    ENUM_ORDER_TYPE_FILLING getTypeFilling(void) const { return m_type_filling; }
    ENUM_ORDER_TYPE_TIME getTypeTime(void) const { return m_type_time; }
    datetime getExp(void) const { return m_exp; }
    ulong getOpenDev(void)const {return m_dev; }
    ulong getCloseDev(void)const {return m_close_dev; }
    // 总配置
    bool getAsync(void) const { return m_async; }
    // 获取交易品种相关
    double getPoint(void) const { return SymbolInfoDouble(m_symbol, SYMBOL_POINT); }
    double getAsk(void) const { return SymbolInfoDouble(m_symbol, SYMBOL_ASK); }
    double getBid(void) const { return SymbolInfoDouble(m_symbol, SYMBOL_BID); }
    int getDigits(void) const { return (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS); }
    int getStopLevel(void) const {return (int)SymbolInfoInteger(m_symbol, SYMBOL_TRADE_STOPS_LEVEL); }

    //--- 设置成员属性
    // 设置交易品种
    void setSymbol(const string symbol_name) { m_symbol = symbol_name; }
    // 设置订单类型
    void setType(const int type) {m_type = type; }
    // 设置EA识别码
    void setMagic(const ulong magic) {m_magic = magic; }
    // 设置下单或者挂单价格
    void setPrice(const double price) { m_price = price; }
    // 设置挂单中的 stop_limit的 突破后后抄底的价格
    void setPriceLimit(const double price_limit) { m_stop_limit = price_limit; }
    // 设置下单手数
    void setVolume(const double volume) { m_volume = volume; }
    // 设置止损价格
    void setSL(const double sl) { m_sl = sl; }
    // 设置止盈价格
    void setTP(const double tp) {m_tp = tp; }
    // 设置滑点
    void setDev(const ulong dev) { m_dev = dev; }
    // 设置撮合交易类型
    void setTypeFilling(const ENUM_ORDER_TYPE_FILLING type_filling) {m_type_filling = type_filling; }
    // 设置挂单到期时间类型
    void setTypeTime(const ENUM_ORDER_TYPE_TIME type_time) { m_type_time = type_time;}
    // 设置挂单到期时间
    void setExp(const datetime exp_time) { m_exp = exp_time; }
    // 设置持仓单号
    void setID(const ulong id) { m_id = id; }
    // 设置挂单单号
    void setOrderID(const ulong order_id) { m_order_id = order_id; }
    // 设置持仓单锁仓单号
    void setByID(const ulong by_id) { m_by_id = by_id; }
    // 设置注释
    void setComment(const string comment) { m_comment = comment; }
    // 设置开仓滑点
    void setOpenDev(const ulong dev);
    // 设置平仓滑点
    void setCloseDev(const ulong dev);  

    // 是否开启异步
    void setAsync(const bool is_async) { m_async = is_async; }
    // 是否打印交易订单信息, 0: 有错误时打印, 1 成功或者失败打印, -1: 成功或者失败都不打印
    void setPrint(const int print_level) { m_print = print_level; }
    // 是否开启错误日志
    void setLogs(const bool logs_level) { m_logs = logs_level; }
    // 是否开启数据校验
    void setCheck(const bool is_check) { m_check = is_check; }
    // 语言版本
    void setLanguage(const string language) { m_language = language; }
    //--- 核心方法
    // 初始化
    void Init();
    // 发送交易请求
    bool baseSend(void);
    // 发送交易请求基础组件
    bool _baseSend(MqlTradeRequest &Request, MqlTradeResult &Result);
   // 发送修改请求基础组件
   bool baseUpdate();
   // 发送平仓请求基础组件
   bool baseClose();
   // 检验订单
   bool CheckSand();
   // 打印交易请求数据
   void PrintRequest(MqlTradeRequest &Request);
   // 打印交易成功或者失败请求数据
   void PrintResult(MqlTradeRequest &Request, MqlTradeResult &Result);
   //--- 挂单操作
   bool baseOrderSend(void);
   bool baseOrdrUpdate(void);
   bool baseOrderDelete(void);
   protected:
      

   private:
      ENUM_TRADE_REQUEST_ACTIONS m_action; // 请求类型
      ulong m_id; // 持仓单号
      ulong m_by_id; // 持仓锁仓单
      ulong m_order_id; // 挂单号
      ulong m_magic; // 识别码
      string m_symbol; // 交易品种
      string m_comment; // 注释
      int m_type; // 订单类型
      double m_volume; // 手数
      double m_price; // 价格
      double m_sl; // 止损价格
      double m_tp; // 止盈价格
      double m_stop_limit; // 挂单抄底价位 stop_limit的抄底单
      ulong m_dev; // 滑点值
      ulong m_close_dev;// 平仓滑点值
      ENUM_ORDER_TYPE_FILLING m_type_filling; // 交易撮合模式
      ENUM_ORDER_TYPE_TIME m_type_time; // 挂单到期类型
      datetime m_exp; // 挂单到期时间
      // 货币相关信息
      double m_ask; // ask
      double m_bid; // bid
      double m_point; // 最小报价单位
      int m_digits; // 小数点位数
      int m_stop_level; // 最小间隔
      
      bool m_async; // 是否异步请求
      int m_print;
      bool m_logs; // 日志登记
      bool m_check; // 是否检查交易发送数据是否正常
      string m_language; // 语言版本

      MqlTradeRequest m_request;
      MqlTradeResult m_result;
};

TradeRequest::TradeRequest(void)
{
   m_symbol = _Symbol;
   m_magic = 8199231;
   m_volume = 0.01;
   m_type = 0;
   m_sl = 0.0;
   m_tp = 0.0;
   m_dev = 0;
   m_close_dev = 0;
   m_type_filling = ORDER_FILLING_IOC;
   m_type_time = ORDER_TIME_SPECIFIED;     
   m_language = "cn";
   m_async = false;
}
TradeRequest::~TradeRequest(void)
{
    
}

// 发送交易请求
bool TradeRequest::_baseSend(MqlTradeRequest &Request, MqlTradeResult &Result)
{
   // 重置错误代码
   ResetLastError();
   // 是否检查请求数据
   if(m_check)
   {
      if(!CheckSand())
         return false;
   }
   bool is_send;
   // 是否启动异步请求
   if(m_async)
   {
      is_send = OrderSendAsync(Request,Result);
   }
   else
   {
      is_send = OrderSend(Request,Result);
   }
   // 日志等级
   if(m_print == 0)
   {
      if(!is_send)
         printf("发送交易请求失败,错误代码: %d", GetLastError());
   }
   else if(m_print == 1)
   {
      PrintRequest(Request);
      if(!is_send)
         printf("发送交易请求失败,错误代码: %d", GetLastError());
   }
   else if(m_print == -1)
   {
      return is_send;
   }
   return is_send;
}

void TradeRequest::PrintRequest(MqlTradeRequest &Request)
{
   string str = "";
   if(m_language == "cn")
   {
      if(Request.action == TRADE_ACTION_DEAL || Request.action == TRADE_ACTION_SLTP)
      {
         str = "请求类型: %s \n持仓单号: %d \n交易品种: %s \n订单类型: %d \n手数: %g \n" +
               "价格: %g \n止损价: %g \n止盈价: %g \n滑点值: %d \n" +
               "注释: %s \n识别码: %d \n";
         StringFormat(str,Request.action,
                     Request.position,
                     Request.symbol,
                     EnumToString(Request.type),
                     Request.volume,
                     Request.price,
                     Request.sl,
                     Request.tp,
                     Request.deviation,
                     Request.comment,
                     Request.magic
                     );  
      }
      else if(Request.action == TRADE_ACTION_PENDING ||
              Request.action == TRADE_ACTION_MODIFY ||
              Request.action == TRADE_ACTION_REMOVE)
      {
         str = "请求类型: %s \n挂单单号: %d \n交易品种: %s \n订单类型: %d \n手数: %g \n" +
               "挂单价格: %g \n突破抄底价: %g \n止损价: %g \n止盈价: %g \n滑点值: %d \n" +
               "到期类型: %s \n到期时间: %s \n注释: %s \n识别码: %d \n";
         StringFormat(str,Request.action,
                     Request.order,
                     Request.symbol,
                     EnumToString(m_request.type),
                     Request.volume,
                     Request.price,
                     Request.stoplimit,
                     Request.sl,
                     Request.tp,
                     Request.deviation,
                     EnumToString(Request.type_time),
                     (string)Request.expiration,
                     Request.comment,
                     Request.magic
                     );  
      }
      
      
   }
   else if(m_language == "en")
   {
      if(Request.action == TRADE_ACTION_DEAL || Request.action == TRADE_ACTION_SLTP)
      {
         str = "action: %s \nposition: %d \nsymbol: %s \ntype: %d \nvolume: %g \n" +
               "price: %g \nsl: %g \ntp: %g \ndeviation: %d \n" +
               "comment: %s \nmagic: %d \n";
         StringFormat(str,Request.action,
                     Request.position,
                     Request.symbol,
                     EnumToString(Request.type),
                     Request.volume,
                     Request.price,
                     Request.sl,
                     Request.tp,
                     Request.deviation,
                     Request.comment,
                     Request.magic
                     );  
      }
      else if(Request.action == TRADE_ACTION_PENDING ||
              Request.action == TRADE_ACTION_MODIFY ||
              Request.action == TRADE_ACTION_REMOVE)
      {
         str = "action: %s \norder: %d \nsymbol: %s \ntype: %d \nvolume: %g \n" +
               "price: %g \nstoplimit: %g \nsl: %g \ntp: %g \ndeviation: %d \n" +
               "type_time: %s \nexpiration: %s \ncomment: %s \nmagic: %d \n";
         StringFormat(str,Request.action,
                     Request.order,
                     Request.symbol,
                     EnumToString(Request.type),
                     Request.volume,
                     Request.price,
                     Request.stoplimit,
                     Request.sl,
                     Request.tp,
                     Request.deviation,
                     EnumToString(Request.type_time),
                     (string)Request.expiration,
                     Request.comment,
                     Request.magic
                     );  
      }
   }
   printf(str);
}

void TradeRequest::PrintResult(MqlTradeRequest &Request, MqlTradeResult &Result)
{
   string str = "";
   if(m_language == "cn")
   {
      if(Request.action == TRADE_ACTION_DEAL || Request.action == TRADE_ACTION_SLTP)
      {
         str = "状态码: %d \n交易号: %d \n 交易价格: %g \n交易手数: %g \n市场买入价: %g \n" +
               "市场卖出价: %g \n交易注释: %s \n服务器请求ID: %d \n交易错误码: %d";
         StringFormat(str,Result.retcode,
                     Result.deal,
                     Result.price,
                     Result.volume,
                     Result.ask,
                     Result.bid,
                     Result.comment,
                     Result.request_id,
                     Result.retcode_external); 
      }
      else if(m_request.action == TRADE_ACTION_PENDING ||
              m_request.action == TRADE_ACTION_MODIFY ||
              m_request.action == TRADE_ACTION_REMOVE)
      {
         str = "状态码: %d \n交易挂单号: %d \n 交易价格: %g \n交易手数: %g \n市场买入价: %g \n" +
               "市场卖出价: %g \n交易注释: %s \n服务器请求ID: %d \n交易错误码: %d";
         StringFormat(str,Result.retcode,
                     Result.order,
                     Result.price,
                     Result.volume,
                     Result.ask,
                     Result.bid,
                     Result.comment,
                     Result.request_id,
                     Result.retcode_external);
      }
      
   }
   else if (m_language == "en")
   {
      if(Request.action == TRADE_ACTION_DEAL || Request.action == TRADE_ACTION_SLTP)
      {
         str = "retcode: %d \ndeal: %d \nprice: %g \nvolume: %g \nask: %g \n" +
               "bid: %g \ncomment: %s \nrequest_id: %d \nretcode_external: %d";
         StringFormat(str,Result.retcode,
                     Result.deal,
                     Result.price,
                     Result.volume,
                     Result.ask,
                     Result.bid,
                     Result.comment,
                     Result.request_id,
                     Result.retcode_external); 
      }
      else if(m_request.action == TRADE_ACTION_PENDING ||
              m_request.action == TRADE_ACTION_MODIFY ||
              m_request.action == TRADE_ACTION_REMOVE)
      {
         str = "retcode: %d \norder: %d \nprice: %g \nvolume: %g \nask: %g \n" +
               "bid: %g \ncomment: %s \nrequest_id: %d \nretcode_external: %d";
         StringFormat(str,Result.retcode,
                     Result.order,
                     Result.price,
                     Result.volume,
                     Result.ask,
                     Result.bid,
                     Result.comment,
                     Result.request_id,
                     Result.retcode_external);
      }
   }

   printf(str);
   
}

bool TradeRequest::CheckSand()
{

   if(m_language == "cn" && m_request.action == TRADE_ACTION_DEAL)
   {
      if(m_request.action < TRADE_ACTION_DEAL || 
      m_request.action > TRADE_ACTION_CLOSE_BY)
      {  
         printf("交易请求类型(action)错误, 请查询帮助手册中的 ENUM_TRADE_REQUEST_ACTIONS 枚举类型,当前值: %d", m_request.action);
         return false;
      }
      if (m_request.magic < 0)
      {
         printf("识别码(magic)错误, 识别码必须在ulong的整数范围以内, 当前值: %d", m_request.magic);
         return false;
      }
      if(!SymbolSelect(m_request.symbol,true))
      {
          printf("交易品种(symbol)错误, 没有找到指定交易品种, 当前品种: %s", m_request.symbol);
          return false;
      }
      // 判断价格是否合法
      if(m_request.price < 0)
      {
         printf("交易价格(price)错误, 价格低于0.0, 当前价格: %g", m_request.price);
         return false;
      }
      // 如果为突破回踩挂单
      if(m_request.type == ORDER_TYPE_BUY_STOP_LIMIT || m_request.type == ORDER_TYPE_SELL_STOP_LIMIT){
         if(m_request.stoplimit > 0)
         {
            if(m_request.type == ORDER_TYPE_BUY_STOP_LIMIT && m_request.stoplimit > m_request.price)
            {
               printf("交易回踩价格(stop limit)错误, 回踩价格高于了开仓价格,当前挂单(Buy Stop Limit)价格: %g, 回踩价: %g", m_request.price,m_request.stoplimit);
               return false;
            }
            else if(m_request.type == ORDER_TYPE_SELL_STOP_LIMIT && m_request.stoplimit < m_request.price)
            {
               printf("交易回踩价格(stop limit)错误, 回踩价格低于了开仓价格,当前挂单(Sell Stop Limit)价格: %g, 回踩价: %g", m_request.price,m_request.stoplimit);
               return false;
            }
               
         }
         else if(m_request.stoplimit <= 0)
         {
            printf("交易回踩价格(stop limit)错误, 回踩价格不能低于0.0,当前回踩价: %g", m_request.stoplimit);
            return false;
         }
      }
      
      // 下单手数
      if(m_request.volume > 0)
      {
         double m_min = SymbolInfoDouble(m_request.symbol,SYMBOL_VOLUME_MIN);
         double m_max = SymbolInfoDouble(m_request.symbol,SYMBOL_VOLUME_MAX);
         if(m_request.volume < m_min || m_request.volume >m_max)
         {
            printf("交易手数(volume)错误, 交易手数超出范围(%g ~ %g),并且必须保持2位数小数点,当前手数: %g",m_min,m_max, m_request.volume);
            return false;
         }
      }
      else
      {
         double m_min = SymbolInfoDouble(m_request.symbol,SYMBOL_VOLUME_MIN);
         printf("交易手数(volume)错误, 交易手数不能低于%g,并且必须保持2位数小数点,当前手数: %g",m_min, m_request.volume);
         return false;
      }
      if(m_request.sl > 0)
      {
         if(m_request.type == ORDER_TYPE_BUY && m_request.sl > m_request.price){
            printf("交易止损价(sl)错误, 止损价格高于了开仓价格,当前开仓(Buy)价格: %g, 止损价: %g", m_request.price,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL && m_request.sl < m_request.price){
            printf("交易止损价(sl)错误, 止损价格应该低于了开仓价格,当前开仓(Sell)价格: %g, 止损价: %g", m_request.price,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_BUY_LIMIT && m_request.sl > m_request.price){
            printf("交易止损价(sl)错误, 止损价格应该高于了挂单价格,当前挂单(Buy Limit)价格: %g, 止损价: %g", m_request.price,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL_LIMIT && m_request.sl < m_request.price){
            printf("交易止损价(sl)错误, 止损价格应该低于了挂单价格,当前挂单(Sell Limit)价格: %g 止损价: %g", m_request.price,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_BUY_STOP && m_request.sl > m_request.price){
            printf("交易止损价(sl)错误, 止损价格应该高于了挂单价格,当前挂单(Buy Stop)价格: %g, 止损价: %g", m_request.price,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL_STOP && m_request.sl < m_request.price){
            printf("交易止损价(sl)错误, 止损价格应该低于了挂单价格,当前挂单(Sell Stop)价格: %g, 止损价: %g", m_request.price,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_BUY_STOP_LIMIT  && m_request.sl > m_request.stoplimit){
            printf("交易止损价(sl)错误, 止损价格高于了挂单价格,当前挂单(Buy Stop Limit)价格: %g, 止损价: %g", m_request.stoplimit,m_request.sl);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL_STOP_LIMIT  && m_request.sl < m_request.stoplimit){
            printf("交易止损价(sl)错误, 止损价格低于了挂单价格,当前挂单(Sell Stop Limit)价格: %g, 止损价: %g", m_request.stoplimit,m_request.sl);
            return false;
         }
            
      }
      else if(m_request.sl < 0){
         printf("交易止损价(sl)错误, 止损价格不能低于0.0以下,当前止损价: %g", m_request.sl);
         return false;
      }
         
      if(m_request.tp > 0)
      {
         if(m_request.type == ORDER_TYPE_BUY && m_request.tp < m_request.price){
            printf("交易止盈价(tp)错误, 止盈价格高于了开仓价格,当前开仓(Buy)价格: %g, 止盈价: %g", m_request.price,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL && m_request.tp > m_request.price){
            printf("交易止盈价(tp)错误, 止盈价格应该低于了开仓价格,当前开仓(Sell)价格: %g, 止盈价: %g", m_request.price,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_BUY_LIMIT && m_request.tp < m_request.price){
            printf("交易止盈价(tp)错误, 止盈价格应该高于了挂单价格,当前挂单(Buy Limit)价格: %g, 止盈价: %g", m_request.price,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL_LIMIT && m_request.tp > m_request.price){
            printf("交易止盈价(tp)错误, 止盈价格应该低于了挂单价格,当前挂单(Sell Limit)价格: %g 止盈价: %g", m_request.price,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_BUY_STOP && m_request.tp < m_request.price){
            printf("交易止盈价(tp)错误, 止盈价格应该高于了挂单价格,当前挂单(Buy Stop)价格: %g, 止盈价: %g", m_request.price,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL_STOP && m_request.tp > m_request.price){
            printf("交易止盈价(tp)错误, 止盈价格应该低于了挂单价格,当前挂单(Sell Stop)价格: %g, 止盈价: %g", m_request.price,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_BUY_STOP_LIMIT  && m_request.tp < m_request.stoplimit){
            printf("交易止盈价(tp)错误, 止盈价格高于了挂单价格,当前挂单(Buy Stop Limit)价格: %g, 止盈价: %g", m_request.stoplimit,m_request.tp);
            return false;
         }
            
         else if(m_request.type == ORDER_TYPE_SELL_STOP_LIMIT  && m_request.tp > m_request.stoplimit){
            printf("交易止盈价(tp)错误, 止盈价格低于了挂单价格,当前挂单(Sell Stop Limit)价格: %g, 止盈价: %g", m_request.stoplimit,m_request.tp);
            return false;
         }
            
      }
      else if(m_request.tp < 0){
         printf("交易止盈价(tp)错误, 止损价格不能低于0.0以下,当前止盈价: %g", m_request.tp);
         return false;
      }
      
   }

   return true;
}

// 发送交易请求
bool TradeRequest::baseSend()
{
   ZeroMemory(m_request);
   ZeroMemory(m_result);

   m_request.action = TRADE_ACTION_DEAL;
   if(m_type == 0)
   {
      m_request.type = ORDER_TYPE_BUY;
      m_request.price = getAsk();
   }
   else if(m_type == 1)
   {
      m_request.type = ORDER_TYPE_SELL;
      m_request.price = getBid();
   }
   m_request.type_filling = m_type_filling;
   
   m_request.symbol = m_symbol;
   m_request.comment = m_comment;
   m_request.magic = m_magic;
   
   m_request.volume = NormalizeDouble(m_volume,2);
   
   m_request.sl = NormalizeDouble(m_sl,Digits());
   m_request.tp = NormalizeDouble(m_tp,Digits());
   m_request.deviation = m_dev;

   return _baseSend(m_request,m_result);
}

// 修改持仓单
bool TradeRequest::baseUpdate()
{
   ZeroMemory(m_request);
   ZeroMemory(m_result);
   m_request.action = TRADE_ACTION_SLTP;
   m_request.position = m_id;
   m_request.symbol = m_symbol;
   m_request.sl = NormalizeDouble(m_sl,Digits());
   m_request.tp = NormalizeDouble(m_tp,Digits());
   m_request.magic = m_magic;
   //m_request.type_filling = m_filling;
   return _baseSend(m_request,m_result);
}
// 平仓持仓单
bool TradeRequest::baseClose(void)
{
   ZeroMemory(m_request);
   ZeroMemory(m_result);
   m_request.action = TRADE_ACTION_DEAL;
   m_request.position = m_id;
   m_request.symbol = m_symbol;
   m_request.volume = m_volume;
   m_request.deviation = m_close_dev;
   m_request.type_filling = m_type_filling;
   if(m_type == 0){
      m_request.type = ORDER_TYPE_SELL;
      m_request.price = getAsk();
   }
   else if(m_type == 1){
      m_request.type = ORDER_TYPE_BUY;
      m_request.price = getBid();
   }
   
   return _baseSend(m_request,m_result);
}

// 挂单操作
bool TradeRequest::baseOrderSend()
{

   ZeroMemory(m_request);
   ZeroMemory(m_result);
   m_request.action = TRADE_ACTION_PENDING;
   m_request.type = (ENUM_ORDER_TYPE)m_type;
   m_request.type_filling = m_type_filling;
   
   m_request.symbol = m_symbol;
   m_request.comment = m_comment;
   m_request.magic = m_magic;
   
   m_request.price = m_price;
   m_request.volume = m_volume;
   m_request.sl = NormalizeDouble(m_sl, m_digits);
   m_request.tp = NormalizeDouble(m_tp, m_digits);
   m_request.expiration = m_exp;
   m_request.stoplimit = m_stop_level;
   m_request.deviation = m_dev;

   return _baseSend(m_request,m_result);  
}

// 修改挂单
bool TradeRequest::baseOrdrUpdate()
{
   ZeroMemory(m_request);
   ZeroMemory(m_result);
   m_request.action = TRADE_ACTION_MODIFY;
   m_request.order = m_order_id;
   m_request.symbol = m_symbol;
   
   m_request.price = m_price;
   m_request.sl = NormalizeDouble(m_sl, m_digits);
   m_request.tp = NormalizeDouble(m_tp, m_digits);
   m_request.expiration = m_exp;
   m_request.stoplimit = m_stop_limit;
   m_request.deviation = m_dev;
   
   return _baseSend(m_request,m_result); 
}

bool TradeRequest::baseOrderDelete(void)
{
   ZeroMemory(m_request);
   ZeroMemory(m_result);
   m_request.action = TRADE_ACTION_REMOVE;
   m_request.order = m_order_id;
   
   return _baseSend(m_request,m_result); 
}

