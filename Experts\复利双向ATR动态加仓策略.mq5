//+------------------------------------------------------------------+
//|                                     复利双向ATR动态加仓策略.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\AccountInfo.mqh>

// 包含函数文件
#include "复利双向ATR动态加仓策略_functions.mq5"

// 全局变量
CTrade         Trade;          // 交易对象
CPositionInfo  PositionInfo;   // 持仓信息对象
CSymbolInfo    SymbolInfo;     // 品种信息对象
CAccountInfo   AccountInfo;    // 账户信息对象

// 订单管理数组
struct OrderData
{
   ulong    ticket;           // 订单号
   datetime open_time;        // 开仓时间
   double   open_price;       // 开仓价格
   double   lot;              // 手数
   double   sl;               // 止损价格
   double   tp;               // 止盈价格
   int      direction;        // 方向 (1=多单, -1=空单)
   int      level;            // 加仓层级
   bool     is_revived;       // 是否为复活订单
   int      revival_count;    // 复活次数
   datetime last_revival_time;// 上次复活时间
   string   comment;          // 订单注释
};

OrderData long_orders[];      // 多单数据数组
OrderData short_orders[];     // 空单数据数组

//--- 资金管理参数
enum MONEY_MANAGEMENT_MODE
{
   MODE_COMPOUND,            // 复利模式
   MODE_FIXED                 // 固定模式
};

input group "===== 资金管理设置 ====="
input MONEY_MANAGEMENT_MODE InpMoneyManagementMode = MODE_COMPOUND; // 资金管理模式
input double InpEquityPerLot = 5000.0;                           // 每多少净值建仓0.01手
input double InpMaxLotDivisor = 0.01;                            // 基础仓位(手)
input double InpMaxRiskPercent = 10.0;                           // 最大总仓位风险比例(%)
input double InpEquityStopLossPercent = 10.0;                    // 账户净值亏损平仓比例(%)

//--- 加仓参数
input group "===== 加仓设置 ====="
input int    InpMaxLevels = 15;                                  // 最大加仓层数
input double InpATRMultiplier = 5.0;                             // ATR加仓间距系数
input double InpFirstLevelMultiplier = 2.0;                      // 首次加仓间距系数(相对于普通间距)
input double InpInitialLotMultiplier = 1.5;                      // 初始加仓倍数(1-5层)
input double InpMidLotMultiplier = 1.3;                          // 中期加仓倍数(6-10层)
input double InpLateLotMultiplier = 1.2;                         // 后期加仓倍数(11-15层)
input double InpFinalLotMultiplier = 1.1;                        // 最终加仓倍数(>15层)

//--- 止损设置
enum STOP_LOSS_MODE
{
   SL_ATR,                   // ATR动态止损
   SL_FIXED                  // 固定点数止损
};

input group "===== 止损设置 ====="
input STOP_LOSS_MODE InpStopLossMode = SL_ATR;                   // 止损模式
input double InpATRStopMultiplier = 2.0;                         // ATR止损倍数
input int    InpFixedStopLoss = 400;                             // 固定止损点数
input bool   InpUseBreakEven = true;                             // 启用移动保本止损
input int    InpBreakEvenStart = 300;                            // 移动保本启动点数
input int    InpBreakEvenProfit = 50;                            // 保本后额外获利点数

//--- 止盈设置
input group "===== 止盈设置 ====="
input double InpBaseProfitTarget = 2000.0;                       // 基础目标盈利点数
input bool   InpUseDynamicDrawdown = true;                       // 启用动态回撤止盈
input double InpDrawdownPercent1 = 10.0;                         // 第一阶段回撤比例(%)
input double InpDrawdownPercent2 = 8.0;                          // 第二阶段回撤比例(%)
input double InpDrawdownPercent3 = 5.0;                          // 第三阶段回撤比例(%)
input double InpPartialClosePercent1 = 30.0;                     // 第一阶段平仓比例(%)
input double InpPartialClosePercent2 = 20.0;                     // 第二阶段平仓比例(%)
input double InpPartialClosePercent3 = 30.0;                     // 第三阶段平仓比例(%)

//--- 指标参数
input group "===== 指标设置 ====="
input int    InpATRPeriod = 14;                                  // ATR周期
input int    InpRSIPeriod = 14;                                  // RSI周期
input int    InpADXPeriod = 14;                                  // ADX周期

//--- 全局变量
int handle_atr;                // ATR指标句柄
int handle_rsi;                // RSI指标句柄
int handle_adx;                // ADX指标句柄
double current_atr;            // 当前ATR值
double current_rsi;            // 当前RSI值
double current_adx;            // 当前ADX值
int total_long_orders;         // 当前多单总数
int total_short_orders;        // 当前空单总数
double total_long_lot;         // 当前多单总手数
double total_short_lot;        // 当前空单总手数
double account_equity;         // 账户净值
double account_balance;        // 账户余额
double account_profit;         // 当前浮动盈亏
double initial_equity;         // 初始净值
double point_value;            // 点值
double current_spread;         // 当前点差

// 止盈管理变量
double max_long_profit;        // 多单最大盈利
double max_short_profit;       // 空单最大盈利
bool long_first_close_done;    // 多单第一次平仓完成
bool long_second_close_done;   // 多单第二次平仓完成
bool long_third_close_done;    // 多单第三次平仓完成
bool short_first_close_done;   // 空单第一次平仓完成
bool short_second_close_done;  // 空单第二次平仓完成
bool short_third_close_done;   // 空单第三次平仓完成

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化交易对象
   m_trade.SetDeviationInPoints(INT_MAX);
   m_trade.SetAsyncMode(true);
   m_trade.SetMarginMode();
   m_trade.LogLevel(LOG_LEVEL_ERRORS);
   
   // 初始化品种信息
   SymbolInfo.Name(Symbol());
   point_value = SymbolInfo.Point();
   
   // 创建指标句柄
   handle_atr = iATR(Symbol(), PERIOD_CURRENT, InpATRPeriod);
   handle_rsi = iRSI(Symbol(), PERIOD_CURRENT, InpRSIPeriod, PRICE_CLOSE);
   handle_adx = iADX(Symbol(), PERIOD_CURRENT, InpADXPeriod);
   
   if(handle_atr == INVALID_HANDLE || handle_rsi == INVALID_HANDLE || handle_adx == INVALID_HANDLE)
   {
      Print("指标句柄创建失败!");
      return INIT_FAILED;
   }
   
   // 初始化订单数组
   ArrayResize(long_orders, 0);
   ArrayResize(short_orders, 0);
   LoadExistingPositions();
   
   // 初始化净值
   initial_equity = AccountInfo.Equity();
   
   // 初始化止盈管理变量
   max_long_profit = 0;
   max_short_profit = 0;
   long_first_close_done = false;
   long_second_close_done = false;
   long_third_close_done = false;
   short_first_close_done = false;
   short_second_close_done = false;
   short_third_close_done = false;
   
   // 显示信息面板
   CreateInfoPanel();
   
   // 创建交易按钮
   btn = new Button();
   btn.Create("gp_button_01",1750,410);
   btn.Size(100,32);
   btn.Text("Buy");
   btn.Font("Arial",12);
   btn.Color(clrWhite);
   btn.BGColor(clrRoyalBlue);
   btn.BorderColor(clrDimGray);
   btn.State(false);
   btn.Update();
   
   btn2 = new Button();
   btn2.Create("gp_button_02",1750,450);
   btn2.Size(100,32);
   btn2.Text("Sell");
   btn2.Font("Arial",12);
   btn2.Color(clrWhite);
   btn2.BGColor(clrCrimson);
   btn2.BorderColor(clrDimGray);
   btn2.State(false);
   btn2.Update();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   if(handle_atr != INVALID_HANDLE)
      IndicatorRelease(handle_atr);
      
   if(handle_rsi != INVALID_HANDLE)
      IndicatorRelease(handle_rsi);
      
   if(handle_adx != INVALID_HANDLE)
      IndicatorRelease(handle_adx);
      
   // 删除信息面板
   ObjectsDeleteAll(0, "InfoPanel_");
   
   // 删除按钮
   delete btn;
   delete btn2;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新市场数据
   if(!UpdateMarketData())
      return;
      
   // 检查账户风险
   if(CheckAccountRisk())
      return;
      
   // 更新订单状态
   UpdateOrdersStatus();
   
   // 更新移动止损
   if(InpUseBreakEven)
      UpdateBreakEven();
      
   // 更新信息面板
   UpdateInfoPanel();
}

//+------------------------------------------------------------------+
//| 更新市场数据                                                     |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
   // 获取账户信息
   account_equity = AccountInfo.Equity();
   account_balance = AccountInfo.Balance();
   account_profit = AccountInfo.Profit();
   
   // 获取点差
   current_spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * SymbolInfo.Point();
   
   // 更新指标数据
   double atr_buffer[];
   double rsi_buffer[];
   double adx_buffer[];
   
   if(CopyBuffer(handle_atr, 0, 0, 1, atr_buffer) <= 0)
      return false;
      
   if(CopyBuffer(handle_rsi, 0, 0, 1, rsi_buffer) <= 0)
      return false;
      
   if(CopyBuffer(handle_adx, 0, 0, 1, adx_buffer) <= 0)
      return false;
      
   current_atr = atr_buffer[0];
   current_rsi = rsi_buffer[0];
   current_adx = adx_buffer[0];
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查账户风险                                                     |
//+------------------------------------------------------------------+
bool CheckAccountRisk()
{
   // 检查账户净值亏损比例
   double equity_loss_percent = (initial_equity - account_equity) / initial_equity * 100.0;
   
   if(equity_loss_percent >= InpEquityStopLossPercent)
   {
      Print("账户净值亏损达到", InpEquityStopLossPercent, "%，平掉所有仓位");
      CloseAllPositions();
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| 加载现有持仓                                                     |
//+------------------------------------------------------------------+
void LoadExistingPositions()
{
   total_long_orders = 0;
   total_short_orders = 0;
   total_long_lot = 0;
   total_short_lot = 0;
   
   // 清空数组
   ArrayResize(long_orders, 0);
   ArrayResize(short_orders, 0);
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) == Symbol() && 
            PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
         {
            OrderData order;
            
            order.ticket = PositionGetInteger(POSITION_TICKET);
            order.open_time = (datetime)PositionGetInteger(POSITION_TIME);
            order.open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            order.lot = PositionGetDouble(POSITION_VOLUME);
            order.sl = PositionGetDouble(POSITION_SL);
            order.tp = PositionGetDouble(POSITION_TP);
            order.direction = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 1 : -1;
            order.comment = PositionGetString(POSITION_COMMENT);
            
            // 解析注释中的层级信息
            string comment = order.comment;
            if(StringFind(comment, "Level=") >= 0)
            {
               string level_str = StringSubstr(comment, StringFind(comment, "Level=") + 6, 2);
               order.level = (int)StringToInteger(level_str);
            }
            else
            {
               order.level = 1; // 默认为第一层
            }
            
            // 根据方向添加到相应数组
            if(order.direction > 0) // 多单
            {
               int idx = ArraySize(long_orders);
               ArrayResize(long_orders, idx + 1);
               long_orders[idx] = order;
               total_long_orders++;
               total_long_lot += order.lot;
            }
            else // 空单
            {
               int idx = ArraySize(short_orders);
               ArrayResize(short_orders, idx + 1);
               short_orders[idx] = order;
               total_short_orders++;
               total_short_lot += order.lot;
            }
         }
      }
   }
}

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

//+------------------------------------------------------------------+
//| 获取最后一个订单索引                                             |
//+------------------------------------------------------------------+
int GetLastOrderIndex(OrderData &orders[])
{
   int last_idx = -1;
   datetime last_time = 0;
   
   for(int i = 0; i < ArraySize(orders); i++)
   {
      if(orders[i].open_time > last_time)
      {
         last_time = orders[i].open_time;
         last_idx = i;
      }
   }
   
   return last_idx;
}

//+------------------------------------------------------------------+
//| 计算加仓间距                                                     |
//+------------------------------------------------------------------+
double CalculateAddDistance(int level)
{
   double base_distance = current_atr * InpATRMultiplier;
   
   // 首次加仓间距更大
   if(level == 1)
      return base_distance * InpFirstLevelMultiplier;
      
   // 趋势加速时缩短间距
   // 使用ADX指标判断趋势强度
   if(current_adx > 30) // 强趋势
      return base_distance * 0.8;
   else if(current_adx > 20) // 中等趋势
      return base_distance * 0.9;
   
   return base_distance;
}

//+------------------------------------------------------------------+
//| 格式化手数，确保符合交易品种的最小手数和步长要求                 |
//+------------------------------------------------------------------+
double FormatLots(string symbol, double lots)
{
   double a = 0;
   double minilots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double steplots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(lots < minilots)
      return(minilots);
   else
   {
      double a1 = MathFloor(lots / minilots) * minilots;
      a = a1 + MathFloor((lots - a1) / steplots) * steplots;
   }
   
   return(a);
}

//+------------------------------------------------------------------+
//| 计算加仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateAddLot(double prev_lot, int level)
{
   // 根据层级选择倍数
   double multiplier;
   
   if(level <= 5)
      multiplier = InpInitialLotMultiplier;
   else if(level <= 10)
      multiplier = InpMidLotMultiplier;
   else if(level <= 15)
      multiplier = InpLateLotMultiplier;
   else
      multiplier = InpFinalLotMultiplier;
      
   // 非线性递减公式
   double lot_multiplier = 1 + (multiplier - 1) / level;
   
   // 计算新手数
   double new_lot = NormalizeDouble(prev_lot * lot_multiplier, 2);
   
   // 检查是否超过最大手数限制
   double max_lot = NormalizeDouble(InpMaxLotDivisor * (account_equity / InpEquityPerLot), 2);
   if(new_lot > max_lot)
      new_lot = max_lot;
      
   // 使用FormatLots函数格式化手数
   new_lot = FormatLots(Symbol(), new_lot);
      
   return new_lot;
}

//+------------------------------------------------------------------+
//| 计算开仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateInitialLot()
{
   double lot;
   
   if(InpMoneyManagementMode == MODE_COMPOUND)
   {
      // 复利模式 - 新仓位=基础仓位×（当前账户余额/5000）
      lot = NormalizeDouble(InpMaxLotDivisor * (account_balance / InpEquityPerLot), 2);
   }
   else
   {
      // 固定模式
      lot = InpMaxLotDivisor; // 使用基础仓位作为固定模式手数
   }
   
   // 检查最小/最大手数限制
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   
   if(lot < min_lot) lot = min_lot;
   if(lot > max_lot) lot = max_lot;
   
   // 使用FormatLots函数格式化手数
   lot = FormatLots(Symbol(), lot);
   
   return lot;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CalculateStopLoss(double entry_price, int direction)
{
   double sl;
   
   if(InpStopLossMode == SL_ATR)
   {
      // ATR动态止损
      double sl_distance = current_atr * InpATRStopMultiplier;
      
      if(direction > 0) // 多单
         sl = entry_price - sl_distance;
      else // 空单
         sl = entry_price + sl_distance;
   }
   else
   {
      // 固定点数止损
      double sl_distance = InpFixedStopLoss * point_value;
      
      if(direction > 0) // 多单
         sl = entry_price - sl_distance;
      else // 空单
         sl = entry_price + sl_distance;
   }
   
   return NormalizeDouble(sl, SymbolInfo.Digits());
}

//+------------------------------------------------------------------+
//| 更新移动保本止损                                                 |
//+------------------------------------------------------------------+
void UpdateBreakEven()
{
   // 更新多单移动止损
   for(int i = 0; i < ArraySize(long_orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      // 计算当前盈利点数
      double profit_points = (last_tick.bid - long_orders[i].open_price) / point_value;
      
      // 检查是否达到移动保本条件
      if(profit_points >= InpBreakEvenStart)
      {
         double new_sl = long_orders[i].open_price + InpBreakEvenProfit * point_value;
         
         // 检查是否需要修改止损
         if(long_orders[i].sl < long_orders[i].open_price || long_orders[i].sl == 0)
         {
            Trade.PositionModify(long_orders[i].ticket, new_sl, long_orders[i].tp);
         }
      }
   }
   
   // 更新空单移动止损
   for(int i = 0; i < ArraySize(short_orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      // 计算当前盈利点数
      double profit_points = (short_orders[i].open_price - last_tick.ask) / point_value;
      
      // 检查是否达到移动保本条件
      if(profit_points >= InpBreakEvenStart)
      {
         double new_sl = short_orders[i].open_price - InpBreakEvenProfit * point_value;
         
         // 检查是否需要修改止损
         if(short_orders[i].sl > short_orders[i].open_price || short_orders[i].sl == 0)
         {
            Trade.PositionModify(short_orders[i].ticket, new_sl, short_orders[i].tp);
         }
      }
   }
}

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

//+------------------------------------------------------------------+
//| 获取最后一个订单索引                                             |
//+------------------------------------------------------------------+
int GetLastOrderIndex(OrderData &orders[])
{
   int last_idx = -1;
   datetime last_time = 0;
   
   for(int i = 0; i < ArraySize(orders); i++)
   {
      if(orders[i].open_time > last_time)
      {
         last_time = orders[i].open_time;
         last_idx = i;
      }
   }
   
   return last_idx;
}

//+------------------------------------------------------------------+
//| 计算加仓间距                                                     |
//+------------------------------------------------------------------+
double CalculateAddDistance(int level)
{
   double base_distance = current_atr * InpATRMultiplier;
   
   // 首次加仓间距更大
   if(level == 1)
      return base_distance * InpFirstLevelMultiplier;
      
   // 趋势加速时缩短间距
   // 使用ADX指标判断趋势强度
   if(current_adx > 30) // 强趋势
      return base_distance * 0.8;
   else if(current_adx > 20) // 中等趋势
      return base_distance * 0.9;
   
   return base_distance;
}

//+------------------------------------------------------------------+
//| 格式化手数，确保符合交易品种的最小手数和步长要求                 |
//+------------------------------------------------------------------+
double FormatLots(string symbol, double lots)
{
   double a = 0;
   double minilots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double steplots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(lots < minilots)
      return(minilots);
   else
   {
      double a1 = MathFloor(lots / minilots) * minilots;
      a = a1 + MathFloor((lots - a1) / steplots) * steplots;
   }
   
   return(a);
}

//+------------------------------------------------------------------+
//| 计算加仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateAddLot(double prev_lot, int level)
{
   // 根据层级选择倍数
   double multiplier;
   
   if(level <= 5)
      multiplier = InpInitialLotMultiplier;
   else if(level <= 10)
      multiplier = InpMidLotMultiplier;
   else if(level <= 15)
      multiplier = InpLateLotMultiplier;
   else
      multiplier = InpFinalLotMultiplier;
      
   // 非线性递减公式
   double lot_multiplier = 1 + (multiplier - 1) / level;
   
   // 计算新手数
   double new_lot = NormalizeDouble(prev_lot * lot_multiplier, 2);
   
   // 检查是否超过最大手数限制
   double max_lot = NormalizeDouble(InpMaxLotDivisor * (account_equity / InpEquityPerLot), 2);
   if(new_lot > max_lot)
      new_lot = max_lot;
      
   // 使用FormatLots函数格式化手数
   new_lot = FormatLots(Symbol(), new_lot);
      
   return new_lot;
}

//+------------------------------------------------------------------+
//| 计算开仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateInitialLot()
{
   double lot;
   
   if(InpMoneyManagementMode == MODE_COMPOUND)
   {
      // 复利模式 - 新仓位=基础仓位×（当前账户余额/5000）
      lot = NormalizeDouble(InpMaxLotDivisor * (account_balance / InpEquityPerLot), 2);
   }
   else
   {
      // 固定模式
      lot = InpMaxLotDivisor; // 使用基础仓位作为固定模式手数
   }
   
   // 检查最小/最大手数限制
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   
   if(lot < min_lot) lot = min_lot;
   if(lot > max_lot) lot = max_lot;
   
   // 使用FormatLots函数格式化手数
   lot = FormatLots(Symbol(), lot);
   
   return lot;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CalculateStopLoss(double entry_price, int direction)
{
   double sl;
   
   if(InpStopLossMode == SL_ATR)
   {
      // ATR动态止损
      double sl_distance = current_atr * InpATRStopMultiplier;
      
      if(direction > 0) // 多单
         sl = entry_price - sl_distance;
      else // 空单
         sl = entry_price + sl_distance;
   }
   else
   {
      // 固定点数止损
      double sl_distance = InpFixedStopLoss * point_value;
      
      if(direction > 0) // 多单
         sl = entry_price - sl_distance;
      else // 空单
         sl = entry_price + sl_distance;
   }
   
   return NormalizeDouble(sl, SymbolInfo.Digits());
}

//+------------------------------------------------------------------+
//| 更新移动保本止损                                                 |
//+------------------------------------------------------------------+
void UpdateBreakEven()
{
   // 更新多单移动止损
   for(int i = 0; i < ArraySize(long_orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      // 计算当前盈利点数
      double profit_points = (last_tick.bid - long_orders[i].open_price) / point_value;
      
      // 检查是否达到移动保本条件
      if(profit_points >= InpBreakEvenStart)
      {
         double new_sl = long_orders[i].open_price + InpBreakEvenProfit * point_value;
         
         // 检查是否需要修改止损
         if(long_orders[i].sl < long_orders[i].open_price || long_orders[i].sl == 0)
         {
            Trade.PositionModify(long_orders[i].ticket, new_sl, long_orders[i].tp);
         }
      }
   }
   
   // 更新空单移动止损
   for(int i = 0; i < ArraySize(short_orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      // 计算当前盈利点数
      double profit_points = (short_orders[i].open_price - last_tick.ask) / point_value;
      
      // 检查是否达到移动保本条件
      if(profit_points >= InpBreakEvenStart)
      {
         double new_sl = short_orders[i].open_price - InpBreakEvenProfit * point_value;
         
         // 检查是否需要修改止损
         if(short_orders[i].sl > short_orders[i].open_price || short_orders[i].sl == 0)
         {
            Trade.PositionModify(short_orders[i].ticket, new_sl, short_orders[i].tp);
         }
      }
   }
}

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

// 函数已移至函数文件中

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   // 更新值
   ObjectSetString(0, "InfoPanel_Value_0", OBJPROP_TEXT, DoubleToString(account_equity, 2));
   ObjectSetString(0, "InfoPanel_Value_1", OBJPROP_TEXT, IntegerToString(total_long_orders));
   ObjectSetString(0, "InfoPanel_Value_2", OBJPROP_TEXT, IntegerToString(total_short_orders));
   ObjectSetString(0, "InfoPanel_Value_3", OBJPROP_TEXT, DoubleToString(total_long_lot, 2));
   ObjectSetString(0, "InfoPanel_Value_4", OBJPROP_TEXT, DoubleToString(total_short_lot, 2));
   ObjectSetString(0, "InfoPanel_Value_5", OBJPROP_TEXT, DoubleToString(CalculateTotalRisk(),