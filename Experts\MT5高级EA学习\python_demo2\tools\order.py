# 导入MT5的模块
import MetaTrader5 as mt5


# 连接MT5软件
def login(path, server, username, password, timeout):
    is_ok = mt5.initialize(path=path,
                           server=server,
                           login=username,
                           password=password,
                           timeout=timeout)
    if not is_ok:
        print("连接MT5失败, 错误原因: ", mt5.last_error())
        mt5.shutdown()
        return False
    else:
        return True


# 主动断开连接
def stop():
    mt5.shutdown()


# 验证下单手数
def val_volume(symbol, volume):
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    # print(type(symbol_info))
    if not symbol_info:
        return 0

    if volume < symbol_info.volume_min:
        return symbol_info.volume_min
    elif volume > symbol_info.volume_max:
        return symbol_info.volume_min
    else:  # volume == 0.0111111
        return round(volume, 2)


# 验证止损价格
def val_stoploss(symbol, type, price, sl):
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return 0
    if price <= 0:
        return 0
    if sl <= 0:
        return 0

    stop_level = symbol_info.trade_stops_level
    point = symbol_info.point
    # 如果自定义的止损点数 大于或者等于 最小间隔限制
    if sl >= stop_level:
        m_sl = sl
    else:
        m_sl = stop_level

    if type == 0:
        return round(price - m_sl * point, symbol_info.digits)
    elif type == 1:
        return round(price + m_sl * point, symbol_info.digits)
    return 0


# 验证止盈价格
def val_takeprofit(symbol, type, price, tp):
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return 0
    if price <= 0:
        return 0
    if tp <= 0:
        return 0

    stop_level = symbol_info.trade_stops_level
    point = symbol_info.point
    # 如果自定义的止损点数 大于或者等于 最小间隔限制
    if tp >= stop_level:
        m_tp = tp
    else:
        m_tp = stop_level

    if type == 0:
        return round(price + m_tp * point, symbol_info.digits)
    elif type == 1:
        return round(price - m_tp * point, symbol_info.digits)
    return 0


# 查询指定的挂单是否存在
def find_order(group, comment, magic):
    orders = mt5.orders_get(group=group)
    if not orders:
        return False
    for order in orders:
        m_order = order._asdict()
        if m_order['comment'] == comment and m_order['magic'] == magic:
            return True

    return False


# 统计指定条件挂单的数量
def order_count(group, type, magic):
    count = 0
    orders = mt5.orders_get(group=group)
    if not orders:
        return 0
    for order in orders:
        m_order = order._asdict()
        if m_order['type'] == type and m_order['magic'] == magic:
            count += 1
        elif type == 12 and m_order['magic'] == magic:
            count += 1
    return count


# 开挂单
def order_send(symbol, order_type, price, volume, sl, tp, deviation,
               stop_limit, type_time, expiration, comment, magic):
    # 获取交易品种信息
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return False
    # 检查挂单是否存在
    if find_order(symbol, comment, magic):
        return False

    # 检查挂单价
    if order_type == mt5.ORDER_TYPE_BUY_LIMIT:
        if price > symbol_info.ask:
            return False
    elif order_type == mt5.ORDER_TYPE_SELL_LIMIT:
        if price < symbol_info.bid:
            return False
    elif order_type == mt5.ORDER_TYPE_BUY_STOP:
        if price < symbol_info.ask:
            return False
    elif order_type == mt5.ORDER_TYPE_SELL_STOP:
        if price > symbol_info.bid:
            return False
    elif order_type == mt5.ORDER_TYPE_BUY_STOP_LIMIT:
        if stop_limit > symbol_info.ask:
            return False
    elif order_type == mt5.ORDER_TYPE_SELL_STOP_LIMIT:
        if stop_limit < symbol_info.bid:
            return False
    else:
        return False

    m_type = order_type % 2
    if order_type == mt5.ORDER_TYPE_BUY_STOP_LIMIT or \
            order_type == mt5.ORDER_TYPE_SELL_STOP_LIMIT:
        m_sl = val_stoploss(symbol, m_type, stop_limit, sl)
        m_tp = val_takeprofit(symbol, m_type, stop_limit, tp)
    else:
        m_sl = val_stoploss(symbol, m_type, price, sl)
        m_tp = val_takeprofit(symbol, m_type, price, tp)

    m_volume = val_volume(symbol, volume)

    request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": symbol,
        "type": order_type,
        "price": price,
        "volume": m_volume,
        "deviation": deviation,
        "comment": comment,
        "magic": magic,
        "type_filling": mt5.ORDER_FILLING_IOC,
        "type_time": type_time,
        "expiration": expiration
    }

    if m_sl != 0:
        request['sl'] = m_sl
    if m_tp != 0:
        request['tp'] = m_tp

    result = mt5.order_send(request)

    if not result:
        print("交易请求发送失败, 错误原因: ", mt5.last_error())
        return False
    else:
        return True


def buy_limit(symbol, price, volume, sl, tp, deviation,
              stop_limit, type_time, expiration, comment, magic):
    return order_send(symbol, mt5.ORDER_TYPE_BUY_LIMIT, price, volume, sl, tp, deviation,
                      stop_limit, type_time, expiration, comment, magic)


def sell_limit(symbol, price, volume, sl, tp, deviation,
               stop_limit, type_time, expiration, comment, magic):
    return order_send(symbol, mt5.ORDER_TYPE_SELL_LIMIT, price, volume, sl, tp, deviation,
                      stop_limit, type_time, expiration, comment, magic)


def buy_stop(symbol, price, volume, sl, tp, deviation,
             stop_limit, type_time, expiration, comment, magic):
    return order_send(symbol, mt5.ORDER_TYPE_BUY_STOP, price, volume, sl, tp, deviation,
                      stop_limit, type_time, expiration, comment, magic)


def sell_stop(symbol, price, volume, sl, tp, deviation,
              stop_limit, type_time, expiration, comment, magic):
    return order_send(symbol, mt5.ORDER_TYPE_SELL_STOP, price, volume, sl, tp, deviation,
                      stop_limit, type_time, expiration, comment, magic)


def buy_stop_limit(symbol, price, volume, sl, tp, deviation,
                   stop_limit, type_time, expiration, comment, magic):
    return order_send(symbol, mt5.ORDER_TYPE_BUY_STOP_LIMIT, price, volume, sl, tp, deviation,
                      stop_limit, type_time, expiration, comment, magic)


def sell_stop_limit(symbol, price, volume, sl, tp, deviation,
                    stop_limit, type_time, expiration, comment, magic):
    return order_send(symbol, mt5.ORDER_TYPE_SELL_STOP_LIMIT, price, sl, tp, deviation,
                      stop_limit, type_time, expiration, comment, magic)


# 修改制定单号的挂单
# 能修改的部分: price价格,sl止损价,tp止盈价,stop_limit,type_time到期时间类型, 到期时间(秒数)
def modify_order_ticket(order: mt5.TradeOrder, price, stoploss, takeprofit,
                        stop_limit, type_time, expiration):
    if not order:
        return False

    request = {
        "action": mt5.TRADE_ACTION_MODIFY,
        "order": order.ticket,
        "symbol": order.symbol,
        "type": order.type,
        "price": price,
        "type_time": type_time
    }
    if stoploss == -1:
        request["sl"] = order.sl
    elif stoploss != 0:
        request["sl"] = stoploss

    if takeprofit == -1:
        request["tp"] = order.tp
    elif takeprofit != 0:
        request["tp"] = takeprofit

    if stop_limit != 0:
        request["stop_limit"] = stop_limit

    if expiration == 0:
        request['expiration'] = order.time_expiration
    else:
        request['expiration'] = expiration

    result = mt5.order_send(request)

    if not result:
        print("交易请求发送失败, 错误原因: ", mt5.last_error())
        return False
    else:
        return True


# 修改制定方向的所有挂单
def modify_order(symbol, order_type, magic, price, stoploss, takeprofit, stop_limit,
                 type_time, expiration):
    orders = mt5.orders_get(symbol=symbol)
    if not orders:
        return

    m_order: mt5.TradeOrder
    for order in orders:
        m_order = order
        if m_order.magic != magic:
            continue
        if m_order.type != order_type:
            continue

        modify_order_ticket(m_order, price, stoploss, takeprofit, stop_limit, type_time, expiration)


# 删除指定挂单
def delete_order_ticket(order: mt5.TradeOrder):
    if not order:
        return False

    request = {
        "action": mt5.TRADE_ACTION_REMOVE,
        "order": order.ticket
    }

    result = mt5.order_send(request)

    if not result:
        print("交易请求发送失败, 错误原因: ", mt5.last_error())
        return False
    else:
        return True


# 删除指定类型范围的订单
def delete_order(symbol, order_type, magic):
    orders = mt5.orders_get(symbol=symbol)
    if not orders:
        return

    m_order: mt5.TradeOrder
    for order in orders:
        m_order = order
        if m_order.magic != magic:
            continue
        if m_order.type != order_type:
            continue

        delete_order_ticket(m_order)
