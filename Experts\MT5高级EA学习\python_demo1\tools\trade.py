# 导入MT5的模块
import MetaTrader5 as mt5


# 连接MT5软件
def login(path, server, username, password, timeout):
    is_ok = mt5.initialize(path=path,
                           server=server,
                           login=username,
                           password=password,
                           timeout=timeout)
    if not is_ok:
        print("连接MT5失败, 错误原因: ", mt5.last_error())
        mt5.shutdown()
        return False
    else:
        return True


# 主动断开连接
def stop():
    mt5.shutdown()


# 验证下单手数
def val_volume(symbol, volume):
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    # print(type(symbol_info))
    if not symbol_info:
        return 0

    if volume < symbol_info.volume_min:
        return symbol_info.volume_min
    elif volume > symbol_info.volume_max:
        return symbol_info.volume_min
    else:  # volume == 0.0111111
        return round(volume, 2)


# 验证止损价格
def val_stoploss(symbol, type, price, sl):
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return 0
    if price <= 0:
        return 0
    if sl <= 0:
        return 0

    stop_level = symbol_info.trade_stops_level
    point = symbol_info.point
    # 如果自定义的止损点数 大于或者等于 最小间隔限制
    if sl >= stop_level:
        m_sl = sl
    else:
        m_sl = stop_level

    if type == 0:
        return round(price - m_sl * point, symbol_info.digits)
    elif type == 1:
        return round(price + m_sl * point, symbol_info.digits)
    return 0


# 验证止盈价格
def val_takeprofit(symbol, type, price, tp):
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return 0
    if price <= 0:
        return 0
    if tp <= 0:
        return 0

    stop_level = symbol_info.trade_stops_level
    point = symbol_info.point
    # 如果自定义的止损点数 大于或者等于 最小间隔限制
    if tp >= stop_level:
        m_tp = tp
    else:
        m_tp = stop_level

    if type == 0:
        return round(price + m_tp * point, symbol_info.digits)
    elif type == 1:
        return round(price - m_tp * point, symbol_info.digits)
    return 0


# 搜索指定订单是否存在
def find_position(group, comment, magic):
    # 获取所有持仓单数据
    positions = mt5.positions_get(group=group)
    if not positions:
        return False
    # pprint(positions)
    for pos in positions:
        m_pos = pos._asdict()
        if m_pos['comment'] == comment and m_pos['magic'] == magic:
            return True
    return False


# 统计指定条件的订单数
def position_count(group, type, magic):
    count = 0
    positions = mt5.positions_get(group=group)
    # not == !
    if not positions:
        return 0
    for pos in positions:
        m_pos = pos._asdict()
        # and == &&
        if m_pos['type'] == type and m_pos['magic'] == magic:
            count += 1
        elif type == 12 and m_pos['magic'] == magic:
            count += 1
    return count


# 开仓函数
def sand(symbol, type, volume, sl, tp, deviation, comment, magic):
    # 获取交易品种信息
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return False
    # 检查订单是否存在
    if find_position(symbol, comment, magic):
        return False

    if type == 0:
        m_price = symbol_info.ask
        m_sl = val_stoploss(symbol, 0, m_price, sl)
        m_tp = val_takeprofit(symbol, 0, m_price, tp)
    elif type == 1:
        m_price = symbol_info.bid
        m_sl = val_stoploss(symbol, 1, m_price, sl)
        m_tp = val_takeprofit(symbol, 1, m_price, tp)
    else:
        m_price = 0
        m_sl = 0
        m_tp = 0

    m_volume = val_volume(symbol, volume)

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "type": type,
        "price": m_price,
        "volume": m_volume,
        "deviation": deviation,
        "comment": comment,
        "magic": magic,
        "type_filling": mt5.ORDER_FILLING_IOC
    }

    if m_sl != 0:
        request['sl'] = m_sl
    if m_tp != 0:
        request['tp'] = m_tp

    result = mt5.order_send(request)

    if not result:
        print("交易请求发送失败, 错误原因: ", mt5.last_error())
        return False
    else:
        return True


def buy(symbol, volume, sl, tp, deviation, comment, magic):
    return sand(symbol, 0, volume, sl, tp, deviation, comment, magic)


def sell(symbol, volume, sl, tp, deviation, comment, magic):
    return sand(symbol, 1, volume, sl, tp, deviation, comment, magic)


# 修改制定订单
def update_ticket(position: mt5.TradePosition, stoploss, takeprofit):
    if not position:
        return False

    request = {
        "action": mt5.TRADE_ACTION_SLTP,
        "position": position.ticket,
        "symbol": position.symbol,
        "magic": position.magic,
        "type_filling": mt5.ORDER_FILLING_IOC
    }

    if stoploss != 0:
        request['sl'] = stoploss
    if takeprofit != 0:
        request['tp'] = takeprofit

    result = mt5.order_send(request)

    if not result:
        print("修改订单失败, 错误原因 ", mt5.last_error())
        return False
    else:
        return True


# 根据指定的条件修改止损止盈
def update_position(symbol, type, sl, tp, magic):
    positions = mt5.positions_get(symbol=symbol)
    if not positions:
        return

    m_pos: mt5.TradePosition
    for pos in positions:
        m_pos = pos
        if m_pos.magic != magic:
            continue
        elif m_pos.type != type:
            continue

        # 验证通过
        if sl == -1:
            m_sl = -1
        else:
            m_sl = val_stoploss(symbol, type, m_pos.price_current, sl)

        if tp == -1:
            m_tp = -1
        else:
            m_tp = val_takeprofit(symbol, type, m_pos.price_current, tp)

        if m_sl != m_pos.sl and m_sl != -1:
            if m_tp == -1 or m_tp == m_pos.tp:
                update_ticket(m_pos, m_sl, m_pos.tp)
            elif m_tp != m_pos.tp:
                update_ticket(m_pos, m_sl, m_tp)
        elif m_tp != m_pos.tp and m_tp != -1:
            if m_sl == -1 or m_sl == m_pos.sl:
                update_ticket(m_pos, m_pos.sl, m_tp)
            elif m_sl != m_pos.sl:
                update_ticket(m_pos, m_sl, m_tp)


# 止损向盈利方向移动
def update_position_up(symbol, type, sl, tp, magic):
    positions = mt5.positions_get(symbol=symbol)
    if not positions:
        return

    m_pos: mt5.TradePosition
    for pos in positions:
        m_pos = pos
        if m_pos.magic != magic:
            continue
        elif m_pos.type != type:
            continue

        # 验证通过
        if sl == -1:
            m_sl = -1
        else:
            m_sl = val_stoploss(symbol, type, m_pos.price_current, sl)

        if tp == -1:
            m_tp = -1
        else:
            m_tp = val_takeprofit(symbol, type, m_pos.price_current, tp)

        # 如果是buy方向
        if type == 0:
            if m_sl > m_pos.sl and m_sl != -1:
                if m_tp == -1 or m_tp == m_pos.tp:
                    update_ticket(m_pos, m_sl, m_pos.tp)
                elif m_tp != m_pos.tp:
                    update_ticket(m_pos, m_sl, m_tp)
        # 如果是sell方向
        elif type == 1:
            if m_sl < m_pos.sl and m_sl != -1:
                if m_tp == -1 or m_tp == m_pos.tp:
                    update_ticket(m_pos, m_sl, m_pos.tp)
                elif m_tp != m_pos.tp:
                    update_ticket(m_pos, m_sl, m_tp)

        # 如果止盈发生改变就只修改止盈
        if m_tp != m_pos.tp and m_tp != -1:
            update_ticket(m_pos, m_pos.sl, m_tp)


# 平仓指定订单
def close_ticket(position: mt5.TradePosition,):
    if not position:
        return False

    if position.type == 0:
        m_type = 1
    elif position.type == 1:
        m_type = 0
    else:
        return False

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "position": position.ticket,
        "symbol": position.symbol,
        "type": m_type,
        "price": position.price_current,
        "volume": position.volume,
        "deviation": 0,
        "magic": position.magic,
        "type_filling": mt5.ORDER_FILLING_IOC
    }

    result = mt5.order_send(request)

    if not result:
        print("修改订单失败, 错误原因 ", mt5.last_error())
        return False
    else:
        return True


# 根据指定范围平仓
def close_position(symbol, type, magic):
    positions = mt5.positions_get(symbol=symbol)
    if not positions:
        return

    m_pos: mt5.TradePosition
    for pos in positions:
        m_pos = pos
        if m_pos.magic != magic:
            continue
        elif m_pos.type != type:
            if type != -1:
                continue
        # 条件通过
        close_ticket(m_pos)

