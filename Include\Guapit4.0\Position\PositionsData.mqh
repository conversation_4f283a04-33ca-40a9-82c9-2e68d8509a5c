
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#include "Position.mqh"


class PositionsData
{
   public:
      PositionsData(void);
      ~PositionsData(void);
      int count; // 所有持仓单数
      int count_buy; // 做多方向持仓单数
      int count_sell; // 做空泛型的持仓单数
      double profit; // 总盈利
      double profit_buy; // 做多盈利
      double profit_sell; // 做空盈利
      Position pos_first; // 首仓单信息
      Position pos_last; // 最后加仓单信息

};
PositionsData::PositionsData(void)
{

}

PositionsData::~PositionsData(void)
{

}