from history import *
import datetime
from pprint import pprint


# 统计指定时间范围内所有历史交易订单盈亏额
def getHistoryProfit(start_time, end_time, group):
    # 获取指定时间范围内的历史交易订单数据
    deals = mt5.history_deals_get(start_time, end_time, group=group)
    m_profit = 0.0
    if not deals:
        return 0
    for deal in deals:
        m_deal = deal._asdict()
        m_profit += m_deal['profit'] + m_deal['commission'] + m_deal['swap']

    return m_profit

if __name__ in "__main__":
    path = "D:\\MetaTrader 5\\terminal64.exe"
    if not login(path=path,
                 server="Exness-MT5Trial5",
                 username=76314481,
                 password="6ixuhbWp",
                 timeout=2000):
        quit()

    delta = datetime.timedelta(hours=-4)
    start_time = datetime.datetime.now() + delta
    stop_time = datetime.datetime.now()
    print(start_time)
    print(stop_time)
    # 获取指定时间范围的历史交易单数
    total = mt5.history_deals_total(start_time,stop_time)
    pprint(total)

    # 获取指定时间范围内的历史交易订单数据
    deals = mt5.history_deals_get(start_time,stop_time,group="*")
    if not deals:
        quit()
    pprint(len(deals))

    for deal in deals:
        pprint(deal._asdict())

    # 获取历史盈利额
    profit = getHistoryProfit(start_time, stop_time, group="*")
    pprint(profit)

    # 获取历史挂单数据
    # 获取指定时间范围的历史交易单数
    order_total = mt5.history_orders_total(start_time, stop_time)
    pprint(order_total)

    orders = mt5.history_orders_get(start_time, stop_time, group="*")
    if orders:
        pprint(orders[0]._asdict())
