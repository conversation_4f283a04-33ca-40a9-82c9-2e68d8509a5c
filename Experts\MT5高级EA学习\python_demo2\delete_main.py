import time

import MetaTrader5 as mt5
from tools.order import *
import datetime

if __name__ == '__main__':
    path = "D:\\MetaTrader 5\\terminal64.exe"
    if not login(path=path,
                 server="Exness-MT5Trial5",
                 username=76314481,
                 password="6ixuhbWp",
                 timeout=2000):
        quit()

    delta = datetime.timedelta(seconds=120)
    dt = (datetime.datetime.now() + delta).timetuple()
    sec = int(time.mktime(dt))
    print(sec)

    symbol = "EURUSDz"
    magic = 8199231
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        stop()

    # 删除指定方向挂单
    # buy limit
    delete_order(symbol, 2, 0)
    # sell limit
    delete_order(symbol, 3, 0)
    # buy stop
    delete_order(symbol, 4, 0)
    # sell stop
    delete_order(symbol, 5, 0)