//+------------------------------------------------------------------+
//|                                          动态风险控制矩阵测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "动态风险控制矩阵功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input int TestScenarios = 6;         // 测试场景数量

//+------------------------------------------------------------------+
//| 动态风险控制矩阵测试脚本                                          |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始动态风险控制矩阵测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("测试场景数: ", TestScenarios);
    Print("========================================");
    
    // 测试1: 风险等级评估
    TestRiskLevelAssessment();
    
    // 测试2: 熔断器触发机制
    TestCircuitBreakerTriggers();
    
    // 测试3: 风险调整因子计算
    TestRiskAdjustmentFactors();
    
    // 测试4: 订单参数风险调整
    TestOrderParameterRiskAdjustment();
    
    // 测试5: 风险评分计算
    TestRiskScoreCalculation();
    
    // 测试6: 完整风险控制流程
    TestCompleteRiskControlWorkflow();
    
    Print("========================================");
    Print("✅ 动态风险控制矩阵测试完成");
}

//+------------------------------------------------------------------+
//| 测试风险等级评估                                                  |
//+------------------------------------------------------------------+
void TestRiskLevelAssessment()
{
    Print("📋 测试1: 风险等级评估");
    
    // 测试不同风险指标组合的等级评估
    struct RiskTestCase {
        double vixLevel;
        double liquidityIndex;
        double currentDrawdown;
        double volatilityIndex;
        bool economicEventActive;
        int expectedRiskLevel;
        string description;
    };
    
    RiskTestCase testCases[] = {
        {12.0, 85.0, 2.0, 18.0, false, 0, "低风险场景"},
        {18.0, 70.0, 5.0, 25.0, false, 1, "中风险场景"},
        {25.0, 50.0, 8.0, 40.0, true, 2, "高风险场景"},
        {35.0, 30.0, 11.0, 60.0, true, 3, "极高风险场景"},
        {45.0, 15.0, 15.0, 75.0, true, 3, "极端市场场景"},
        {20.0, 90.0, 1.0, 15.0, true, 1, "经济事件影响"}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        RiskTestCase tc = testCases[i];
        
        // 计算风险等级
        int calculatedRiskLevel = CalculateTestRiskLevel(tc);
        
        Print("场景: ", tc.description);
        Print("  VIX: ", DoubleToString(tc.vixLevel, 1));
        Print("  流动性: ", DoubleToString(tc.liquidityIndex, 1));
        Print("  回撤: ", DoubleToString(tc.currentDrawdown, 1), "%");
        Print("  波动率: ", DoubleToString(tc.volatilityIndex, 1));
        Print("  经济事件: ", tc.economicEventActive ? "活跃" : "正常");
        Print("  计算风险等级: ", calculatedRiskLevel, " (", GetTestRiskLevelDescription(calculatedRiskLevel), ")");
        Print("  预期风险等级: ", tc.expectedRiskLevel, " (", GetTestRiskLevelDescription(tc.expectedRiskLevel), ")");
        
        bool riskLevelCorrect = (calculatedRiskLevel == tc.expectedRiskLevel);
        Print("  等级验证: ", riskLevelCorrect ? "✓" : "✗");
    }
    
    Print("✅ 风险等级评估完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试风险等级                                                  |
//+------------------------------------------------------------------+
int CalculateTestRiskLevel(const RiskTestCase &tc) {
    int maxRiskLevel = 0; // 低风险
    
    // VIX评估
    int vixRisk = 0;
    if(tc.vixLevel >= 40.0) vixRisk = 3;
    else if(tc.vixLevel >= 30.0) vixRisk = 2;
    else if(tc.vixLevel >= 20.0) vixRisk = 1;
    else vixRisk = 0;
    
    // 流动性评估 (值越低风险越高)
    int liquidityRisk = 0;
    if(tc.liquidityIndex <= 20.0) liquidityRisk = 3;
    else if(tc.liquidityIndex <= 40.0) liquidityRisk = 2;
    else if(tc.liquidityIndex <= 60.0) liquidityRisk = 1;
    else liquidityRisk = 0;
    
    // 回撤评估
    int drawdownRisk = 0;
    if(tc.currentDrawdown >= 12.0) drawdownRisk = 3;
    else if(tc.currentDrawdown >= 9.0) drawdownRisk = 2;
    else if(tc.currentDrawdown >= 6.0) drawdownRisk = 1;
    else drawdownRisk = 0;
    
    // 波动率评估
    int volatilityRisk = 0;
    if(tc.volatilityIndex >= 70.0) volatilityRisk = 3;
    else if(tc.volatilityIndex >= 50.0) volatilityRisk = 2;
    else if(tc.volatilityIndex >= 35.0) volatilityRisk = 1;
    else volatilityRisk = 0;
    
    // 经济事件评估
    int economicRisk = tc.economicEventActive ? 1 : 0;
    
    // 取最高风险等级
    maxRiskLevel = MathMax(maxRiskLevel, vixRisk);
    maxRiskLevel = MathMax(maxRiskLevel, liquidityRisk);
    maxRiskLevel = MathMax(maxRiskLevel, drawdownRisk);
    maxRiskLevel = MathMax(maxRiskLevel, volatilityRisk);
    maxRiskLevel = MathMax(maxRiskLevel, economicRisk);
    
    return maxRiskLevel;
}

//+------------------------------------------------------------------+
//| 获取测试风险等级描述                                              |
//+------------------------------------------------------------------+
string GetTestRiskLevelDescription(int level) {
    switch(level) {
        case 0: return "低风险";
        case 1: return "中风险";
        case 2: return "高风险";
        case 3: return "极高风险";
        default: return "未知风险";
    }
}

//+------------------------------------------------------------------+
//| 测试熔断器触发机制                                                |
//+------------------------------------------------------------------+
void TestCircuitBreakerTriggers()
{
    Print("📋 测试2: 熔断器触发机制");
    
    // 测试各种熔断器的触发条件
    struct CircuitBreakerTest {
        string breakerType;
        double testValue;
        double lowThreshold;
        double mediumThreshold;
        double highThreshold;
        double extremeThreshold;
        bool isReverse; // 流动性是反向的
        int expectedTriggerLevel;
    };
    
    CircuitBreakerTest tests[] = {
        {"VIX", 12.0, 15.0, 20.0, 30.0, 40.0, false, 0},
        {"VIX", 25.0, 15.0, 20.0, 30.0, 40.0, false, 2},
        {"VIX", 45.0, 15.0, 20.0, 30.0, 40.0, false, 3},
        {"流动性", 85.0, 80.0, 60.0, 40.0, 20.0, true, 0},
        {"流动性", 50.0, 80.0, 60.0, 40.0, 20.0, true, 2},
        {"流动性", 15.0, 80.0, 60.0, 40.0, 20.0, true, 3},
        {"回撤", 2.0, 3.0, 6.0, 9.0, 12.0, false, 0},
        {"回撤", 10.0, 3.0, 6.0, 9.0, 12.0, false, 2},
        {"回撤", 15.0, 3.0, 6.0, 9.0, 12.0, false, 3}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        CircuitBreakerTest cbt = tests[i];
        
        // 计算触发等级
        int triggerLevel = CalculateCircuitBreakerTriggerLevel(cbt);
        
        Print("熔断器: ", cbt.breakerType);
        Print("  测试值: ", DoubleToString(cbt.testValue, 1));
        Print("  阈值: 低<", DoubleToString(cbt.lowThreshold, 1), 
              " 中<", DoubleToString(cbt.mediumThreshold, 1),
              " 高<", DoubleToString(cbt.highThreshold, 1),
              " 极>", DoubleToString(cbt.extremeThreshold, 1));
        Print("  计算触发等级: ", triggerLevel, " (", GetTestRiskLevelDescription(triggerLevel), ")");
        Print("  预期触发等级: ", cbt.expectedTriggerLevel, " (", GetTestRiskLevelDescription(cbt.expectedTriggerLevel), ")");
        
        bool triggerCorrect = (triggerLevel == cbt.expectedTriggerLevel);
        Print("  触发验证: ", triggerCorrect ? "✓" : "✗");
    }
    
    Print("✅ 熔断器触发机制完成\n");
}

//+------------------------------------------------------------------+
//| 计算熔断器触发等级                                                |
//+------------------------------------------------------------------+
int CalculateCircuitBreakerTriggerLevel(const CircuitBreakerTest &cbt) {
    double value = cbt.testValue;
    
    if(cbt.isReverse) {
        // 流动性：值越低风险越高
        if(value <= cbt.extremeThreshold) return 3;
        if(value <= cbt.highThreshold) return 2;
        if(value <= cbt.mediumThreshold) return 1;
        return 0;
    } else {
        // 其他指标：值越高风险越高
        if(value >= cbt.extremeThreshold) return 3;
        if(value >= cbt.highThreshold) return 2;
        if(value >= cbt.mediumThreshold) return 1;
        return 0;
    }
}

//+------------------------------------------------------------------+
//| 测试风险调整因子计算                                              |
//+------------------------------------------------------------------+
void TestRiskAdjustmentFactors()
{
    Print("📋 测试3: 风险调整因子计算");
    
    // 测试不同风险等级的调整因子
    struct RiskAdjustmentTest {
        int riskLevel;
        double expectedFactor;
        string description;
    };
    
    RiskAdjustmentTest tests[] = {
        {0, 1.0, "低风险-无调整"},
        {1, 0.7, "中风险-降低30%"},
        {2, 0.4, "高风险-降低60%"},
        {3, 0.1, "极高风险-降低90%"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        RiskAdjustmentTest rat = tests[i];
        
        // 计算调整因子
        double calculatedFactor = CalculateTestRiskAdjustmentFactor(rat.riskLevel);
        
        Print("场景: ", rat.description);
        Print("  风险等级: ", rat.riskLevel);
        Print("  计算调整因子: ", DoubleToString(calculatedFactor, 2));
        Print("  预期调整因子: ", DoubleToString(rat.expectedFactor, 2));
        
        bool factorCorrect = (MathAbs(calculatedFactor - rat.expectedFactor) < 0.01);
        Print("  因子验证: ", factorCorrect ? "✓" : "✗");
    }
    
    Print("✅ 风险调整因子计算完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试风险调整因子                                              |
//+------------------------------------------------------------------+
double CalculateTestRiskAdjustmentFactor(int riskLevel) {
    switch(riskLevel) {
        case 0: return 1.0;  // 低风险
        case 1: return 0.7;  // 中风险
        case 2: return 0.4;  // 高风险
        case 3: return 0.1;  // 极高风险
        default: return 1.0;
    }
}

//+------------------------------------------------------------------+
//| 测试订单参数风险调整                                              |
//+------------------------------------------------------------------+
void TestOrderParameterRiskAdjustment()
{
    Print("📋 测试4: 订单参数风险调整");
    
    // 基础订单参数
    struct TestOrderParams {
        int orderCount;
        int intervalSeconds;
        double orderSize;
    };
    
    TestOrderParams baseParams = {5, 600, 0.05};
    
    Print("基础参数: ", baseParams.orderCount, "个订单, ", 
          baseParams.intervalSeconds, "秒间隔, ", 
          DoubleToString(baseParams.orderSize, 3), "手");
    Print("");
    
    // 测试各风险等级的参数调整
    string riskLevelNames[] = {"低风险", "中风险", "高风险", "极高风险"};
    
    for(int i = 0; i < 4; i++) {
        // 模拟风险调整逻辑
        TestOrderParams adjustedParams = baseParams;
        
        switch(i) {
            case 0: // 低风险
                // 无调整
                break;
            case 1: // 中风险
                adjustedParams.orderCount = (int)(adjustedParams.orderCount * 0.5);
                adjustedParams.intervalSeconds = (int)(adjustedParams.intervalSeconds * 1.2);
                adjustedParams.orderSize *= 0.8;
                break;
            case 2: // 高风险
                adjustedParams.orderCount = (int)(adjustedParams.orderCount * 0.25);
                adjustedParams.intervalSeconds = (int)(adjustedParams.intervalSeconds * 1.5);
                adjustedParams.orderSize *= 0.5;
                break;
            case 3: // 极高风险
                adjustedParams.orderCount = 0;
                adjustedParams.orderSize = 0.01;
                adjustedParams.intervalSeconds = 900;
                break;
        }
        
        // 确保参数在合理范围内
        adjustedParams.orderCount = MathMax(0, MathMin(10, adjustedParams.orderCount));
        adjustedParams.intervalSeconds = MathMax(300, MathMin(900, adjustedParams.intervalSeconds));
        adjustedParams.orderSize = MathMax(0.01, MathMin(0.1, adjustedParams.orderSize));
        
        Print("风险等级: ", riskLevelNames[i]);
        Print("  订单数量: ", baseParams.orderCount, " → ", adjustedParams.orderCount);
        Print("  订单间隔: ", baseParams.intervalSeconds, "秒 → ", adjustedParams.intervalSeconds, "秒");
        Print("  订单规模: ", DoubleToString(baseParams.orderSize, 3), "手 → ", 
              DoubleToString(adjustedParams.orderSize, 3), "手");
        
        // 验证参数合理性
        bool paramsValid = (adjustedParams.orderCount >= 0 && adjustedParams.orderCount <= 10) &&
                          (adjustedParams.intervalSeconds >= 300 && adjustedParams.intervalSeconds <= 900) &&
                          (adjustedParams.orderSize >= 0.01 && adjustedParams.orderSize <= 0.1);
        Print("  参数验证: ", paramsValid ? "✓" : "✗");
    }
    
    Print("✅ 订单参数风险调整完成\n");
}

//+------------------------------------------------------------------+
//| 测试风险评分计算                                                  |
//+------------------------------------------------------------------+
void TestRiskScoreCalculation()
{
    Print("📋 测试5: 风险评分计算");
    
    // 测试不同风险指标组合的评分
    struct RiskScoreTest {
        double vixLevel;
        double liquidityIndex;
        double currentDrawdown;
        double volatilityIndex;
        bool economicEventActive;
        double expectedScore;
        string description;
    };
    
    RiskScoreTest tests[] = {
        {12.0, 85.0, 2.0, 18.0, false, 90.0, "优秀风险状况"},
        {18.0, 70.0, 5.0, 25.0, false, 70.0, "良好风险状况"},
        {25.0, 50.0, 8.0, 40.0, true, 40.0, "一般风险状况"},
        {35.0, 30.0, 11.0, 60.0, true, 15.0, "较差风险状况"},
        {45.0, 15.0, 15.0, 75.0, true, 0.0, "极差风险状况"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        RiskScoreTest rst = tests[i];
        
        // 计算风险评分
        double calculatedScore = CalculateTestRiskScore(rst);
        
        Print("场景: ", rst.description);
        Print("  VIX: ", DoubleToString(rst.vixLevel, 1));
        Print("  流动性: ", DoubleToString(rst.liquidityIndex, 1));
        Print("  回撤: ", DoubleToString(rst.currentDrawdown, 1), "%");
        Print("  波动率: ", DoubleToString(rst.volatilityIndex, 1));
        Print("  经济事件: ", rst.economicEventActive ? "活跃" : "正常");
        Print("  计算评分: ", DoubleToString(calculatedScore, 1));
        Print("  预期评分: ", DoubleToString(rst.expectedScore, 1));
        
        bool scoreCorrect = (MathAbs(calculatedScore - rst.expectedScore) < 15.0);
        Print("  评分验证: ", scoreCorrect ? "✓" : "✗");
    }
    
    Print("✅ 风险评分计算完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试风险评分                                                  |
//+------------------------------------------------------------------+
double CalculateTestRiskScore(const RiskScoreTest &rst) {
    double baseScore = 0.0;
    
    // VIX风险评分 (权重25%)
    double vixScore = 0.0;
    if(rst.vixLevel <= 15.0) vixScore = 25.0;
    else if(rst.vixLevel <= 20.0) vixScore = 20.0;
    else if(rst.vixLevel <= 30.0) vixScore = 15.0;
    else if(rst.vixLevel <= 40.0) vixScore = 10.0;
    else vixScore = 0.0;
    
    // 流动性风险评分 (权重20%)
    double liquidityScore = 0.0;
    if(rst.liquidityIndex >= 80.0) liquidityScore = 20.0;
    else if(rst.liquidityIndex >= 60.0) liquidityScore = 15.0;
    else if(rst.liquidityIndex >= 40.0) liquidityScore = 10.0;
    else if(rst.liquidityIndex >= 20.0) liquidityScore = 5.0;
    else liquidityScore = 0.0;
    
    // 回撤风险评分 (权重30%)
    double drawdownScore = 0.0;
    if(rst.currentDrawdown <= 3.0) drawdownScore = 30.0;
    else if(rst.currentDrawdown <= 6.0) drawdownScore = 25.0;
    else if(rst.currentDrawdown <= 9.0) drawdownScore = 20.0;
    else if(rst.currentDrawdown <= 12.0) drawdownScore = 10.0;
    else drawdownScore = 0.0;
    
    // 波动率风险评分 (权重15%)
    double volatilityScore = 0.0;
    if(rst.volatilityIndex <= 20.0) volatilityScore = 15.0;
    else if(rst.volatilityIndex <= 35.0) volatilityScore = 12.0;
    else if(rst.volatilityIndex <= 50.0) volatilityScore = 8.0;
    else if(rst.volatilityIndex <= 70.0) volatilityScore = 4.0;
    else volatilityScore = 0.0;
    
    // 经济事件风险评分 (权重10%)
    double economicScore = rst.economicEventActive ? 0.0 : 10.0;
    
    baseScore = vixScore + liquidityScore + drawdownScore + volatilityScore + economicScore;
    
    return MathMax(0.0, MathMin(100.0, baseScore));
}

//+------------------------------------------------------------------+
//| 测试完整风险控制流程                                              |
//+------------------------------------------------------------------+
void TestCompleteRiskControlWorkflow()
{
    Print("📋 测试6: 完整风险控制流程");
    
    // 模拟一个完整的风险控制周期
    Print("模拟风险控制流程:");
    
    // 场景1: 正常市场 → 风险上升 → 熔断器激活 → 风险下降 → 恢复正常
    struct WorkflowStep {
        string stepName;
        double vixLevel;
        double liquidityIndex;
        double currentDrawdown;
        int expectedRiskLevel;
        bool expectedCircuitBreaker;
    };
    
    WorkflowStep steps[] = {
        {"正常市场", 15.0, 80.0, 3.0, 0, false},
        {"市场波动", 22.0, 65.0, 6.5, 1, false},
        {"风险上升", 28.0, 45.0, 9.5, 2, true},
        {"极端情况", 38.0, 25.0, 13.0, 3, true},
        {"风险缓解", 25.0, 50.0, 8.0, 2, true},
        {"市场恢复", 18.0, 75.0, 4.0, 1, false},
        {"回归正常", 14.0, 85.0, 2.0, 0, false}
    };
    
    bool circuitBreakerActive = false;
    
    for(int i = 0; i < ArraySize(steps); i++) {
        WorkflowStep step = steps[i];
        
        // 计算风险等级
        RiskTestCase tc;
        tc.vixLevel = step.vixLevel;
        tc.liquidityIndex = step.liquidityIndex;
        tc.currentDrawdown = step.currentDrawdown;
        tc.volatilityIndex = 30.0; // 固定值
        tc.economicEventActive = false;
        
        int riskLevel = CalculateTestRiskLevel(tc);
        
        // 判断熔断器状态
        if(riskLevel >= 2 && !circuitBreakerActive) {
            circuitBreakerActive = true;
        } else if(riskLevel < 1 && circuitBreakerActive) {
            circuitBreakerActive = false;
        }
        
        Print("步骤", i+1, ": ", step.stepName);
        Print("  VIX: ", DoubleToString(step.vixLevel, 1), 
              " 流动性: ", DoubleToString(step.liquidityIndex, 1),
              " 回撤: ", DoubleToString(step.currentDrawdown, 1), "%");
        Print("  风险等级: ", riskLevel, " (", GetTestRiskLevelDescription(riskLevel), ")");
        Print("  熔断器: ", circuitBreakerActive ? "激活" : "正常");
        Print("  预期风险: ", step.expectedRiskLevel, " 预期熔断: ", step.expectedCircuitBreaker ? "激活" : "正常");
        
        bool riskCorrect = (riskLevel == step.expectedRiskLevel);
        bool breakerCorrect = (circuitBreakerActive == step.expectedCircuitBreaker);
        Print("  流程验证: ", (riskCorrect && breakerCorrect) ? "✓" : "✗");
        Print("");
    }
    
    Print("✅ 完整风险控制流程完成\n");
}
