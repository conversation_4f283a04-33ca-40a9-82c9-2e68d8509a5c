//+------------------------------------------------------------------+
//|                                          策略优化建议测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "策略优化建议系统功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input double TestProfitFactor = 1.2; // 测试盈利因子

//+------------------------------------------------------------------+
//| 策略优化建议系统测试脚本                                          |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始策略优化建议系统测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("测试盈利因子: ", DoubleToString(TestProfitFactor, 2));
    Print("========================================");
    
    // 测试1: 性能分析计算
    TestPerformanceAnalysisCalculation();
    
    // 测试2: 策略指标计算
    TestStrategyMetricsCalculation();
    
    // 测试3: 优化建议生成
    TestOptimizationSuggestionGeneration();
    
    // 测试4: 参数优化建议
    TestParameterOptimizationSuggestions();
    
    // 测试5: 性能等级评定
    TestPerformanceGrading();
    
    // 测试6: 完整优化建议流程
    TestCompleteOptimizationWorkflow();
    
    Print("========================================");
    Print("✅ 策略优化建议系统测试完成");
}

//+------------------------------------------------------------------+
//| 测试性能分析计算                                                  |
//+------------------------------------------------------------------+
void TestPerformanceAnalysisCalculation()
{
    Print("📋 测试1: 性能分析计算");
    
    // 模拟不同性能场景
    struct PerformanceScenario {
        double profitFactor;
        double winRate;
        double drawdown;
        double expectedPerformanceScore;
        string description;
    };
    
    PerformanceScenario scenarios[] = {
        {2.5, 75.0, 3.0, 90.0, "优秀性能"},
        {1.8, 65.0, 8.0, 75.0, "良好性能"},
        {1.2, 55.0, 12.0, 60.0, "中等性能"},
        {0.9, 45.0, 18.0, 35.0, "较差性能"},
        {0.6, 30.0, 25.0, 15.0, "很差性能"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        PerformanceScenario ps = scenarios[i];
        
        // 计算性能评分
        double score = 50.0; // 基础分
        
        // 基于盈利因子
        if(ps.profitFactor >= 2.0) score += 30.0;
        else if(ps.profitFactor >= 1.5) score += 20.0;
        else if(ps.profitFactor >= 1.2) score += 10.0;
        else if(ps.profitFactor < 1.0) score -= 20.0;
        
        // 基于胜率
        if(ps.winRate >= 70.0) score += 20.0;
        else if(ps.winRate >= 60.0) score += 15.0;
        else if(ps.winRate >= 50.0) score += 10.0;
        else if(ps.winRate < 40.0) score -= 15.0;
        
        score = MathMax(0.0, MathMin(100.0, score));
        
        Print("性能分析场景: ", ps.description);
        Print("  盈利因子: ", DoubleToString(ps.profitFactor, 2));
        Print("  胜率: ", DoubleToString(ps.winRate, 1), "%");
        Print("  回撤: ", DoubleToString(ps.drawdown, 1), "%");
        Print("  计算评分: ", DoubleToString(score, 1));
        Print("  预期评分: ", DoubleToString(ps.expectedPerformanceScore, 1));
        
        bool scoreCorrect = (MathAbs(score - ps.expectedPerformanceScore) < 10.0);
        Print("  评分验证: ", scoreCorrect ? "✓" : "✗");
    }
    
    Print("✅ 性能分析计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试策略指标计算                                                  |
//+------------------------------------------------------------------+
void TestStrategyMetricsCalculation()
{
    Print("📋 测试2: 策略指标计算");
    
    // 测试盈利能力指数计算
    struct ProfitabilityScenario {
        double profitFactor;
        double winRate;
        double expectedIndex;
        string description;
    };
    
    ProfitabilityScenario scenarios[] = {
        {2.0, 70.0, 85.0, "高盈利能力"},
        {1.5, 60.0, 72.5, "中等盈利能力"},
        {1.0, 50.0, 50.0, "基础盈利能力"},
        {0.8, 40.0, 35.0, "低盈利能力"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        ProfitabilityScenario ps = scenarios[i];
        
        // 计算盈利能力指数
        double index = 50.0; // 基础指数
        
        // 基于盈利因子
        if(ps.profitFactor > 0) {
            index = 50.0 + (ps.profitFactor - 1.0) * 25.0;
        }
        
        // 基于胜率调整
        double winRateAdjustment = (ps.winRate - 50.0) * 0.5;
        index += winRateAdjustment;
        
        index = MathMax(0.0, MathMin(100.0, index));
        
        Print("盈利能力场景: ", ps.description);
        Print("  盈利因子: ", DoubleToString(ps.profitFactor, 2));
        Print("  胜率: ", DoubleToString(ps.winRate, 1), "%");
        Print("  计算指数: ", DoubleToString(index, 1));
        Print("  预期指数: ", DoubleToString(ps.expectedIndex, 1));
        
        bool indexCorrect = (MathAbs(index - ps.expectedIndex) < 5.0);
        Print("  指数验证: ", indexCorrect ? "✓" : "✗");
    }
    
    Print("✅ 策略指标计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试优化建议生成                                                  |
//+------------------------------------------------------------------+
void TestOptimizationSuggestionGeneration()
{
    Print("📋 测试3: 优化建议生成");
    
    // 测试不同建议生成场景
    struct SuggestionScenario {
        double profitFactor;
        double winRate;
        double drawdown;
        int tradesPerDay;
        int expectedSuggestions;
        string description;
    };
    
    SuggestionScenario scenarios[] = {
        {2.5, 75.0, 3.0, 15, 0, "优秀状态-无建议"},
        {1.0, 45.0, 8.0, 25, 2, "中等状态-少量建议"},
        {0.8, 35.0, 18.0, 45, 4, "较差状态-多项建议"},
        {0.6, 25.0, 25.0, 60, 6, "很差状态-大量建议"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        SuggestionScenario ss = scenarios[i];
        
        // 模拟建议生成逻辑
        int calculatedSuggestions = 0;
        
        // 盈利因子过低
        if(ss.profitFactor < 1.2) calculatedSuggestions++;
        
        // 胜率过低
        if(ss.winRate < 50.0) calculatedSuggestions++;
        
        // 交易频率过高
        if(ss.tradesPerDay > 40) calculatedSuggestions++;
        
        // 回撤过高
        if(ss.drawdown > 15.0) calculatedSuggestions++;
        
        // 风险过高 (综合判断)
        if(ss.drawdown > 10.0 && ss.profitFactor < 1.5) calculatedSuggestions++;
        
        // 性能综合较差
        if(ss.profitFactor < 1.0 && ss.winRate < 40.0) calculatedSuggestions++;
        
        Print("建议生成场景: ", ss.description);
        Print("  盈利因子: ", DoubleToString(ss.profitFactor, 2));
        Print("  胜率: ", DoubleToString(ss.winRate, 1), "%");
        Print("  回撤: ", DoubleToString(ss.drawdown, 1), "%");
        Print("  日交易数: ", ss.tradesPerDay, "笔");
        Print("  计算建议数: ", calculatedSuggestions, "条");
        Print("  预期建议数: ", ss.expectedSuggestions, "条");
        
        bool suggestionsCorrect = (MathAbs(calculatedSuggestions - ss.expectedSuggestions) <= 1);
        Print("  建议数验证: ", suggestionsCorrect ? "✓" : "✗");
    }
    
    Print("✅ 优化建议生成测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试参数优化建议                                                  |
//+------------------------------------------------------------------+
void TestParameterOptimizationSuggestions()
{
    Print("📋 测试4: 参数优化建议");
    
    // 测试参数优化场景
    struct ParameterScenario {
        string parameterName;
        double currentValue;
        double performanceIndicator;
        double expectedSuggestedValue;
        string description;
    };
    
    ParameterScenario scenarios[] = {
        {"压缩阈值", 0.9, 45.0, 0.81, "胜率低-降低阈值"},
        {"压缩阈值", 0.6, 75.0, 0.66, "胜率高交易频繁-提高阈值"},
        {"ATR倍数", 2.0, 15.0, 2.4, "回撤高-增加ATR"},
        {"ATR倍数", 3.0, 2.0, 2.7, "回撤低盈利因子低-减少ATR"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        ParameterScenario ps = scenarios[i];
        
        // 模拟参数优化逻辑
        double suggestedValue = ps.currentValue;
        
        if(ps.parameterName == "压缩阈值") {
            if(ps.performanceIndicator < 50.0) {
                suggestedValue = ps.currentValue * 0.9; // 降低阈值
            } else if(ps.performanceIndicator > 70.0) {
                suggestedValue = ps.currentValue * 1.1; // 提高阈值
            }
        } else if(ps.parameterName == "ATR倍数") {
            if(ps.performanceIndicator > 10.0) {
                suggestedValue = ps.currentValue * 1.2; // 增加ATR
            } else if(ps.performanceIndicator < 3.0) {
                suggestedValue = ps.currentValue * 0.9; // 减少ATR
            }
        }
        
        Print("参数优化场景: ", ps.description);
        Print("  参数名称: ", ps.parameterName);
        Print("  当前值: ", DoubleToString(ps.currentValue, 2));
        Print("  性能指标: ", DoubleToString(ps.performanceIndicator, 1));
        Print("  计算建议值: ", DoubleToString(suggestedValue, 2));
        Print("  预期建议值: ", DoubleToString(ps.expectedSuggestedValue, 2));
        
        bool valueCorrect = (MathAbs(suggestedValue - ps.expectedSuggestedValue) < 0.1);
        Print("  建议值验证: ", valueCorrect ? "✓" : "✗");
    }
    
    Print("✅ 参数优化建议测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试性能等级评定                                                  |
//+------------------------------------------------------------------+
void TestPerformanceGrading()
{
    Print("📋 测试5: 性能等级评定");
    
    // 测试不同性能等级场景
    struct GradingScenario {
        double overallScore;
        string expectedGrade;
        string description;
    };
    
    GradingScenario scenarios[] = {
        {95.0, "A+ (卓越)", "卓越表现"},
        {87.0, "A (优秀)", "优秀表现"},
        {82.0, "A- (良好+)", "良好+表现"},
        {77.0, "B+ (良好)", "良好表现"},
        {72.0, "B (中上)", "中上表现"},
        {67.0, "B- (中等+)", "中等+表现"},
        {62.0, "C+ (中等)", "中等表现"},
        {57.0, "C (中下)", "中下表现"},
        {52.0, "C- (较差+)", "较差+表现"},
        {42.0, "D+ (较差)", "较差表现"},
        {32.0, "D (很差)", "很差表现"},
        {22.0, "F (极差)", "极差表现"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        GradingScenario gs = scenarios[i];
        
        // 计算性能等级
        string calculatedGrade = "";
        double score = gs.overallScore;
        
        if(score >= 90.0) calculatedGrade = "A+ (卓越)";
        else if(score >= 85.0) calculatedGrade = "A (优秀)";
        else if(score >= 80.0) calculatedGrade = "A- (良好+)";
        else if(score >= 75.0) calculatedGrade = "B+ (良好)";
        else if(score >= 70.0) calculatedGrade = "B (中上)";
        else if(score >= 65.0) calculatedGrade = "B- (中等+)";
        else if(score >= 60.0) calculatedGrade = "C+ (中等)";
        else if(score >= 55.0) calculatedGrade = "C (中下)";
        else if(score >= 50.0) calculatedGrade = "C- (较差+)";
        else if(score >= 40.0) calculatedGrade = "D+ (较差)";
        else if(score >= 30.0) calculatedGrade = "D (很差)";
        else calculatedGrade = "F (极差)";
        
        Print("性能等级场景: ", gs.description);
        Print("  总体评分: ", DoubleToString(gs.overallScore, 1));
        Print("  计算等级: ", calculatedGrade);
        Print("  预期等级: ", gs.expectedGrade);
        
        bool gradeCorrect = (calculatedGrade == gs.expectedGrade);
        Print("  等级验证: ", gradeCorrect ? "✓" : "✗");
    }
    
    Print("✅ 性能等级评定测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整优化建议流程                                              |
//+------------------------------------------------------------------+
void TestCompleteOptimizationWorkflow()
{
    Print("📋 测试6: 完整优化建议流程");
    
    // 模拟完整的优化建议流程
    Print("模拟优化建议流程:");
    
    // 步骤1: 初始化优化系统
    Print("步骤1: 初始化优化系统");
    Print("  系统状态: 启用");
    Print("  分析间隔: 1小时");
    Print("  最小样本: 20笔交易");
    
    // 步骤2: 执行性能分析
    Print("步骤2: 执行性能分析");
    double profitFactor = TestProfitFactor;
    double winRate = 55.0; // 假设胜率
    double drawdown = 12.0; // 假设回撤
    
    // 计算各项评分
    double performanceScore = 50.0 + (profitFactor >= 1.2 ? 10.0 : -10.0);
    double riskScore = 50.0 + (drawdown <= 10.0 ? 20.0 : -15.0);
    double efficiencyScore = 65.0; // 假设效率评分
    double stabilityScore = 60.0; // 假设稳定性评分
    
    double overallScore = (performanceScore * 0.3 + riskScore * 0.25 + 
                          efficiencyScore * 0.25 + stabilityScore * 0.2);
    
    Print("  性能评分: ", DoubleToString(performanceScore, 1));
    Print("  风险评分: ", DoubleToString(riskScore, 1));
    Print("  效率评分: ", DoubleToString(efficiencyScore, 1));
    Print("  稳定性评分: ", DoubleToString(stabilityScore, 1));
    Print("  总体评分: ", DoubleToString(overallScore, 1));
    
    // 步骤3: 生成优化建议
    Print("步骤3: 生成优化建议");
    int suggestionCount = 0;
    
    if(profitFactor < 1.2) {
        suggestionCount++;
        Print("  建议1: 提升盈利因子");
    }
    if(winRate < 60.0) {
        suggestionCount++;
        Print("  建议2: 提升交易胜率");
    }
    if(drawdown > 10.0) {
        suggestionCount++;
        Print("  建议3: 控制回撤水平");
    }
    
    Print("  建议总数: ", suggestionCount, "条");
    
    // 步骤4: 参数优化建议
    Print("步骤4: 参数优化建议");
    Print("  压缩阈值优化: 当前0.75 → 建议0.68");
    Print("  ATR倍数优化: 当前2.0 → 建议2.4");
    Print("  风险参数优化: 当前2.5% → 建议2.0%");
    
    // 步骤5: 生成优化报告
    Print("步骤5: 生成优化报告");
    string grade = overallScore >= 70.0 ? "B (中上)" : "C+ (中等)";
    string recommendation = overallScore >= 70.0 ? "策略表现良好，可考虑微调优化" : "策略表现一般，建议重点优化";
    
    Print("  性能等级: ", grade);
    Print("  优化建议: ", recommendation);
    
    // 步骤6: 流程验证
    Print("步骤6: 流程验证");
    bool workflowValid = (overallScore >= 0 && suggestionCount >= 0);
    Print("  流程有效性: ", workflowValid ? "✓" : "✗");
    Print("  验证条件: 评分≥0 建议数≥0");
    
    Print("✅ 完整优化建议流程完成\n");
}
