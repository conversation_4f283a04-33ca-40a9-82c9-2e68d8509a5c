#property copyright "Copyright 2023, guapit."
#property link      "https://www.guapit.com"

// 验证交易品种是否存在
string valiSymbol(const string symbom_name)
{
   if(symbom_name == NULL)
      return _Symbol;
   if(!SymbolSelect(symbom_name, true))
      return NULL;
   return symbom_name;
}

// 验证交易手数是否合法
double valiVolume(const double volume,const string symbol_name=NULL)
{
   string m_symbol = valiSymbol(symbol_name);
   if(m_symbol == NULL) return 0.0;
   
   double m_min = SymbolInfoDouble(m_symbol,SYMBOL_VOLUME_MIN);
   double m_max = SymbolInfoDouble(m_symbol,SYMBOL_VOLUME_MAX);
   double m_volume = 0.0;
   if(m_min <= volume || volume <=m_max)
      m_volume = volume;
   else
      m_volume = m_min;

   return NormalizeDouble(m_volume,2);
   
}

// 验证止损点数转换是否合法
double valiSL(const double sl_point,const string symbol_name,const int type, const double price)
{
   string m_symbol = valiSymbol(symbol_name);
   if(m_symbol == NULL) return 0.0;
   if(type < 0 || type > 1) return 0.0;
   if(price <= 0.0 || sl_point <= 0.0)
      return 0.0;
   int m_stop_level = (int)SymbolInfoInteger(m_symbol,SYMBOL_TRADE_STOPS_LEVEL);
   int m_digits = (int)SymbolInfoInteger(m_symbol,SYMBOL_DIGITS);
   double m_point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   double m_sl = sl_point > m_stop_level ? sl_point : m_stop_level;
   double m_stoploss = 0.0;
   if(type == 0)
      m_stoploss = price - m_sl * m_point;
   else if (type == 1)
      m_stoploss = price + m_sl * m_point;
   
   return NormalizeDouble(m_stoploss, m_digits);


}

// 验证止盈点数转换是否合法
double valiTP(const double tp_point,const string symbol_name,const int type, const double price)
{
   string m_symbol = valiSymbol(symbol_name);
   if(m_symbol == NULL) return 0.0;
   if(type < 0 || type > 1) return 0.0;
   if(price <= 0.0 || tp_point <= 0.0)
      return 0.0;
   int m_stop_level = (int)SymbolInfoInteger(m_symbol,SYMBOL_TRADE_STOPS_LEVEL);
   int m_digits = (int)SymbolInfoInteger(m_symbol,SYMBOL_DIGITS);
   double m_point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   double m_tp = tp_point > m_stop_level ? tp_point : m_stop_level;
   double m_takeprofit = 0.0;
   if(type == 0)
      m_takeprofit = price + m_tp * m_point;
   else if (type == 1)
      m_takeprofit = price - m_tp * m_point;
   
   return NormalizeDouble(m_takeprofit, m_digits);

}