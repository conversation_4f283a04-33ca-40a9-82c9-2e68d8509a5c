//+------------------------------------------------------------------+
//| 别提特玛.mq5                                                     |
//| 整合版ATR动态加仓策略EA                                          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, Bittma EA"
#property link      "https://www.bittma.com"
#property version   "1.00"

#include <Trade/Trade.mqh>
#include <Indicators/Indicator.mqh>
#include <Indicators/Trend.mqh>
#include <Indicators/Oscilators.mqh>
#include <Guapit/controls/Button.mqh>

//--- 输入参数
input group "===== 资金管理 ====="
input double FixedLotSize = 0.1;            // 固定手数
input bool   UseCompoundMode = true;        // 使用复利模式
input double BaseEquityPerLot = 1000;       // 每手基础资金
input double MaxRiskPercent = 30.0;         // 最大风险比例(%)
input double MaxPositionSizePercent = 50.0; // 最大仓位占比(%)

input group "===== 加仓策略 ====="
input int    MaxLayers = 10;                // 最大加仓层数
input int    MidLayerStart = 3;             // 中期加仓起始层
input int    LateLayerStart = 6;            // 后期加仓起始层
input double InitialLayerMultiplier = 1.0;  // 初期加仓倍数
input double MidLayerMultiplier = 1.5;      // 中期加仓倍数
input double LateLayerMultiplier = 2.0;     // 后期加仓倍数
input int    ATRPeriod = 14;               // ATR周期
input double ATRMultiplier = 1.5;           // ATR乘数
input double InitialATRMultiplier = 2.0;    // 首次加仓ATR乘数
input double StopLossATRMultiplier = 1.0;   // 止损ATR乘数

input group "===== 止盈策略 ====="
input int    BreakEvenPoints = 50;          // 保本点数
input int    TrailPoints = 50;              // 跟踪止损点数
input double BaseProfitTarget = 100.0;      // 基础盈利目标($)

//--- 全局变量
CTrade       trade;
double       initialEquity;
double       currentEquity;
double       highestEquity;
bool         trendDirection; // true为多头趋势, false为空头趋势
int          currentLayer;
double       lastBuyPrice;
double       lastSellPrice;
int          atrHandle;
int          macdHandle;

//--- 信息面板变量
Button      btnBuy, btnSell;
CLabel       lblEquity, lblBalance, lblProfit, lblPositions, lblATR;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化指标句柄
   atrHandle = iATR(_Symbol, _Period, ATRPeriod);
   macdHandle = iMACD(_Symbol, _Period, 12, 26, 9, PRICE_CLOSE);
   
   // 记录初始净值
   initialEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   currentEquity = initialEquity;
   highestEquity = initialEquity;
   
   // 初始化其他变量
   currentLayer = 0;
   lastBuyPrice = 0;
   lastSellPrice = 0;
   trendDirection = false;
   
   // 设置交易参数
   trade.SetDeviationInPoints(10);
   trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   // 创建信息面板
   CreateInfoPanel();
   
   // 创建交易按钮
   CreateTradeButtons();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 创建账户信息标签
   lblEquity.Create("lblEquity", 0, 10, 10);
   lblEquity.Text("净值: " + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2));
   
   lblBalance.Create("lblBalance", 0, 10, 30);
   lblBalance.Text("余额: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   
   lblProfit.Create("lblProfit", 0, 10, 50);
   lblProfit.Text("浮动盈亏: " + DoubleToString(AccountInfoDouble(ACCOUNT_PROFIT), 2));
   
   // 创建仓位信息标签
   lblPositions.Create("lblPositions", 0, 10, 70);
   lblPositions.Text("持仓: " + IntegerToString(PositionsTotal()) + " 手");
   
   // 创建ATR值标签
   lblATR.Create("lblATR", 0, 10, 90);
   lblATR.Text("当前ATR: " + DoubleToString(GetATRValue(), 5));
}

//+------------------------------------------------------------------+
//| 创建交易按钮                                                     |
//+------------------------------------------------------------------+
void CreateTradeButtons()
{
   // 创建买入按钮
   btnBuy.Create("btnBuy", 0, 150, 10, 80, 30);
   btnBuy.Text("买入");
   btnBuy.Color(clrWhite);
   btnBuy.BackColor(clrGreen);
   
   // 创建卖出按钮
   btnSell.Create("btnSell", 0, 150, 50, 80, 30);
   btnSell.Text("卖出");
   btnSell.Color(clrWhite);
   btnSell.BackColor(clrRed);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   IndicatorRelease(atrHandle);
   IndicatorRelease(macdHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新账户净值
   UpdateEquity();
   
   // 检查账户风险
   CheckAccountRisk();
   
   // 获取当前ATR值
   double atr = GetATRValue();
   
   // 趋势判断
   DetermineTrendDirection();
   
   // 执行交易逻辑
   ExecuteTradingLogic(atr);
   
   // 管理已有订单
   ManageOpenPositions(atr);
}

//+------------------------------------------------------------------+
//| 更新账户净值                                                     |
//+------------------------------------------------------------------+
void UpdateEquity()
{
   currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   if(currentEquity > highestEquity)
      highestEquity = currentEquity;
}

//+------------------------------------------------------------------+
//| 检查账户风险                                                     |
//+------------------------------------------------------------------+
void CheckAccountRisk()
{
   double drawdownPercent = (initialEquity - currentEquity) / initialEquity * 100;
   if(drawdownPercent >= MaxRiskPercent)
   {
      CloseAllPositions();
      Print("账户风险达到 ", MaxRiskPercent, "%, 全部平仓");
   }
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                       |
//+------------------------------------------------------------------+
double GetATRValue()
{
   double atr[1];
   if(CopyBuffer(atrHandle, 0, 0, 1, atr) == 1)
      return atr[0];
   return 0;
}

//+------------------------------------------------------------------+
//| 判断趋势方向                                                     |
//+------------------------------------------------------------------+
void DetermineTrendDirection()
{
   double macdMain[1], macdSignal[1];
   if(CopyBuffer(macdHandle, MAIN_LINE, 0, 1, macdMain) == 1 && 
      CopyBuffer(macdHandle, SIGNAL_LINE, 0, 1, macdSignal) == 1)
   {
      trendDirection = (macdMain[0] > macdSignal[0]);
   }
}

//+------------------------------------------------------------------+
//| 执行交易逻辑                                                     |
//+------------------------------------------------------------------+
void ExecuteTradingLogic(double atr)
{
   // 计算当前手数
   double lotSize = CalculateLotSize();
   
   // 获取当前价格
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // 计算加仓间距
   double spacing = CalculateSpacing(atr, currentLayer);
   
   // 多头加仓逻辑
   if(trendDirection)
   {
      if(lastBuyPrice == 0 || (ask - lastBuyPrice) >= spacing)
      {
         OpenBuyPosition(lotSize, atr);
         lastBuyPrice = ask;
      }
   }
   // 空头加仓逻辑
   else
   {
      if(lastSellPrice == 0 || (lastSellPrice - bid) >= spacing)
      {
         OpenSellPosition(lotSize, atr);
         lastSellPrice = bid;
      }
   }
}

//+------------------------------------------------------------------+
//| 计算手数大小                                                     |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   if(!UseCompoundMode)
      return FixedLotSize;
      
   double lotSize = NormalizeDouble((currentEquity / BaseEquityPerLot) * 0.01, 2);
   return MathMax(lotSize, 0.01); // 最小手数为0.01
}

//+------------------------------------------------------------------+
//| 计算加仓间距                                                     |
//+------------------------------------------------------------------+
double CalculateSpacing(double atr, int layer)
{
   double spacing = atr * ATRMultiplier * _Point;
   
   // 首次加仓使用更大的间距
   if(layer == 0)
      spacing = atr * InitialATRMultiplier * _Point;
   
   // 趋势加速时缩短间距
   if(IsTrendAccelerating())
      spacing *= 0.7; // 缩短30%
   
   return spacing;
}

//+------------------------------------------------------------------+
//| 判断趋势是否加速                                                 |
//+------------------------------------------------------------------+
bool IsTrendAccelerating()
{
   double macdMain[2], macdSignal[2];
   if(CopyBuffer(macdHandle, MAIN_LINE, 0, 2, macdMain) == 2 && 
      CopyBuffer(macdHandle, SIGNAL_LINE, 0, 2, macdSignal) == 2)
   {
      double currentDiff = macdMain[0] - macdSignal[0];
      double previousDiff = macdMain[1] - macdSignal[1];
      return (MathAbs(currentDiff) > MathAbs(previousDiff) * 1.2);
   }
   return false;
}

//+------------------------------------------------------------------+
//| 开多头仓位                                                      |
//+------------------------------------------------------------------+
void OpenBuyPosition(double lotSize, double atr)
{
   // 检查最大层数
   if(currentLayer >= MaxLayers)
      return;
      
   // 检查最大仓位占比
   if(GetTotalPositionSize() > (currentEquity * MaxPositionSizePercent / 100))
      return;
   
   // 计算手数倍数
   double multiplier = GetLayerMultiplier(currentLayer);
   double adjustedLotSize = lotSize * multiplier;
   
   // 计算止损
   double stopLoss = SymbolInfoDouble(_Symbol, SYMBOL_BID) - (atr * StopLossATRMultiplier * _Point);
   
   // 开仓
   if(trade.Buy(adjustedLotSize, _Symbol, 0, stopLoss, 0, NULL))
   {
      currentLayer++;
      Print("开多头仓位, 层数: ", currentLayer, ", 手数: ", adjustedLotSize);
   }
}

//+------------------------------------------------------------------+
//| 开空头仓位                                                      |
//+------------------------------------------------------------------+
void OpenSellPosition(double lotSize, double atr)
{
   // 检查最大层数
   if(currentLayer >= MaxLayers)
      return;
      
   // 检查最大仓位占比
   if(GetTotalPositionSize() > (currentEquity * MaxPositionSizePercent / 100))
      return;
   
   // 计算手数倍数
   double multiplier = GetLayerMultiplier(currentLayer);
   double adjustedLotSize = lotSize * multiplier;
   
   // 计算止损
   double stopLoss = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + (atr * StopLossATRMultiplier * _Point);
   
   // 开仓
   if(trade.Sell(adjustedLotSize, _Symbol, 0, stopLoss, 0, NULL))
   {
      currentLayer++;
      Print("开空头仓位, 层数: ", currentLayer, ", 手数: ", adjustedLotSize);
   }
}

//+------------------------------------------------------------------+
//| 获取当前层数的加仓倍数                                           |
//+------------------------------------------------------------------+
double GetLayerMultiplier(int layer)
{
   if(layer < MidLayerStart)
      return InitialLayerMultiplier;
   else if(layer < LateLayerStart)
      return MidLayerMultiplier;
   else
      return LateLayerMultiplier;
}

//+------------------------------------------------------------------+
//| 获取总仓位大小                                                  |
//+------------------------------------------------------------------+
double GetTotalPositionSize()
{
   double totalVolume = 0;
   int total = PositionsTotal();
   
   for(int i = 0; i < total; i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         totalVolume += PositionGetDouble(POSITION_VOLUME);
      }
   }
   
   return totalVolume;
}

//+------------------------------------------------------------------+
//| 管理已有仓位                                                     |
//+------------------------------------------------------------------+
void ManageOpenPositions(double atr)
{
   int total = PositionsTotal();
   double totalProfit = 0;
   
   // 计算总盈利
   for(int i = 0; i < total; i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         totalProfit += PositionGetDouble(POSITION_PROFIT);
      }
   }
   
   // 动态止盈逻辑
   if(totalProfit >= BaseProfitTarget)
   {
      DynamicTakeProfit(totalProfit, total);
   }
   
   // 移动止损逻辑
   MoveStopLossToBreakEven();
}

//+------------------------------------------------------------------+
//| 动态止盈                                                        |
//+------------------------------------------------------------------+
void DynamicTakeProfit(double totalProfit, int totalPositions)
{
   // 第一止盈阶段
   if(totalProfit >= BaseProfitTarget && totalProfit < BaseProfitTarget * 1.5)
   {
      ClosePercentagePositions(0.3, totalPositions); // 平掉30%仓位
   }
   // 第二止盈阶段
   else if(totalProfit >= BaseProfitTarget * 1.5 && totalProfit < BaseProfitTarget * 2.0)
   {
      ClosePercentagePositions(0.2, totalPositions); // 再平掉20%仓位
      SetTrailingStopForRemaining(0.08); // 剩余仓位设置8%回撤止盈
   }
   // 最终止盈阶段
   else if(totalProfit >= BaseProfitTarget * 2.0)
   {
      ClosePercentagePositions(0.3, totalPositions); // 再平掉30%仓位
      SetTrailingStopForRemaining(0.05); // 剩余20%仓位设置5%回撤止盈
   }
}

//+------------------------------------------------------------------+
//| 按比例平仓                                                      |
//+------------------------------------------------------------------+
void ClosePercentagePositions(double percentage, int totalPositions)
{
   int positionsToClose = (int)MathFloor(totalPositions * percentage);
   if(positionsToClose < 1) return;
   
   for(int i = positionsToClose - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         trade.PositionClose(ticket);
      }
   }
}

//+------------------------------------------------------------------+
//| 为剩余仓位设置回撤止盈                                          |
//+------------------------------------------------------------------+
void SetTrailingStopForRemaining(double drawdownPercent)
{
   int total = PositionsTotal();
   for(int i = 0; i < total; i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double profit = PositionGetDouble(POSITION_PROFIT);
         double newStopLoss = 0;
         
         if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
         {
            newStopLoss = currentPrice - (currentPrice * drawdownPercent / 100);
            if(newStopLoss > PositionGetDouble(POSITION_SL) || PositionGetDouble(POSITION_SL) == 0)
            {
               trade.PositionModify(PositionGetInteger(POSITION_TICKET), newStopLoss, PositionGetDouble(POSITION_TP));
            }
         }
         else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
         {
            newStopLoss = currentPrice + (currentPrice * drawdownPercent / 100);
            if(newStopLoss < PositionGetDouble(POSITION_SL) || PositionGetDouble(POSITION_SL) == 0)
            {
               trade.PositionModify(PositionGetInteger(POSITION_TICKET), newStopLoss, PositionGetDouble(POSITION_TP));
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 移动止损到保本                                                  |
//+------------------------------------------------------------------+
void MoveStopLossToBreakEven()
{
   int total = PositionsTotal();
   for(int i = 0; i < total; i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double profitPoints = 0;
         double newStopLoss = 0;
         
         if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
         {
            profitPoints = (currentPrice - openPrice) / _Point;
            if(profitPoints >= BreakEvenPoints)
            {
               newStopLoss = openPrice + (TrailPoints * _Point);
               if(newStopLoss > PositionGetDouble(POSITION_SL))
               {
                  trade.PositionModify(ticket, newStopLoss, PositionGetDouble(POSITION_TP));
               }
            }
         }
         else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
         {
            profitPoints = (openPrice - currentPrice) / _Point;
            if(profitPoints >= BreakEvenPoints)
            {
               newStopLoss = openPrice - (TrailPoints * _Point);
               if(newStopLoss < PositionGetDouble(POSITION_SL))
               {
                  trade.PositionModify(ticket, newStopLoss, PositionGetDouble(POSITION_TP));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 平掉所有仓位                                                    |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   int total = PositionsTotal();
   for(int i = total - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         trade.PositionClose(ticket);
      }
   }
   currentLayer = 0;
   lastBuyPrice = 0;
   lastSellPrice = 0;
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   // 更新账户信息
   lblEquity.Text("净值: " + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2));
   lblBalance.Text("余额: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   lblProfit.Text("浮动盈亏: " + DoubleToString(AccountInfoDouble(ACCOUNT_PROFIT), 2));
   
   // 更新仓位信息
   lblPositions.Text("持仓: " + IntegerToString(PositionsTotal()) + " 手");
   
   // 更新ATR值
   lblATR.Text("当前ATR: " + DoubleToString(GetATRValue(), 5));
}

//+------------------------------------------------------------------+
//| 按钮点击事件处理                                                 |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // 处理买入按钮点击
   if(id == CHARTEVENT_OBJECT_CLICK && sparam == "btnBuy")
   {
      double lotSize = CalculateLotSize();
      double atr = GetATRValue();
      OpenBuyPosition(lotSize, atr);
   }
   
   // 处理卖出按钮点击
   if(id == CHARTEVENT_OBJECT_CLICK && sparam == "btnSell")
   {
      double lotSize = CalculateLotSize();
      double atr = GetATRValue();
      OpenSellPosition(lotSize, atr);
   }
}