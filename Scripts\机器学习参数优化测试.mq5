//+------------------------------------------------------------------+
//|                                          机器学习参数优化测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "机器学习参数优化功能测试脚本"
#property script_show_inputs

// 测试参数
input int TestCycles = 10;           // 测试优化周期数
input bool EnableDetailedLog = true; // 启用详细日志
input double TargetWinRate = 65.0;   // 目标胜率

//+------------------------------------------------------------------+
//| 机器学习参数优化测试脚本                                          |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始机器学习参数优化测试");
    Print("测试周期数: ", TestCycles);
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("目标胜率: ", TargetWinRate, "%");
    Print("========================================");
    
    // 测试1: 性能评分计算
    TestPerformanceScoreCalculation();
    
    // 测试2: 参数调整策略
    TestParameterAdjustmentStrategies();
    
    // 测试3: 优化模式切换
    TestOptimizationModeSwitching();
    
    // 测试4: 学习率动态调整
    TestLearningRateAdjustment();
    
    // 测试5: 参数边界验证
    TestParameterBoundaryValidation();
    
    // 测试6: 完整优化周期模拟
    TestCompleteOptimizationCycle();
    
    Print("========================================");
    Print("✅ 机器学习参数优化测试完成");
}

//+------------------------------------------------------------------+
//| 测试性能评分计算                                                  |
//+------------------------------------------------------------------+
void TestPerformanceScoreCalculation()
{
    Print("📋 测试1: 性能评分计算");
    
    // 测试不同性能指标组合的评分
    struct PerformanceTestCase {
        double winRate;
        double maxDrawdown;
        double profitFactor;
        double sharpeRatio;
        double totalTrades;
        double expectedScore;
        string description;
    };
    
    PerformanceTestCase testCases[] = {
        {70.0, 5.0, 2.0, 1.8, 50, 85.0, "优秀性能"},
        {65.0, 8.0, 1.5, 1.2, 30, 70.0, "良好性能"},
        {60.0, 12.0, 1.2, 0.8, 20, 50.0, "一般性能"},
        {50.0, 18.0, 0.9, 0.5, 15, 25.0, "较差性能"},
        {40.0, 25.0, 0.7, 0.2, 10, 10.0, "极差性能"}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        PerformanceTestCase tc = testCases[i];
        
        // 计算性能评分
        double calculatedScore = CalculateTestPerformanceScore(tc);
        
        Print("场景: ", tc.description);
        Print("  胜率: ", DoubleToString(tc.winRate, 1), "%");
        Print("  回撤: ", DoubleToString(tc.maxDrawdown, 1), "%");
        Print("  盈利因子: ", DoubleToString(tc.profitFactor, 2));
        Print("  夏普比率: ", DoubleToString(tc.sharpeRatio, 2));
        Print("  交易数: ", DoubleToString(tc.totalTrades, 0));
        Print("  计算评分: ", DoubleToString(calculatedScore, 1));
        Print("  预期评分: ", DoubleToString(tc.expectedScore, 1));
        
        bool scoreCorrect = (MathAbs(calculatedScore - tc.expectedScore) < 15.0);
        Print("  评分验证: ", scoreCorrect ? "✓" : "✗");
    }
    
    Print("✅ 性能评分计算完成\n");
}

//+------------------------------------------------------------------+
//| 计算测试性能评分                                                  |
//+------------------------------------------------------------------+
double CalculateTestPerformanceScore(const PerformanceTestCase &tc) {
    double targetWinRate = TargetWinRate;
    double maxAllowedDrawdown = 12.0;
    double minProfitFactor = 1.5;
    
    // 胜率评分 (权重30%)
    double winRateScore = 0.0;
    if(tc.winRate >= targetWinRate) {
        winRateScore = 30.0;
    } else {
        winRateScore = (tc.winRate / targetWinRate) * 30.0;
    }
    
    // 回撤评分 (权重25%)
    double drawdownScore = 0.0;
    if(tc.maxDrawdown <= maxAllowedDrawdown) {
        drawdownScore = 25.0 * (1.0 - tc.maxDrawdown / maxAllowedDrawdown);
    } else {
        drawdownScore = 0.0;
    }
    
    // 盈利因子评分 (权重20%)
    double profitFactorScore = 0.0;
    if(tc.profitFactor >= minProfitFactor) {
        profitFactorScore = 20.0;
    } else {
        profitFactorScore = (tc.profitFactor / minProfitFactor) * 20.0;
    }
    
    // 夏普比率评分 (权重15%)
    double sharpeScore = MathMin(15.0, tc.sharpeRatio * 7.5);
    
    // 交易频率评分 (权重10%)
    double tradeFrequencyScore = MathMin(10.0, tc.totalTrades / 10.0);
    
    double totalScore = winRateScore + drawdownScore + profitFactorScore + sharpeScore + tradeFrequencyScore;
    
    return MathMax(0.0, MathMin(100.0, totalScore));
}

//+------------------------------------------------------------------+
//| 测试参数调整策略                                                  |
//+------------------------------------------------------------------+
void TestParameterAdjustmentStrategies()
{
    Print("📋 测试2: 参数调整策略");
    
    // 基础参数集
    struct TestParameterSet {
        double volatilityThreshold;
        double bbWidthThreshold;
        double atrThreshold;
        double breakoutWeight;
        double baseDecayRate;
    };
    
    TestParameterSet baseParams = {88.0, 82.0, 85.0, 1.7, 0.05};
    
    Print("基础参数:");
    Print("  波动率阈值: ", DoubleToString(baseParams.volatilityThreshold, 1));
    Print("  布林带阈值: ", DoubleToString(baseParams.bbWidthThreshold, 1));
    Print("  ATR阈值: ", DoubleToString(baseParams.atrThreshold, 1));
    Print("  爆发权重: ", DoubleToString(baseParams.breakoutWeight, 2));
    Print("  衰减率: ", DoubleToString(baseParams.baseDecayRate, 3));
    Print("");
    
    // 测试保守调整
    TestParameterSet conservativeParams = baseParams;
    ApplyTestConservativeAdjustments(conservativeParams);
    
    Print("保守调整后:");
    Print("  波动率阈值: ", DoubleToString(conservativeParams.volatilityThreshold, 1), 
          " (", DoubleToString(conservativeParams.volatilityThreshold - baseParams.volatilityThreshold, 1), ")");
    Print("  布林带阈值: ", DoubleToString(conservativeParams.bbWidthThreshold, 1),
          " (", DoubleToString(conservativeParams.bbWidthThreshold - baseParams.bbWidthThreshold, 1), ")");
    Print("  ATR阈值: ", DoubleToString(conservativeParams.atrThreshold, 1),
          " (", DoubleToString(conservativeParams.atrThreshold - baseParams.atrThreshold, 1), ")");
    Print("  爆发权重: ", DoubleToString(conservativeParams.breakoutWeight, 2),
          " (", DoubleToString(conservativeParams.breakoutWeight - baseParams.breakoutWeight, 2), ")");
    Print("  衰减率: ", DoubleToString(conservativeParams.baseDecayRate, 3),
          " (", DoubleToString(conservativeParams.baseDecayRate - baseParams.baseDecayRate, 3), ")");
    Print("");
    
    // 测试激进调整
    TestParameterSet aggressiveParams = baseParams;
    ApplyTestAggressiveAdjustments(aggressiveParams);
    
    Print("激进调整后:");
    Print("  波动率阈值: ", DoubleToString(aggressiveParams.volatilityThreshold, 1),
          " (", DoubleToString(aggressiveParams.volatilityThreshold - baseParams.volatilityThreshold, 1), ")");
    Print("  布林带阈值: ", DoubleToString(aggressiveParams.bbWidthThreshold, 1),
          " (", DoubleToString(aggressiveParams.bbWidthThreshold - baseParams.bbWidthThreshold, 1), ")");
    Print("  ATR阈值: ", DoubleToString(aggressiveParams.atrThreshold, 1),
          " (", DoubleToString(aggressiveParams.atrThreshold - baseParams.atrThreshold, 1), ")");
    Print("  爆发权重: ", DoubleToString(aggressiveParams.breakoutWeight, 2),
          " (", DoubleToString(aggressiveParams.breakoutWeight - baseParams.breakoutWeight, 2), ")");
    Print("  衰减率: ", DoubleToString(aggressiveParams.baseDecayRate, 3),
          " (", DoubleToString(aggressiveParams.baseDecayRate - baseParams.baseDecayRate, 3), ")");
    
    Print("✅ 参数调整策略完成\n");
}

//+------------------------------------------------------------------+
//| 应用测试保守调整                                                  |
//+------------------------------------------------------------------+
void ApplyTestConservativeAdjustments(TestParameterSet &params) {
    // 提高检测阈值，减少交易频率
    params.volatilityThreshold += 2.0;
    params.bbWidthThreshold += 1.0;
    params.atrThreshold += 2.0;
    
    // 降低紧迫度权重
    params.breakoutWeight *= 0.95;
    
    // 增加衰减速度
    params.baseDecayRate *= 1.1;
}

//+------------------------------------------------------------------+
//| 应用测试激进调整                                                  |
//+------------------------------------------------------------------+
void ApplyTestAggressiveAdjustments(TestParameterSet &params) {
    // 降低检测阈值，增加交易机会
    params.volatilityThreshold -= 1.0;
    params.bbWidthThreshold -= 1.0;
    params.atrThreshold -= 1.0;
    
    // 提高紧迫度权重
    params.breakoutWeight *= 1.05;
    
    // 减少衰减速度
    params.baseDecayRate *= 0.95;
}

//+------------------------------------------------------------------+
//| 测试优化模式切换                                                  |
//+------------------------------------------------------------------+
void TestOptimizationModeSwitching()
{
    Print("📋 测试3: 优化模式切换");
    
    // 测试不同优化模式的学习率
    struct OptimizationModeTest {
        int mode;
        string modeName;
        double expectedLearningRate;
    };
    
    OptimizationModeTest modeTests[] = {
        {0, "保守模式", 0.05},
        {1, "平衡模式", 0.1},
        {2, "激进模式", 0.2},
        {3, "自适应模式", 0.15}
    };
    
    for(int i = 0; i < ArraySize(modeTests); i++) {
        OptimizationModeTest mt = modeTests[i];
        
        // 模拟模式切换
        double learningRate = GetTestLearningRateForMode(mt.mode);
        
        Print("模式: ", mt.modeName);
        Print("  学习率: ", DoubleToString(learningRate, 3));
        Print("  预期学习率: ", DoubleToString(mt.expectedLearningRate, 3));
        
        bool learningRateCorrect = (MathAbs(learningRate - mt.expectedLearningRate) < 0.01);
        Print("  学习率验证: ", learningRateCorrect ? "✓" : "✗");
    }
    
    Print("✅ 优化模式切换完成\n");
}

//+------------------------------------------------------------------+
//| 获取测试模式的学习率                                              |
//+------------------------------------------------------------------+
double GetTestLearningRateForMode(int mode) {
    switch(mode) {
        case 0: return 0.05;  // 保守模式
        case 1: return 0.1;   // 平衡模式
        case 2: return 0.2;   // 激进模式
        case 3: return 0.15;  // 自适应模式
        default: return 0.1;
    }
}

//+------------------------------------------------------------------+
//| 测试学习率动态调整                                                |
//+------------------------------------------------------------------+
void TestLearningRateAdjustment()
{
    Print("📋 测试4: 学习率动态调整");
    
    // 测试不同性能情况下的学习率调整
    struct LearningRateTest {
        double currentPerformance;
        double targetPerformance;
        double baseLearningRate;
        double expectedAdjustment;
        string description;
    };
    
    LearningRateTest lrTests[] = {
        {85.0, 70.0, 0.1, 0.05, "性能超预期，降低学习率"},
        {70.0, 70.0, 0.1, 0.1, "性能达标，保持学习率"},
        {60.0, 70.0, 0.1, 0.15, "性能不足，提高学习率"},
        {45.0, 70.0, 0.1, 0.2, "性能很差，大幅提高学习率"}
    };
    
    for(int i = 0; i < ArraySize(lrTests); i++) {
        LearningRateTest lrt = lrTests[i];
        
        // 计算调整后的学习率
        double adjustedLearningRate = CalculateAdjustedLearningRate(lrt);
        
        Print("场景: ", lrt.description);
        Print("  当前性能: ", DoubleToString(lrt.currentPerformance, 1));
        Print("  目标性能: ", DoubleToString(lrt.targetPerformance, 1));
        Print("  基础学习率: ", DoubleToString(lrt.baseLearningRate, 3));
        Print("  调整后学习率: ", DoubleToString(adjustedLearningRate, 3));
        Print("  预期学习率: ", DoubleToString(lrt.expectedAdjustment, 3));
        
        bool adjustmentCorrect = (MathAbs(adjustedLearningRate - lrt.expectedAdjustment) < 0.02);
        Print("  调整验证: ", adjustmentCorrect ? "✓" : "✗");
    }
    
    Print("✅ 学习率动态调整完成\n");
}

//+------------------------------------------------------------------+
//| 计算调整后的学习率                                                |
//+------------------------------------------------------------------+
double CalculateAdjustedLearningRate(const LearningRateTest &lrt) {
    double performanceRatio = lrt.currentPerformance / lrt.targetPerformance;
    
    if(performanceRatio > 1.2) {
        return lrt.baseLearningRate * 0.5; // 性能超预期，降低学习率
    } else if(performanceRatio > 1.0) {
        return lrt.baseLearningRate; // 性能达标，保持学习率
    } else if(performanceRatio > 0.8) {
        return lrt.baseLearningRate * 1.5; // 性能不足，提高学习率
    } else {
        return lrt.baseLearningRate * 2.0; // 性能很差，大幅提高学习率
    }
}

//+------------------------------------------------------------------+
//| 测试参数边界验证                                                  |
//+------------------------------------------------------------------+
void TestParameterBoundaryValidation()
{
    Print("📋 测试5: 参数边界验证");
    
    // 测试参数边界限制
    struct BoundaryTest {
        double inputValue;
        double minBound;
        double maxBound;
        double expectedOutput;
        string parameterName;
    };
    
    BoundaryTest boundaryTests[] = {
        {75.0, 80.0, 95.0, 80.0, "波动率阈值(下限)"},
        {100.0, 80.0, 95.0, 95.0, "波动率阈值(上限)"},
        {87.0, 80.0, 95.0, 87.0, "波动率阈值(正常)"},
        {0.01, 0.02, 0.10, 0.02, "衰减率(下限)"},
        {0.15, 0.02, 0.10, 0.10, "衰减率(上限)"},
        {0.05, 0.02, 0.10, 0.05, "衰减率(正常)"}
    };
    
    for(int i = 0; i < ArraySize(boundaryTests); i++) {
        BoundaryTest bt = boundaryTests[i];
        
        // 应用边界限制
        double boundedValue = MathMax(bt.minBound, MathMin(bt.maxBound, bt.inputValue));
        
        Print("参数: ", bt.parameterName);
        Print("  输入值: ", DoubleToString(bt.inputValue, 3));
        Print("  边界: [", DoubleToString(bt.minBound, 3), ", ", DoubleToString(bt.maxBound, 3), "]");
        Print("  输出值: ", DoubleToString(boundedValue, 3));
        Print("  预期值: ", DoubleToString(bt.expectedOutput, 3));
        
        bool boundaryCorrect = (MathAbs(boundedValue - bt.expectedOutput) < 0.001);
        Print("  边界验证: ", boundaryCorrect ? "✓" : "✗");
    }
    
    Print("✅ 参数边界验证完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整优化周期模拟                                              |
//+------------------------------------------------------------------+
void TestCompleteOptimizationCycle()
{
    Print("📋 测试6: 完整优化周期模拟");
    
    // 模拟多个优化周期
    double currentScore = 60.0; // 初始性能评分
    double bestScore = currentScore;
    int improvementCount = 0;
    
    Print("初始性能评分: ", DoubleToString(currentScore, 1));
    Print("优化周期模拟:");
    
    for(int cycle = 1; cycle <= TestCycles; cycle++) {
        // 模拟性能变化
        double performanceChange = (MathRand() / 32767.0 - 0.5) * 10.0; // ±5分随机变化
        
        // 应用学习效果 (逐渐改善)
        double learningEffect = cycle * 0.5; // 每周期0.5分改善
        
        double newScore = currentScore + performanceChange + learningEffect;
        newScore = MathMax(0.0, MathMin(100.0, newScore));
        
        // 更新最佳评分
        if(newScore > bestScore) {
            bestScore = newScore;
            improvementCount++;
        }
        
        Print("  周期", cycle, ": 评分", DoubleToString(currentScore, 1), 
              " → ", DoubleToString(newScore, 1),
              " (变化", DoubleToString(newScore - currentScore, 1), ")",
              newScore > currentScore ? " ↑" : " ↓");
        
        currentScore = newScore;
    }
    
    Print("\n优化结果总结:");
    Print("  最终评分: ", DoubleToString(currentScore, 1));
    Print("  最佳评分: ", DoubleToString(bestScore, 1));
    Print("  总体提升: ", DoubleToString(bestScore - 60.0, 1), "分");
    Print("  改善次数: ", improvementCount, "/", TestCycles);
    Print("  改善率: ", DoubleToString((double)improvementCount / TestCycles * 100, 1), "%");
    
    // 验证优化效果
    bool optimizationEffective = (bestScore > 60.0) && (improvementCount >= TestCycles / 3);
    Print("  优化验证: ", optimizationEffective ? "✓" : "✗");
    
    Print("✅ 完整优化周期模拟完成\n");
}
