//+------------------------------------------------------------------+
//|                                                 RSI RECOVERY.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+

// 策略属性声明
#property copyright "Copyright 2024, Simple Forex Tools"
#property link      "https://t.me/simpleforextools"
#property description "RSI恢复策略 - 马丁格尔加仓系统"
#property version   "1.0"
#property strict

// 包含交易类库
#include <Trade/trade.mqh>
CTrade obj_Trade;

// 图表对象名称常量
string ZONE_L = "ZL";    // 做多区域线
string ZONE_H ="ZH";     // 做空区域线
string ZONE_T_H = "ZTH"; // 多单目标线
string ZONE_T_L ="ZTL";  // 空单目标线

// 输入参数(策略配置)
input string SimpleForexTeam__ = "免费外汇工具! 加入频道获取更多EA";
input string The_our_telegram = "https://t.me/simpleforextools";
input string The_best_VPS = "https://t.me/simpleforextools/2290";
input double LotSize = 0.01;             // 初始手数
input double CloseWhenInProfit = 20;     // 整体金额盈利平仓
input double sellzone=100;               // RSI超卖
input double buyzone=35;                 // RSI超买
input ENUM_TIMEFRAMES RsiTimeframe= PERIOD_M1; // RSI周期
input int RsiPeriod=14;                  // RSI周期参数
input ENUM_APPLIED_PRICE RsiAppliedPrice=PRICE_CLOSE; // RSI计算价格
input int InpMagicNumber =897654;        // 魔术号

// 全局变量
int handleRsi;            // RSI指标句柄
double Rsi[];             // RSI值数组
int barsTotal=0;          // 已处理K线计数
double zoneLow=0;         // 做多触发价格区域下限
double zoneHigh=0;        // 做空触发价格区域上限
double zoneTargetLow=0;   // 空单目标价格
double zoneTargetHigh=0;  // 多单目标价格

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
int OnInit()
{
   obj_Trade.SetExpertMagicNumber(InpMagicNumber);
   handleRsi=iRSI(_Symbol,RsiTimeframe,RsiPeriod,RsiAppliedPrice);
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 主行情处理函数                                                   |
//+------------------------------------------------------------------+
void OnTick()
{
   double zoneRange=2000*_Point; // 交易区域宽度(20点)
   double zoneTarget=4000*_Point; // 目标利润区域(40点)

   // 新K线检测
   int bars=iBars(_Symbol,RsiTimeframe);
   if(barsTotal==bars) return;
   barsTotal=bars;

   // 获取RSI数据
   if(!CopyBuffer(handleRsi,0,1,2,Rsi)) return;

   // 多单信号处理：RSI从超卖区上穿
   if(Rsi[1]<buyzone && Rsi[0]>buyzone)
   {
      obj_Trade.Buy(LotSize);
      ulong PosTkt=obj_Trade.ResultOrder();
      
      if(PosTkt>0 && PositionSelectByTicket(PosTkt))
      {
         double OpenPrice= PositionGetDouble(POSITION_PRICE_OPEN);
         // 设置做多区域和目标
         zoneHigh= NormalizeDouble(OpenPrice,_Digits);
         zoneLow= NormalizeDouble(zoneHigh-zoneRange,_Digits);
         zoneTargetHigh= NormalizeDouble(zoneHigh+zoneTarget,_Digits);
         zoneTargetLow=NormalizeDouble(zoneLow-zoneTarget,_Digits);
         
         // 在图表绘制区域线
         drawZoneLevel(ZONE_H,zoneHigh,clrCyan,2);
         drawZoneLevel(ZONE_L,zoneLow,clrCyan,2);             
         drawZoneLevel(ZONE_T_H,zoneTargetHigh,clrBlue,2); 
         drawZoneLevel(ZONE_T_L,zoneTargetLow,clrBlue,2);
      }
   }
   // 空单信号处理：RSI从超买区下穿
   else if(Rsi[1]>sellzone && Rsi[0]<sellzone)
   {
      obj_Trade.Sell(LotSize);
      ulong PosTkt=obj_Trade.ResultOrder();
      
      if(PosTkt>0 && PositionSelectByTicket(PosTkt))
      {
         double OpenPrice= PositionGetDouble(POSITION_PRICE_OPEN);
         // 设置做空区域和目标
         zoneLow= NormalizeDouble(OpenPrice,_Digits);
         zoneHigh= NormalizeDouble(zoneLow+zoneRange,_Digits);
         zoneTargetHigh= NormalizeDouble(zoneHigh+zoneTarget,_Digits);
         zoneTargetLow=NormalizeDouble(zoneLow-zoneTarget,_Digits);
      }
   }

   // 整体盈利平仓检查
   if(GetPositionProfit() >= CloseWhenInProfit)
      CloseAllBuySell();
}

//+------------------------------------------------------------------+
//| 绘制水平区域线函数                                               |
//+------------------------------------------------------------------+
void drawZoneLevel(string LevelName,double Price,color clr,int Width)
{
   ObjectCreate(0,LevelName,OBJ_HLINE,0,TimeCurrent(),Price);
   ObjectSetInteger(0,LevelName,OBJPROP_COLOR,clr);
   ObjectSetInteger(0,LevelName,OBJPROP_WIDTH,Width);
}

//+------------------------------------------------------------------+
//| 平仓所有持仓函数                                                 |
//+------------------------------------------------------------------+
void CloseAllBuySell(ENUM_POSITION_TYPE PosType = NULL)
{
    // 倒序遍历所有持仓
    for (int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if (!PositionSelectByTicket(PositionGetTicket(i))) continue;
        
        // 过滤非本EA的订单
        if (PositionGetString(POSITION_SYMBOL) != Symbol()) continue;
        if (PositionGetInteger(POSITION_MAGIC) != InpMagicNumber) continue;
        
        // 尝试平仓最多10次
        for (int try = 0; try < 10; try++)
        {
            if (obj_Trade.PositionClose(PositionGetInteger(POSITION_TICKET))) break;
        }
    }
}

//+------------------------------------------------------------------+
//| 获取总体持仓利润函数                                             |
//+------------------------------------------------------------------+
double GetPositionProfit()
{
   double posProfit = 0;
   // 遍历所有持仓累计利润
   for(int i = PositionsTotal()-1; i>=0; i--) {
      if(PositionSelectByTicket(PositionGetTicket(i))) {
         posProfit += PositionGetDouble(POSITION_PROFIT);
      }
   }
   return posProfit;
}