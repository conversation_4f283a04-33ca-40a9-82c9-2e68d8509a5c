# 导入MT5的模块
import MetaTrader5 as mt5
from .trade import val_volume, val_stoploss, val_takeprofit
import datetime


# 查询指定的挂单是否存在
def find_order(group, comment, magic):
    orders = mt5.orders_get(group=group)
    if not orders:
        return False
    for order in orders:
        m_order = order._asdict()
        if m_order['comment'] == comment and m_order['magic'] == magic:
            return True

    return False


# 统计指定条件挂单的数量
def order_count(group, type, magic):
    count = 0
    orders = mt5.orders_get(group=group)
    if not orders:
        return 0
    for order in orders:
        m_order = order._asdict()
        if m_order['type'] == type and m_order['magic'] == magic:
            count += 1
        elif type == 12 and m_order['magic'] == magic:
            count += 1
    return count


def buy_limit(symbol, price, volume, sl, tp, deviation, stop_limit, type_time, expiration, comment, magic):
    return order_sand(symbol, mt5.ORDER_TYPE_BUY_LIMIT, price, volume, sl, tp, deviation, stop_limit, type_time,
                      expiration, comment, magic)


def sell_limit(symbol, price, volume, sl, tp, deviation, stop_limit, type_time, expiration, comment, magic):
    return order_sand(symbol, mt5.ORDER_TYPE_SELL_LIMIT, price, volume, sl, tp, deviation, stop_limit, type_time,
                      expiration, comment, magic)


# 开挂单
def order_sand(symbol, type, price, volume, sl, tp, deviation, stop_limit, type_time, expiration, comment, magic):
    # 获取交易品种信息
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    if not symbol_info:
        return False
    # 检查订单是否存在
    if find_order(symbol, comment, magic):
        return False

    if type == mt5.ORDER_TYPE_BUY_LIMIT:
        if price > symbol_info.ask:
            return False
    elif type == mt5.ORDER_TYPE_SELL_LIMIT:
        if price < symbol_info.bid:
            return False
    elif type == mt5.ORDER_TYPE_BUY_STOP:
        if price < symbol_info.ask:
            return False
    elif type == mt5.ORDER_TYPE_SELL_STOP:
        if price > symbol_info.bid:
            return False
    # elif type == mt5.ORDER_TYPE_BUY_STOP_LIMIT:
    #     if price > symbol_info.ask:
    #         return False
    # elif type == mt5.ORDER_TYPE_SELL_STOP_LIMIT:
    #     if price < symbol_info.bid:
    #         return False
    # else:
    #     return False
    m_tp = type % 2
    m_sl = val_stoploss(symbol, m_tp, price, sl)
    m_tp = val_takeprofit(symbol, m_tp, price, tp)

    m_volume = val_volume(symbol, volume)

    request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": symbol,
        "type": type,
        "price": price,
        "volume": m_volume,
        "deviation": deviation,
        "comment": comment,
        "magic": magic,
        "type_filling": mt5.ORDER_FILLING_IOC,
        "stop_limit": stop_limit,
        "type_time": type_time,
        "expiration": expiration
    }

    if m_sl != 0:
        request['sl'] = m_sl
    if m_tp != 0:
        request['tp'] = m_tp

    result = mt5.order_send(request)

    if not result:
        print("交易请求发送失败, 错误原因: ", mt5.last_error())
        return False
    else:
        return True