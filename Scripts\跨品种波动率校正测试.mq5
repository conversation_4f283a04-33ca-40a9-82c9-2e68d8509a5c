//+------------------------------------------------------------------+
//|                                          跨品种波动率校正测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "跨品种波动率校正功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input bool TestAllAssetClasses = true; // 测试所有资产类别

//+------------------------------------------------------------------+
//| 跨品种波动率校正测试脚本                                          |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始跨品种波动率校正测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("测试所有资产类别: ", TestAllAssetClasses ? "是" : "否");
    Print("========================================");
    
    // 测试1: 资产类别识别
    TestAssetClassification();
    
    // 测试2: 校正因子应用
    TestCorrectionFactorApplication();
    
    // 测试3: 风险乘数调整
    TestRiskMultiplierAdjustment();
    
    // 测试4: 订单参数优化
    TestOrderParameterOptimization();
    
    // 测试5: 适应性评分计算
    TestAdaptabilityScoreCalculation();
    
    // 测试6: 完整校正流程
    TestCompleteCorrectionWorkflow();
    
    Print("========================================");
    Print("✅ 跨品种波动率校正测试完成");
}

//+------------------------------------------------------------------+
//| 测试资产类别识别                                                  |
//+------------------------------------------------------------------+
void TestAssetClassification()
{
    Print("📋 测试1: 资产类别识别");
    
    // 测试品种列表
    struct AssetTestCase {
        string symbol;
        int expectedClass;
        string expectedDescription;
        double expectedCorrectionFactor;
    };
    
    AssetTestCase testCases[] = {
        // 外汇
        {"EURUSD", 0, "外汇", 0.8},
        {"GBPJPY", 0, "外汇", 0.8},
        {"USDCAD", 0, "外汇", 0.8},
        
        // 加密货币
        {"BTCUSDT", 1, "加密货币", 1.3},
        {"ETHUSDT", 1, "加密货币", 1.3},
        {"ADAUSDT", 1, "加密货币", 1.3},
        
        // 商品
        {"XAUUSD", 2, "商品", 1.0},
        {"XAGUSD", 2, "商品", 1.0},
        {"OILUSD", 2, "商品", 1.0},
        
        // 指数
        {"SP500", 3, "指数", 0.9},
        {"NASDAQ", 3, "指数", 0.9},
        {"DAX", 3, "指数", 0.9},
        
        // 股票
        {"AAPL", 4, "股票", 1.1},
        {"TSLA", 4, "股票", 1.1},
        {"MSFT", 4, "股票", 1.1},
        
        // 未知
        {"UNKNOWN", 5, "未知", 1.0}
    };
    
    for(int i = 0; i < ArraySize(testCases); i++) {
        AssetTestCase tc = testCases[i];
        
        // 模拟资产分类逻辑
        int classifiedClass = ClassifyTestAsset(tc.symbol);
        string classDescription = GetTestAssetDescription(classifiedClass);
        double correctionFactor = GetTestCorrectionFactor(classifiedClass);
        
        Print("品种: ", tc.symbol);
        Print("  分类结果: ", classDescription, " (", classifiedClass, ")");
        Print("  预期分类: ", tc.expectedDescription, " (", tc.expectedClass, ")");
        Print("  校正因子: ", DoubleToString(correctionFactor, 2));
        Print("  预期因子: ", DoubleToString(tc.expectedCorrectionFactor, 2));
        
        bool classificationCorrect = (classifiedClass == tc.expectedClass);
        bool factorCorrect = (MathAbs(correctionFactor - tc.expectedCorrectionFactor) < 0.01);
        
        Print("  分类验证: ", classificationCorrect ? "✓" : "✗");
        Print("  因子验证: ", factorCorrect ? "✓" : "✗");
    }
    
    Print("✅ 资产类别识别完成\n");
}

//+------------------------------------------------------------------+
//| 分类测试资产                                                      |
//+------------------------------------------------------------------+
int ClassifyTestAsset(string symbol) {
    string upperSymbol = StringToUpper(symbol);
    
    // 外汇识别
    if(StringFind(upperSymbol, "EUR") >= 0 || StringFind(upperSymbol, "USD") >= 0 ||
       StringFind(upperSymbol, "GBP") >= 0 || StringFind(upperSymbol, "JPY") >= 0 ||
       StringFind(upperSymbol, "CHF") >= 0 || StringFind(upperSymbol, "CAD") >= 0) {
        return 0; // ASSET_FOREX
    }
    
    // 加密货币识别
    if(StringFind(upperSymbol, "BTC") >= 0 || StringFind(upperSymbol, "ETH") >= 0 ||
       StringFind(upperSymbol, "ADA") >= 0 || StringFind(upperSymbol, "USDT") >= 0) {
        return 1; // ASSET_CRYPTO
    }
    
    // 商品识别
    if(StringFind(upperSymbol, "XAU") >= 0 || StringFind(upperSymbol, "XAG") >= 0 ||
       StringFind(upperSymbol, "OIL") >= 0 || StringFind(upperSymbol, "GOLD") >= 0) {
        return 2; // ASSET_COMMODITY
    }
    
    // 指数识别
    if(StringFind(upperSymbol, "SP500") >= 0 || StringFind(upperSymbol, "NASDAQ") >= 0 ||
       StringFind(upperSymbol, "DAX") >= 0 || StringFind(upperSymbol, "INDEX") >= 0) {
        return 3; // ASSET_INDEX
    }
    
    // 股票识别 (简化判断)
    if(StringLen(upperSymbol) >= 3 && StringLen(upperSymbol) <= 5 &&
       StringFind(upperSymbol, "USD") < 0 && StringFind(upperSymbol, "BTC") < 0) {
        return 4; // ASSET_STOCK
    }
    
    return 5; // ASSET_UNKNOWN
}

//+------------------------------------------------------------------+
//| 获取测试资产描述                                                  |
//+------------------------------------------------------------------+
string GetTestAssetDescription(int assetClass) {
    switch(assetClass) {
        case 0: return "外汇";
        case 1: return "加密货币";
        case 2: return "商品";
        case 3: return "指数";
        case 4: return "股票";
        case 5: return "未知";
        default: return "错误";
    }
}

//+------------------------------------------------------------------+
//| 获取测试校正因子                                                  |
//+------------------------------------------------------------------+
double GetTestCorrectionFactor(int assetClass) {
    switch(assetClass) {
        case 0: return 0.8;  // 外汇
        case 1: return 1.3;  // 加密货币
        case 2: return 1.0;  // 商品
        case 3: return 0.9;  // 指数
        case 4: return 1.1;  // 股票
        case 5: return 1.0;  // 未知
        default: return 1.0;
    }
}

//+------------------------------------------------------------------+
//| 测试校正因子应用                                                  |
//+------------------------------------------------------------------+
void TestCorrectionFactorApplication()
{
    Print("📋 测试2: 校正因子应用");
    
    // 测试不同波动率的校正效果
    struct CorrectionTest {
        double originalVolatility;
        int assetClass;
        double expectedCorrectedVolatility;
        string description;
    };
    
    CorrectionTest tests[] = {
        {20.0, 0, 16.0, "外汇20%波动率校正"},
        {30.0, 1, 39.0, "加密货币30%波动率校正"},
        {25.0, 2, 25.0, "商品25%波动率无校正"},
        {18.0, 3, 16.2, "指数18%波动率校正"},
        {22.0, 4, 24.2, "股票22%波动率校正"}
    };
    
    for(int i = 0; i < ArraySize(tests); i++) {
        CorrectionTest ct = tests[i];
        
        // 应用校正
        double correctionFactor = GetTestCorrectionFactor(ct.assetClass);
        double correctedVolatility = ct.originalVolatility * correctionFactor;
        
        Print("场景: ", ct.description);
        Print("  原始波动率: ", DoubleToString(ct.originalVolatility, 1), "%");
        Print("  校正因子: ", DoubleToString(correctionFactor, 2));
        Print("  校正波动率: ", DoubleToString(correctedVolatility, 1), "%");
        Print("  预期波动率: ", DoubleToString(ct.expectedCorrectedVolatility, 1), "%");
        
        bool correctionCorrect = (MathAbs(correctedVolatility - ct.expectedCorrectedVolatility) < 0.1);
        Print("  校正验证: ", correctionCorrect ? "✓" : "✗");
    }
    
    Print("✅ 校正因子应用完成\n");
}

//+------------------------------------------------------------------+
//| 测试风险乘数调整                                                  |
//+------------------------------------------------------------------+
void TestRiskMultiplierAdjustment()
{
    Print("📋 测试3: 风险乘数调整");
    
    // 测试不同资产类别的风险调整
    double baseUrgency = 60.0;
    double riskMultipliers[] = {0.8, 1.5, 1.0, 0.9, 1.1, 1.0}; // 对应各资产类别
    string assetNames[] = {"外汇", "加密货币", "商品", "指数", "股票", "未知"};
    
    Print("基础紧迫度: ", DoubleToString(baseUrgency, 1));
    Print("");
    
    for(int i = 0; i < 6; i++) {
        double riskMultiplier = riskMultipliers[i];
        double adjustedUrgency = baseUrgency * riskMultiplier;
        adjustedUrgency = MathMax(0.0, MathMin(100.0, adjustedUrgency));
        
        Print("资产类别: ", assetNames[i]);
        Print("  风险乘数: ", DoubleToString(riskMultiplier, 2));
        Print("  调整紧迫度: ", DoubleToString(adjustedUrgency, 1));
        
        // 计算调整幅度
        double adjustmentPercent = (adjustedUrgency - baseUrgency) / baseUrgency * 100;
        Print("  调整幅度: ", DoubleToString(adjustmentPercent, 1), "%");
        
        // 验证风险调整逻辑
        bool riskAdjustmentCorrect = true;
        if(i == 0 && adjustedUrgency >= baseUrgency) riskAdjustmentCorrect = false; // 外汇应降低
        if(i == 1 && adjustedUrgency <= baseUrgency) riskAdjustmentCorrect = false; // 加密货币应提高
        
        Print("  风险验证: ", riskAdjustmentCorrect ? "✓" : "✗");
    }
    
    Print("✅ 风险乘数调整完成\n");
}

//+------------------------------------------------------------------+
//| 测试订单参数优化                                                  |
//+------------------------------------------------------------------+
void TestOrderParameterOptimization()
{
    Print("📋 测试4: 订单参数优化");
    
    // 基础订单参数
    struct BaseOrderParams {
        int orderCount;
        int intervalSeconds;
        double orderSize;
    };
    
    BaseOrderParams baseParams = {5, 600, 0.05};
    
    Print("基础参数: ", baseParams.orderCount, "个订单, ", 
          baseParams.intervalSeconds, "秒间隔, ", 
          DoubleToString(baseParams.orderSize, 3), "手");
    Print("");
    
    // 测试各资产类别的参数优化
    string assetNames[] = {"外汇", "加密货币", "商品", "指数", "股票", "未知"};
    
    for(int i = 0; i < 6; i++) {
        // 模拟资产优化逻辑
        int optimizedCount = baseParams.orderCount;
        int optimizedInterval = baseParams.intervalSeconds;
        double optimizedSize = baseParams.orderSize;
        
        switch(i) {
            case 0: // 外汇
                optimizedCount = (int)(optimizedCount * 1.1);
                break;
            case 1: // 加密货币
                optimizedCount = (int)(optimizedCount * 0.8);
                optimizedInterval = (int)(optimizedInterval * 1.2);
                optimizedSize *= 0.8;
                break;
            case 2: // 商品
                // 无调整
                break;
            case 3: // 指数
                optimizedCount = (int)(optimizedCount * 1.2);
                break;
            case 4: // 股票
                optimizedCount = (int)(optimizedCount * 0.9);
                optimizedSize *= 1.1;
                break;
            case 5: // 未知
                optimizedCount = (int)(optimizedCount * 0.9);
                optimizedInterval = (int)(optimizedInterval * 1.1);
                break;
        }
        
        // 确保参数在合理范围内
        optimizedCount = MathMax(1, MathMin(10, optimizedCount));
        optimizedInterval = MathMax(300, MathMin(900, optimizedInterval));
        optimizedSize = MathMax(0.01, MathMin(0.1, optimizedSize));
        
        Print("资产类别: ", assetNames[i]);
        Print("  订单数量: ", baseParams.orderCount, " → ", optimizedCount);
        Print("  订单间隔: ", baseParams.intervalSeconds, "秒 → ", optimizedInterval, "秒");
        Print("  订单规模: ", DoubleToString(baseParams.orderSize, 3), "手 → ", 
              DoubleToString(optimizedSize, 3), "手");
        
        // 验证参数合理性
        bool paramsValid = (optimizedCount >= 1 && optimizedCount <= 10) &&
                          (optimizedInterval >= 300 && optimizedInterval <= 900) &&
                          (optimizedSize >= 0.01 && optimizedSize <= 0.1);
        Print("  参数验证: ", paramsValid ? "✓" : "✗");
    }
    
    Print("✅ 订单参数优化完成\n");
}

//+------------------------------------------------------------------+
//| 测试适应性评分计算                                                |
//+------------------------------------------------------------------+
void TestAdaptabilityScoreCalculation()
{
    Print("📋 测试5: 适应性评分计算");
    
    // 各资产类别的基础评分
    double baseScores[] = {95.0, 85.0, 90.0, 92.0, 88.0, 75.0};
    double correctionFactors[] = {0.8, 1.3, 1.0, 0.9, 1.1, 1.0};
    string assetNames[] = {"外汇", "加密货币", "商品", "指数", "股票", "未知"};
    
    for(int i = 0; i < 6; i++) {
        double baseScore = baseScores[i];
        double correctionFactor = correctionFactors[i];
        
        // 计算校正效果加分
        double correctionEffectiveness = MathAbs(correctionFactor - 1.0);
        double correctionBonus = correctionEffectiveness * 10.0;
        
        double finalScore = baseScore + correctionBonus;
        finalScore = MathMax(0.0, MathMin(100.0, finalScore));
        
        Print("资产类别: ", assetNames[i]);
        Print("  基础评分: ", DoubleToString(baseScore, 1));
        Print("  校正效果: ", DoubleToString(correctionEffectiveness, 2));
        Print("  校正加分: ", DoubleToString(correctionBonus, 1));
        Print("  最终评分: ", DoubleToString(finalScore, 1), "/100");
        
        // 验证评分合理性
        bool scoreValid = (finalScore >= 0.0 && finalScore <= 100.0);
        Print("  评分验证: ", scoreValid ? "✓" : "✗");
    }
    
    Print("✅ 适应性评分计算完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整校正流程                                                  |
//+------------------------------------------------------------------+
void TestCompleteCorrectionWorkflow()
{
    Print("📋 测试6: 完整校正流程");
    
    // 模拟完整的校正流程
    string testSymbols[] = {"EURUSD", "BTCUSDT", "XAUUSD"};
    
    for(int i = 0; i < ArraySize(testSymbols); i++) {
        string symbol = testSymbols[i];
        
        Print("=== 品种: ", symbol, " ===");
        
        // 1. 资产分类
        int assetClass = ClassifyTestAsset(symbol);
        string assetDescription = GetTestAssetDescription(assetClass);
        
        // 2. 获取校正参数
        double correctionFactor = GetTestCorrectionFactor(assetClass);
        double riskMultiplier = (assetClass == 0) ? 0.8 : 
                               (assetClass == 1) ? 1.5 : 1.0;
        
        // 3. 应用波动率校正
        double originalVolatility = 20.0 + i * 5.0; // 模拟不同波动率
        double correctedVolatility = originalVolatility * correctionFactor;
        
        // 4. 应用风险调整
        double originalUrgency = 60.0;
        double adjustedUrgency = originalUrgency * riskMultiplier;
        adjustedUrgency = MathMax(0.0, MathMin(100.0, adjustedUrgency));
        
        // 5. 计算适应性评分
        double baseScore = (assetClass == 0) ? 95.0 : 
                          (assetClass == 1) ? 85.0 : 90.0;
        double correctionBonus = MathAbs(correctionFactor - 1.0) * 10.0;
        double adaptabilityScore = MathMin(100.0, baseScore + correctionBonus);
        
        // 输出结果
        Print("1. 资产分类: ", assetDescription);
        Print("2. 校正因子: ", DoubleToString(correctionFactor, 2));
        Print("3. 波动率校正: ", DoubleToString(originalVolatility, 1), "% → ", 
              DoubleToString(correctedVolatility, 1), "%");
        Print("4. 风险调整: ", DoubleToString(originalUrgency, 1), " → ", 
              DoubleToString(adjustedUrgency, 1));
        Print("5. 适应性评分: ", DoubleToString(adaptabilityScore, 1), "/100");
        
        // 验证流程完整性
        bool workflowComplete = (assetClass >= 0) && (correctionFactor > 0) && 
                               (correctedVolatility > 0) && (adaptabilityScore > 0);
        Print("6. 流程验证: ", workflowComplete ? "✓" : "✗");
        Print("");
    }
    
    Print("✅ 完整校正流程完成\n");
}
