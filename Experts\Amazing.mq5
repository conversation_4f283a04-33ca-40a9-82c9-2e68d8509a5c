//+------------------------------------------------------------------+
//|                                                          Amazing |
//|                                  Copyright © 2023, EarnForex.com |
//|                                       https://www.earnforex.com/ |
//|                                 Based on the EA by FiFtHeLeMeNt. |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2023, EarnForex"
#property link      "https://www.earnforex.com/metatrader-expert-advisors/Amazing/"
#property version   "1.04"

#property description "Amazing - 自动交易EA"
#property description "在价格突破最近高低点时自动开仓"
#property description "系统将自动设置双向挂单(买入和卖出)"
#property description "使用获利目标、保本价和追踪止损来管理交易"

#include <Trade/Trade.mqh>
#include <errordescription.mqh>

input group "主要参数"
input int EntryDistance = 100;            // EntryDistance: 从最近高/低点的入场距离(点值)
input int StopLoss = 200;                 // StopLoss: 止损点数
input int TakeProfit = 200;               // TakeProfit: 获利点数
input int CTCBN = 0;                      // CTCBN: 计算高低点的K线数量
input bool OCO = true;                    // OCO: 一个订单成交后EA将取消另一个挂单
input int BEPoints = 0;                   // BEPoints: EA将止损移至保本+1点的利润点数
input int TrailingStop = 0;               // 追踪止损点数

input group "ATR参数"
input bool UseATR = false;                // 使用基于ATR的止损和获利水平
input int ATR_Period = 14;                // ATR周期
input double ATR_Multiplier_SL = 5;       // 止损的ATR乘数
input double ATR_Multiplier_TP = 5;       // 获利的ATR乘数

input group "资金管理"
input double Lots = 0.01;                 // 交易手数
input bool MM = true;                     // 资金管理，若为true则基于止损计算仓位大小
input double Risk = 1;                    // 风险 - 百分比风险承受度
input double FixedBalance = 0;            // FixedBalance: 若>0，使用此金额计算交易规模
input double MoneyRisk = 0;               // MoneyRisk: 账户货币的风险承受度
input bool UseMoneyInsteadOfPercentage = false; // 使用金额风险代替百分比
input bool UseEquityInsteadOfBalance = false;   // 使用净值代替余额

input group "其他"
input string TradeLog = "Am_Log_";        // TradeLog: 日志文件前缀
input string Commentary = "Amazing";       // Commentary: 交易描述

// 全局变量:
double buy_stop_entry, sell_stop_entry, buy_stop_loss, sell_stop_loss, buy_take_profit, sell_take_profit;
int Magic;
string filename;

double SL, TP;
int lot_decimal_places;
int ATR_handle;
double RiskMoney;
string AccountCurrency = "";
string ProfitCurrency = "";
string BaseCurrency = "";
ENUM_SYMBOL_CALC_MODE CalcMode;
string ReferencePair = NULL;
bool ReferenceSymbolMode;

// 主要交易对象:
CTrade *Trade;
CPositionInfo PositionInfo;
COrderInfo OrderInfo;

//+------------------------------------------------------------------+
//| 初始化函数                                                         |
//+------------------------------------------------------------------+
void OnInit()
{
    Magic = (int)TimeCurrent(); // 动态生成Magic数字，允许多个实例用于不同的新闻公告

    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    lot_decimal_places = CountDecimalPlaces(lot_step);
    Print("最小手数: ", DoubleToString(min_lot, 2), ", 手数步长: ", DoubleToString(lot_step, lot_decimal_places), ".");
    if ((Lots < min_lot) && (!MM)) Alert("手数不应小于: ", DoubleToString(min_lot, lot_decimal_places), ".");

    if (StringLen(Commentary) > 0)
    {
        MqlDateTime dt;
        TimeCurrent(dt);
        filename = TradeLog + Symbol() + "-" + IntegerToString(dt.mon) + "-" + IntegerToString(dt.day) + ".txt";
    }
    
    if (UseATR) ATR_handle = iATR(NULL, 0, ATR_Period);

    // 如果UseATR = false，将使用这些值。否则，稍后将计算ATR值
    SL = StopLoss;
    TP = TakeProfit;
    
    // 初始化Trade类对象
    Trade = new CTrade;
    Trade.SetExpertMagicNumber(Magic);
}

//+------------------------------------------------------------------+
//| 反初始化函数                                                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Comment("");
    delete Trade;
}

//+------------------------------------------------------------------+
//| 检查当前订单和持仓情况                                              |
//| 结果模式                                                           |
//| 1    1    1    1                                                   |
//| |    |    |    |                                                   |
//| |    |    |    -------- 卖出止损订单                                |
//| |    |    |    -------- 买入止损订单                                     |
//| |    |    -------- 卖出持仓                                              |
//| |    |    -------- 买入持仓                                                   |
//+------------------------------------------------------------------+
int CheckOrdersCondition()
{
    int result = 0;

    // 首先，检查订单
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (!OrderSelect(OrderGetTicket(i)))
        {
            {
                Write(__FUNCTION__ + " | 选择订单时出错: " + ErrorDescription(GetLastError()));
                continue;
            }
        }
        if ((OrderGetString(ORDER_SYMBOL) != Symbol()) || (OrderGetInteger(ORDER_MAGIC) != Magic)) continue;
        
        if (OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_BUY_STOP)
        {
            result = result + 10;
        }
        else if (OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_SELL_STOP)
        {
            result = result + 1;
        }
    }

    // 其次，检查持仓
    for (int i = 0; i < PositionsTotal(); i++)
    {
        if (!PositionSelectByTicket(PositionGetTicket(i)))
        {
            {
                Write(__FUNCTION__ + " | 选择持仓时出错: " + ErrorDescription(GetLastError()));
                continue;
            }
        }
        if ((PositionGetString(POSITION_SYMBOL) != Symbol()) || (PositionGetInteger(POSITION_MAGIC) != Magic)) continue;

        if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
        {
            result = result + 1000;
        }
        else if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
        {
            result = result + 100;
        }
    }
    return result; // 0表示没有订单/持仓
}

//+------------------------------------------------------------------+
//| 开立买入止损订单                                                    |
//+------------------------------------------------------------------+
void OpenBuyStop()
{
    for (int tries = 0; tries < 10; tries++)
    {
        Trade.OrderOpen(_Symbol, ORDER_TYPE_BUY_STOP, LotsOptimized(ORDER_TYPE_BUY, buy_stop_entry), 0, buy_stop_entry, buy_stop_loss, buy_take_profit, 0, 0, Commentary);
        ulong ticket = Trade.ResultOrder(); // 获取订单号
        if (ticket < 0)
        {
            Write("OrderSend错误: " + ErrorDescription(GetLastError()) + " 买入止损 @ " + DoubleToString(buy_stop_entry, _Digits) + " 止损 @ " + DoubleToString(buy_stop_loss, _Digits) + " 获利 @ " + DoubleToString(buy_take_profit, _Digits));
            tries++;
        }
        else
        {
            Write("开立买入止损: OrderSend执行成功。订单号 = " + IntegerToString(ticket));
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| 开立卖出止损订单                                                    |
//+------------------------------------------------------------------+
void OpenSellStop()
{
    for (int tries = 0; tries < 10; tries++)
    {
        Trade.OrderOpen(_Symbol, ORDER_TYPE_SELL_STOP, LotsOptimized(ORDER_TYPE_SELL, sell_stop_entry), 0, sell_stop_entry, sell_stop_loss, sell_take_profit, 0, 0, Commentary);
        ulong ticket = Trade.ResultOrder(); // 获取订单号
        if (ticket < 0)
        {
            Write("OrderSend错误: " + ErrorDescription(GetLastError()) + " 卖出止损 @ " + DoubleToString(sell_stop_entry, _Digits) + " 止损 @ " + DoubleToString(sell_stop_loss, _Digits) + " 获利 @ " + DoubleToString(sell_take_profit, _Digits));
        }
        else
        {
            Write("开立卖出止损: OrderSend执行成功。订单号 = "  + IntegerToString(ticket));
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| 如果需要，设置持仓的保本                                            |
//+------------------------------------------------------------------+
void DoBE(int byPoints)
{
    double Ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double Bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    for (int i = 0; i < PositionsTotal(); i++)
    {
        if (!PositionSelectByTicket(PositionGetTicket(i)))
        {
            {
                Write(__FUNCTION__ + " | 选择持仓时出错: " + ErrorDescription(GetLastError()));
                continue;
            }
        }
        if ((PositionGetString(POSITION_SYMBOL) != Symbol()) || (PositionGetInteger(POSITION_MAGIC) != Magic)) continue;
        
        if ((PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) && (NormalizeDouble(Bid - PositionGetDouble(POSITION_PRICE_OPEN), _Digits) > NormalizeDouble(byPoints * _Point, _Digits)) && (PositionInfo.StopLoss() < PositionGetDouble(POSITION_PRICE_OPEN)))
        {
            double NewSL = NormalizeDouble(PositionInfo.PriceOpen() + _Point, _Digits);
            if (Bid - NewSL > SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * _Point)
            {
                Write("将买入订单的止损移动到保本+1点。");
                if (!Trade.PositionModify(_Symbol, NewSL, PositionGetDouble(POSITION_TP)))
                {
                    Write(__FUNCTION__ + " | 修改买入订单时出错: " + ErrorDescription(GetLastError()));
                }
            }
        }
        else if ((PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL) && (NormalizeDouble(PositionGetDouble(POSITION_PRICE_OPEN) - Ask, _Digits) > NormalizeDouble(byPoints * _Point, _Digits)) && (PositionInfo.StopLoss() > PositionGetDouble(POSITION_PRICE_OPEN)))
        {
            double NewSL = NormalizeDouble(PositionInfo.PriceOpen() - _Point, _Digits);
            if (NewSL - Ask > SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * _Point)
            {
                Write("将卖出订单的止损移动到保本-1点。");
                if (!Trade.PositionModify(_Symbol, NewSL, PositionGetDouble(POSITION_SL)))
                {
                    Write(__FUNCTION__ + " | 修改卖出订单时出错: " + ErrorDescription(GetLastError()));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 对开仓持仓进行追踪止损                                              |
//+------------------------------------------------------------------+
void DoTrail()
{
    double Ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double Bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    int total = PositionsTotal();
    for (int cnt = 0; cnt < total; cnt++)
    {
        if (PositionGetSymbol(cnt) != Symbol()) continue;
        if (PositionGetInteger(POSITION_MAGIC) != Magic) continue;

        if ((PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) && (Bid - PositionGetDouble(POSITION_PRICE_OPEN) > TrailingStop * _Point) && (PositionGetDouble(POSITION_SL) < NormalizeDouble(Bid - TrailingStop * _Point, _Digits)))
        {
            double NewSL = NormalizeDouble(Bid - TrailingStop * _Point, _Digits);
            if (Bid - NewSL > SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * _Point)
            {
                Trade.PositionModify(_Symbol, NewSL, PositionGetDouble(POSITION_TP));
            }
        }
        else if ((PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL) && (PositionGetDouble(POSITION_PRICE_OPEN) - Ask > TrailingStop * _Point) && ((PositionGetDouble(POSITION_SL) > NormalizeDouble(Ask + TrailingStop * _Point, _Digits)) || (PositionGetDouble(POSITION_SL) == 0)))
        {
            double NewSL = NormalizeDouble(Ask + TrailingStop * _Point, _Digits);
            if (NewSL - Ask > SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * _Point)
            {
                Trade.PositionModify(_Symbol, NewSL, PositionInfo.TakeProfit());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 删除买入止损订单                                                    |
//+------------------------------------------------------------------+
void DeleteBuyStop()
{
    for (int i = 0; i < OrdersTotal(); i++)
    {
        ulong ticket = OrderGetTicket(i);
        if (OrderSelect(ticket))
        {
            if ((OrderGetString(ORDER_SYMBOL) == _Symbol) && (OrderGetInteger(ORDER_MAGIC) == Magic) && (OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_BUY_STOP))
            {
                if (!Trade.OrderDelete(ticket))
                {
                    Write("删除买入止损订单时出错: " + ErrorDescription(GetLastError()));
                }
                else Write("买入止损订单已删除。");
                return;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 删除卖出止损订单                                                    |
//+------------------------------------------------------------------+
void DeleteSellStop()
{
    for (int i = 0; i < OrdersTotal(); i++)
    {
        ulong ticket = OrderGetTicket(i);
        if (OrderSelect(ticket))
        {
            if ((OrderGetString(ORDER_SYMBOL) == _Symbol) && (OrderGetInteger(ORDER_MAGIC) == Magic) && (OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_SELL_STOP))
            {
                if (!Trade.OrderDelete(ticket))
                {
                    Write("删除卖出止损订单时出错: " + ErrorDescription(GetLastError()));
                }
                else Write("卖出止损订单已删除。");
                return;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 根据新的价格水平更新挂单                                            |
//+------------------------------------------------------------------+
void DoModify()
{
    for (int i = 0; i < OrdersTotal(); i++)
    {
        ulong ticket = OrderGetTicket(i);
        if (!OrderSelect(ticket))
        {
            Write(__FUNCTION__ + " | 选择订单时出错: " + ErrorDescription(GetLastError()));
            continue;
        }
        if ((OrderGetString(ORDER_SYMBOL) != Symbol()) || (OrderGetInteger(ORDER_MAGIC) != Magic)) continue;

        if (OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_BUY_STOP)
        {
            if (OrderGetDouble(ORDER_PRICE_OPEN) != buy_stop_entry)
            {
                if (!Trade.OrderModify(ticket, buy_stop_entry, buy_stop_loss, buy_take_profit, 0, 0))
                {
                    Write(__FUNCTION__ + " | 修改买入止损订单时出错: " + ErrorDescription(GetLastError()));
                }
                else Write("买入止损订单修改执行: " + DoubleToString(OrderGetDouble(ORDER_PRICE_OPEN), _Digits) + " -> " + DoubleToString(buy_stop_entry, _Digits));
            }
        }
        if (OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_SELL_STOP)
        {
            if (OrderGetDouble(ORDER_PRICE_OPEN) != sell_stop_entry)
            {
                if (!Trade.OrderModify(ticket, sell_stop_entry, sell_stop_entry, sell_take_profit, 0, 0))
                {
                    Write(__FUNCTION__ + " | 修改卖出止损订单时出错: " + ErrorDescription(GetLastError()));
                }
                else Write("卖出止损订单修改执行: " + DoubleToString(OrderGetDouble(ORDER_PRICE_OPEN), _Digits) + " -> " + DoubleToString(sell_stop_entry, _Digits));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 打印字符串并写入日志文件                                            |
//+------------------------------------------------------------------+
void Write(string str)
{
    Print(str);

    if (filename == "") return;

    int handle = FileOpen(filename, FILE_READ | FILE_WRITE | FILE_TXT);
    if (handle == INVALID_HANDLE)
    {
        Print("打开文件错误 ", filename, ": ", ErrorDescription(GetLastError()));
        return;
    }
    FileSeek(handle, 0, SEEK_END);
    FileWrite(handle, str + " 时间 " + TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS));
    FileClose(handle);
}

//+------------------------------------------------------------------+
//| 主要交易逻辑                                                       |
//+------------------------------------------------------------------+
void OnTick()
{
    AccountCurrency = AccountInfoString(ACCOUNT_CURRENCY);
    if (AccountCurrency == "RUR") AccountCurrency = "RUB";
    ProfitCurrency = SymbolInfoString(Symbol(), SYMBOL_CURRENCY_PROFIT);
    if (ProfitCurrency == "RUR") ProfitCurrency = "RUB";
    BaseCurrency = SymbolInfoString(Symbol(), SYMBOL_CURRENCY_BASE);
    if (BaseCurrency == "RUR") BaseCurrency = "RUB";
    
    if (BEPoints > 0) DoBE(BEPoints);
    if (TrailingStop > 0) DoTrail();

    int OrdersCondition = CheckOrdersCondition();

    MqlRates rates[];
    ArraySetAsSeries(rates, true);
    int copied = CopyRates(NULL, PERIOD_M1, 0, CTCBN + 1, rates);
    if (copied != CTCBN + 1) Print("复制价格数据时出错: ", ErrorDescription(GetLastError()));

    double Ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double Bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    double recent_high = rates[0].high;
    double recent_low = rates[0].low;
    for (int i = 1; i <= CTCBN; i++)
    {
        if (rates[i].high > recent_high) recent_high = rates[i].high;
        if (rates[i].low < recent_low) recent_low = rates[i].low;
    }
    double spread = Ask - Bid;
    buy_stop_entry = NormalizeDouble(recent_high + spread + EntryDistance * _Point, _Digits);
    sell_stop_entry = NormalizeDouble(recent_low - EntryDistance * _Point, _Digits);

    if (UseATR)
    {
        double ATR;
        double ATR_buffer[1];
        if (CopyBuffer(ATR_handle, 0, 1, 1, ATR_buffer) != 1)
        {
            Print("ATR数据未就绪！");
            return;
        }
        ATR = ATR_buffer[0];
        SL = ATR * ATR_Multiplier_SL;
        if (SL <= (SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) + SymbolInfoInteger(Symbol(), SYMBOL_SPREAD)) * _Point) SL = (SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) + SymbolInfoInteger(Symbol(), SYMBOL_SPREAD)) * _Point;
        TP = ATR * ATR_Multiplier_TP;
        if (TP <= (SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) + SymbolInfoInteger(Symbol(), SYMBOL_SPREAD)) * _Point) TP = (SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) + SymbolInfoInteger(Symbol(), SYMBOL_SPREAD)) * _Point;
        SL /= _Point;
        TP /= _Point;
    }

    buy_stop_loss = NormalizeDouble(buy_stop_entry - SL * _Point, _Digits);
    sell_stop_loss = NormalizeDouble(sell_stop_entry + SL * _Point, _Digits);
    buy_take_profit = NormalizeDouble(buy_stop_entry + TP * _Point, _Digits);
    sell_take_profit= NormalizeDouble(sell_stop_entry - TP * _Point, _Digits);

    Comment("\nAmazing EA",
            "\n高点 @ ", recent_high, " 买入订单 @ ", buy_stop_entry, " 止损 @ ", buy_stop_loss, " 获利 @ ", buy_take_profit, 
            "\n低点 @ ", recent_low, " 卖出订单 @ ", sell_stop_entry, " 止损 @ ", sell_stop_loss, " 获利 @ ", sell_take_profit, 
            "\nCTCBN: ", CTCBN, " OCO: ", OCO, " BEPips: ", BEPoints, 
            "\n资金管理: ", MM, " 风险: ", DoubleToString(RiskMoney, 2), " ", AccountCurrency, " 手数(买/卖): ", DoubleToString(LotsOptimized(ORDER_TYPE_BUY, buy_stop_entry), lot_decimal_places), "/", DoubleToString(LotsOptimized(ORDER_TYPE_SELL, sell_stop_entry), lot_decimal_places));

    // 检查是否需要开立新订单
    if (OrdersCondition == 0) // 没有订单时
    {
        Write("开立买入止损和卖出止损。OrdersCondition = " + IntegerToString(OrdersCondition) + " 时间戳 = " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + ".");
        OpenBuyStop();
        OpenSellStop();
    }
    else if (OrdersCondition == 10)
    {
        Write("开立卖出止损。OrdersCondition = " + IntegerToString(OrdersCondition) + " 时间戳 = " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + ".");
        OpenSellStop();
    }
    else if (OrdersCondition == 1)
    {
        Write("开立买入止损。OrdersCondition = " + IntegerToString(OrdersCondition) + " 时间戳 = " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + ".");
        OpenBuyStop();
    }

    // 持续更新挂单位置
    DoModify();

    // OCO订单管理
    if (OCO)
    {
        if (OrdersCondition == 1001)
        {
            Write("由于买入止损被触发，删除卖出止损。OrdersCondition = " + IntegerToString(OrdersCondition) + " 时间戳 = " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + ".");
            DeleteSellStop();
        }
        else if (OrdersCondition == 110)
        {
            Write("由于卖出止损被触发，删除买入止损。OrdersCondition=" + IntegerToString(OrdersCondition) + " 时间戳=" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + ".");
            DeleteBuyStop();
        }
    }
}

//+------------------------------------------------------------------+
//| 根据利润计算模式计算单位成本                                        |
//+------------------------------------------------------------------+
double CalculateUnitCost()
{
    double UnitCost;
    // CFD
    if (((CalcMode == SYMBOL_CALC_MODE_CFD) || (CalcMode == SYMBOL_CALC_MODE_CFDINDEX) || (CalcMode == SYMBOL_CALC_MODE_CFDLEVERAGE)))
        UnitCost = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE) * SymbolInfoDouble(Symbol(), SYMBOL_TRADE_CONTRACT_SIZE);
    // 对于外汇和期货工具，点值已经等于1个单位成本
    else UnitCost = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE_LOSS);
    
    return UnitCost;
}

//+-----------------------------------------------------------------------------------+
//| 计算当GivenCurrency != AccountCurrency时的必要调整                                 |
//+-----------------------------------------------------------------------------------+
double CalculateAdjustment()
{
    if (ReferencePair == NULL)
    {
        ReferencePair = GetSymbolByCurrencies(ProfitCurrency, AccountCurrency);
        ReferenceSymbolMode = true;
        // 失败
        if (ReferencePair == NULL)
        {
            // 反转货币
            ReferencePair = GetSymbolByCurrencies(AccountCurrency, ProfitCurrency);
            ReferenceSymbolMode = false;
        }
    }
    if (ReferencePair == NULL)
    {
        Print("错误！无法检测到用于调整计算的正确货币对: ", ProfitCurrency, ", ", AccountCurrency, ".");
        ReferencePair = Symbol();
        return 1;
    }
    MqlTick tick;
    SymbolInfoTick(ReferencePair, tick);
    return GetCurrencyCorrectionCoefficient(tick);
}

//+---------------------------------------------------------------------------+
//| 返回具有指定基础货币和利润货币的货币对                                      |
//+---------------------------------------------------------------------------+
string GetSymbolByCurrencies(string base_currency, string profit_currency)
{
    // 遍历所有交易品种
    for (int s = 0; s < SymbolsTotal(false); s++)
    {
        // 通过编号获取交易品种名称
        string symbolname = SymbolName(s, false);

        // 跳过非外汇对
        if ((SymbolInfoInteger(symbolname, SYMBOL_TRADE_CALC_MODE) != SYMBOL_CALC_MODE_FOREX) && (SymbolInfoInteger(symbolname, SYMBOL_TRADE_CALC_MODE) != SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE)) continue;

        // 获取其基础货币
        string b_cur = SymbolInfoString(symbolname, SYMBOL_CURRENCY_BASE);
        if (b_cur == "RUR") b_cur = "RUB";

        // 获取其利润货币
        string p_cur = SymbolInfoString(symbolname, SYMBOL_CURRENCY_PROFIT);
        if (p_cur == "RUR") p_cur = "RUB";

        // 如果货币对匹配两种货币，在Market Watch中选择它并返回其名称
        if ((b_cur == base_currency) && (p_cur == profit_currency))
        {
            // 如果需要，选择它
            if (!(bool)SymbolInfoInteger(symbolname, SYMBOL_SELECT)) SymbolSelect(symbolname, true);

            return symbolname;
        }
    }
    return NULL;
}

//+------------------------------------------------------------------+
//| 根据货币、交易方向和当前价格获取修正系数                            |
//+------------------------------------------------------------------+
double GetCurrencyCorrectionCoefficient(MqlTick &tick)
{
    if ((tick.ask == 0) || (tick.bid == 0)) return -1; // 数据尚未就绪
    // 反向报价
    if (ReferenceSymbolMode)
    {
        // 对反向报价使用买入价
        return tick.ask;
    }
    // 直接报价
    else
    {
        // 对直接报价使用卖出价
        return (1 / tick.bid);
    }
}

//+------------------------------------------------------------------+
//| 根据资金管理参数计算头寸规模                                        |
//+------------------------------------------------------------------+
double LotsOptimized(ENUM_ORDER_TYPE dir, double entry)
{
    if (!MM) return (Lots);

    double PositionSize = 0, Size;

    if (AccountInfoString(ACCOUNT_CURRENCY) == "") return 0;

    if (FixedBalance > 0)
    {
        Size = FixedBalance;
    }
    else if (UseEquityInsteadOfBalance)
    {
        Size = AccountInfoDouble(ACCOUNT_EQUITY);
    }
    else
    {
        Size = AccountInfoDouble(ACCOUNT_BALANCE);
    }

    if (!UseMoneyInsteadOfPercentage) RiskMoney = Size * Risk / 100;
    else RiskMoney = MoneyRisk;

    double UnitCost = CalculateUnitCost();

    // 如果利润货币与账户货币不同，且交易品种不是外汇对或期货(CFD等)
    if ((ProfitCurrency != AccountCurrency) && (CalcMode != SYMBOL_CALC_MODE_FOREX) && (CalcMode != SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE) && (CalcMode != SYMBOL_CALC_MODE_FUTURES) && (CalcMode != SYMBOL_CALC_MODE_EXCH_FUTURES) && (CalcMode != SYMBOL_CALC_MODE_EXCH_FUTURES_FORTS))
    {
        double CCC = CalculateAdjustment(); // 仅对亏损计算有效
        // 调整单位成本
        UnitCost *= CCC;
    }

    // 如果账户货币 == 交易品种的基础货币，调整UnitCost到未来汇率(止损)。仅对外汇对有效
    if ((AccountCurrency == BaseCurrency) && ((CalcMode == SYMBOL_CALC_MODE_FOREX) || (CalcMode == SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE)))
    {
        double current_rate = 1, future_rate = 1;
        if (dir == ORDER_TYPE_BUY)
        {
            if (entry == 0) current_rate = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            else current_rate = entry;
            future_rate = current_rate - SL * _Point;
        }
        else if (dir == ORDER_TYPE_SELL)
        {
            if (entry == 0) current_rate = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            else current_rate = entry;
            future_rate = current_rate + SL * _Point;
        }
        if (future_rate == 0) future_rate = _Point; // 防止除以零
        UnitCost *= (current_rate / future_rate);
    }

    double TickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double LotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    int LotStep_digits = CountDecimalPlaces(LotStep);
    if ((SL != 0) && (UnitCost != 0) && (TickSize != 0)) PositionSize = NormalizeDouble(RiskMoney / (SL * _Point * UnitCost / TickSize), LotStep_digits);

    if (PositionSize < SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN))
    {
        Print("计算的头寸规模(" + DoubleToString(PositionSize, 2) + ")小于最小头寸规模(" + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN), 2) + ")。设置头寸规模为最小值。");
        PositionSize = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    }
    else if (PositionSize > SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX))
    {
        Print("计算的头寸规模(" + DoubleToString(PositionSize, 2) + ")大于最大头寸规模(" + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX), 2) + ")。设置头寸规模为最大值。");
        PositionSize = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    }

    double steps = PositionSize / LotStep;
    if (MathAbs(MathRound(steps) - steps) < 0.00000001) steps = MathRound(steps);
    if (steps - MathFloor(steps) > LotStep / 2)
    {
        Print("计算的头寸规模(" + DoubleToString(PositionSize, 2) + ")使用不均匀的步长。允许的步长 = " + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP), 2) + "。设置头寸规模为 " + DoubleToString(MathFloor(steps) * LotStep, 2) + "。");
        PositionSize = MathFloor(steps) * LotStep;
    }

    return PositionSize;
}

//+------------------------------------------------------------------+
//| 计算小数位数                                                       |
//+------------------------------------------------------------------+
int CountDecimalPlaces(double number)
{
    // 100作为数字的最大长度
    for (int i = 0; i < 100; i++)
    {
        double pwr = MathPow(10, i);
        if (MathRound(number * pwr) / pwr == number) return i;
    }
    return -1;
}
//+------------------------------------------------------------------+