//+------------------------------------------------------------------+
//|                                                 BittmaEA_Plus.mq5 |
//|                         Copyright 2024, MetaQuotes Software Corp. |
//|                                              https://www.meta.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.meta.com"
#property version   "1.30"

#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include <Trade/SymbolInfo.mqh>
#include <Arrays/ArrayLong.mqh>
#include <Guapit/controls/Button.mqh>
#include <Math/Stat/Math.mqh>

//--- UI主题枚举
enum ENUM_UI_THEME {
   THEME_DARK,    // 深色主题
   THEME_LIGHT,   // 浅色主题
   THEME_BLUE     // 蓝色主题
};

//--- 输入参数
// 复利模式参数
input int       MagicNumber = 123789;               // 魔术号
input bool       UseCompoundMode = true;            // 使用复利模式
input double    BaseEquityPerLot = 3000.0;          // 每0.01手所需的资金(复利模式)
input double    FixedLotSize = 0.01;                // 初始手数
input double    MaxRiskPercent = 30.0;              // 账户最大风险%(触发平仓)
input int       MaxLayers = 50;                     // 最大加仓层数
input double    BaseProfitTarget = 1000.0;           // 初始止盈金额
input ENUM_UI_THEME UITheme = THEME_DARK;           // UI主题

//--- 加仓参数
input double    Multiplier_FirstLayer = 2.0;        // 第1层倍数
input double    Multiplier_2To5 = 1.3;              // 第2至第5层倍数
input double    Multiplier_6To10 = 0.9;             // 第6至第10层倍数
input double    Multiplier_11To20 = 0.8;            // 第11至第20层倍数
input double    Multiplier_21Plus = 0.8;            // 第21层及以后倍数
input double    ScaleInSpacing = 250;               // 加仓间距(点数)
input double    MinSpacing = 120;                   // 最小加仓间距(点数)
input double    MaxSpacing = 600;                   // 最大加仓间距(点数)
input double    VolatilityFactor = 0.42;            // 波动率调整系数(0-1)
input int       ADXPeriod = 14;                     // ADX周期
input double    MinADX = 22.0;                      // 最小ADX值(允许加仓)

// ATR止盈参数
input group "===== ATR止盈参数 ====="
input bool   UseATRBasedTP = true;         // 使用基于ATR的止盈策略
input int    ATR_Period_TP = 14;           // ATR周期(用于止盈)
input ENUM_TIMEFRAMES ATR_Timeframe = PERIOD_D1; // ATR时间框架
input double ATR_TP_Multiplier1 = 1.5;     // 第一阶段ATR倍数
input double ATR_TP_Multiplier2 = 2.0;     // 第二阶段ATR倍数
input double ATR_TP_Multiplier3 = 3.0;     // 最终阶段ATR倍数
input double ATR_TP_Portion1 = 0.3;        // 第一阶段平仓比例
input double ATR_TP_Portion2 = 0.3;        // 第二阶段平仓比例

// 全局变量
CTrade         trade;                   // 交易类
CPositionInfo  *PositionInfo;          // 持仓信息类
CSymbolInfo    SymbolInfo;             // 品种信息类
CArrayLong     m_arr_tickets;          // 订单号数组

// 技术指标句柄
int atrHandle;                         // ATR指标句柄
int adxHandle;                         // ADX指标句柄

// UI 控件
Button btnBuySell, btnCloseAll;

//--- 全局变量
double account_equity;         // 账户净值
double account_balance;        // 账户余额
double account_profit;        // 当前浮动盈亏
double point_value;           // 点值
double long_volume;           // 多单手数
double short_volume;          // 空单手数
double total_volume;          // 总手数
double daily_profit;          // 当日收益（已实现）
double daily_floating_profit; // 当日浮动盈亏
double daily_start_balance;   // 当日开始时的账户余额
double daily_start_equity;    // 当日开始时的账户净值
double daily_closed_profit;   // 当日已平仓盈亏
double daily_net_deposits;    // 当日净入金
datetime last_daily_check;    // 上次检查日期
datetime last_history_check;  // 上次历史订单检查时间
int last_deal_ticket;         // 上次检查的最后一笔交易单号

// UI主题相关变量
color g_positiveColor;        // 正值颜色
color g_negativeColor;        // 负值颜色
color g_neutralColor;         // 中性值颜色
color g_textColor;            // 文本颜色

// Expert Magic Number已移至输入参数

// 止盈相关变量
bool firstCloseDone = false;   // 第一次部分平仓是否完成
bool secondCloseDone = false;  // 第二次部分平仓是否完成

// 交易相关变量
double initialEquity;         // 初始资金
double currentEquity;         // 当前资金
double highestEquity;        // 最高资金
bool   trendDirection;       // 趋势方向
int    currentLayer;         // 当前层数
double lastBuyPrice;         // 最后买入价格
double lastSellPrice;        // 最后卖出价格
double symbolPoint;          // 品种点值
double symbolMinLot;         // 最小手数
double symbolLotStep;        // 手数步长
double symbolMaxLot;         // 最大手数
int    symbolTradeStopsLevel;    // 交易止损级别
int    symbolTradeFreezeLevel;   // 交易冻结级别

// 定义RTOTAL和SLEEPTIME (用于平仓循环)
#define RTOTAL 3
#define SLEEPTIME 1000

//--- 全局变量
int currentBuyLayer = 0;    // 当前多单层数
int currentSellLayer = 0;   // 当前空单层数

// 1. 交易策略优化
//+------------------------------------------------------------------+
//| 简化版加仓倍数计算函数                                           |
//+------------------------------------------------------------------+
double GetMultiplierForLayer(int layer)
{
    // 获取基础倍数 - 根据层级选择对应倍数
    double baseMultiplier;

    if(layer <= 0)                  // 处理边界情况
        return Multiplier_FirstLayer;
    else if(layer == 1)             // 第1层
        baseMultiplier = Multiplier_FirstLayer;
    else if(layer <= 5)             // 第2-5层
        baseMultiplier = Multiplier_2To5;
    else if(layer <= 10)            // 第6-10层
        baseMultiplier = Multiplier_6To10;
    else if(layer <= 20)            // 第11-20层
        baseMultiplier = Multiplier_11To20;
    else if(layer <= MaxLayers)     // 第21层至最大层
        baseMultiplier = Multiplier_21Plus;
    else                            // 超过最大层
        return Multiplier_21Plus;

    // 应用市场状态和风险调整因子
    double adjustedMultiplier = baseMultiplier * GetMarketState() * GetDynamicRiskAdjustment();

    // 确保倍数在1.1-2.0范围内
    return MathMax(1.1, MathMin(adjustedMultiplier, 2.0));
}

//+------------------------------------------------------------------+
//| 获取市场状态                                                     |
//+------------------------------------------------------------------+
double GetMarketState()
{
    // 获取ATR值
    double atr = GetCurrentATR();
    double atrPoints = atr / SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    // 获取ADX值
    double adx = GetCurrentADX();

    // 获取RSI值
    double rsi = GetCurrentRSI();

    // 计算市场状态得分
    double volatilityScore = MathMin(atrPoints / 100, 1.0); // 标准化ATR
    double trendScore = MathMin(adx / 50, 1.0); // 标准化ADX
    double momentumScore = MathAbs(rsi - 50) / 50; // 标准化RSI

    // 综合评分
    return (volatilityScore * 0.4 + trendScore * 0.4 + momentumScore * 0.2);
}

//+------------------------------------------------------------------+
//| 计算持仓风险                                                     |
//+------------------------------------------------------------------+
double CalculatePositionRisk()
{
    double totalVolume = 0;
    double maxVolume = 0;

    // 计算总持仓量和最大单笔持仓量
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)) && PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            double volume = PositionGetDouble(POSITION_VOLUME);
            totalVolume += volume;
            if(volume > maxVolume)
                maxVolume = volume;
        }
    }

    // 如果没有持仓，返回0风险
    if(totalVolume == 0)
        return 0.0;

    // 计算风险得分 (0-1之间)
    double volumeRisk = MathMin(totalVolume / 10.0, 1.0); // 假设10手为最高风险
    double concentrationRisk = maxVolume / totalVolume; // 集中度风险

    return (volumeRisk * 0.7 + concentrationRisk * 0.3);
}

//+------------------------------------------------------------------+
//| 计算市场风险                                                     |
//+------------------------------------------------------------------+
double CalculateMarketRisk()
{
    // 获取ATR值作为波动率指标
    double atr = GetCurrentATR();
    double atrPoints = atr / SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    // 获取ADX值作为趋势强度指标
    double adx = GetCurrentADX();

    // 计算波动率风险 (高波动 = 高风险)
    double volatilityRisk = MathMin(atrPoints / 500.0, 1.0);

    // 计算趋势风险 (强趋势 = 低风险，因为方向更明确)
    double trendRisk = 1.0 - MathMin(adx / 50.0, 0.8);

    // 综合风险评分
    return (volatilityRisk * 0.6 + trendRisk * 0.4);
}

//+------------------------------------------------------------------+
//| 获取波动率因子                                                   |
//+------------------------------------------------------------------+
double GetVolatilityFactor()
{
    double atr = GetCurrentATR();
    double atrPoints = atr / SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    // 标准化ATR值到0.8-1.2范围
    double factor = 1.0 + ((atrPoints / 300.0) - 0.5) * 0.4;

    // 确保因子在合理范围内
    factor = MathMax(0.8, MathMin(factor, 1.2));

    return factor;
}

//+------------------------------------------------------------------+
//| 获取动态风险调整                                                 |
//+------------------------------------------------------------------+
double GetDynamicRiskAdjustment()
{
    // 使用已在UpdateMarketData中更新的currentEquity值
    // 不需要重复获取: currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);

    // 计算账户风险 - 基于初始净值的回撤比例
    double equityRisk = 0;
    if(initialEquity > 0 && currentEquity < initialEquity)
    {
        equityRisk = (initialEquity - currentEquity) / initialEquity;
    }

    // 计算持仓风险
    double positionRisk = CalculatePositionRisk();

    // 计算市场风险
    double marketRisk = CalculateMarketRisk();

    // 综合风险评分 - 账户风险权重最高
    double totalRisk = (equityRisk * 0.5 + positionRisk * 0.3 + marketRisk * 0.2);

    // 确保风险值在0-1范围内
    totalRisk = MathMax(0, MathMin(totalRisk, 1.0));

    // 根据风险水平调整倍数
    if(totalRisk > 0.7) return 0.8;      // 高风险 - 减少80%
    else if(totalRisk > 0.5) return 0.9; // 中等风险 - 减少90%
    else if(totalRisk > 0.3) return 1.0; // 正常风险 - 不调整
    else return 1.2;                      // 低风险 - 增加20%
}

//+------------------------------------------------------------------+
//| 基于ATR的止盈策略                                                |
//+------------------------------------------------------------------+
void CheckATRBasedProfitTarget()
{
    // 计算当前盈利
    double currentProfit = currentEquity - initialEquity;

    // 如果没有盈利，直接返回
    if(currentProfit <= 0)
        return;

    // 获取3日ATR值
    double atr = CalculateATRForTimeframe(ATR_Timeframe, ATR_Period_TP);
    if(atr <= 0)
        return;

    // 将ATR转换为点数
    double atrPoints = atr / SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    // 计算每点的价值
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double pointValue = tickValue / SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE) * SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    // 计算持仓总量
    double totalVolume = CacheManager::GetCachedTotalVolume();
    if(totalVolume <= 0)
        return;

    // 计算ATR对应的盈利金额
    double atrValue = atr * pointValue * totalVolume * 100; // 转换为标准手

    // 计算各阶段的目标盈利
    double target1 = atrValue * ATR_TP_Multiplier1;
    double target2 = atrValue * ATR_TP_Multiplier2;
    double target3 = atrValue * ATR_TP_Multiplier3;

    // 输出止盈目标信息（每分钟一次）
    static datetime lastProfitLogTime = 0;
    datetime currentTime = TimeCurrent();
    if(currentTime - lastProfitLogTime >= 60)
    {
        PrintFormat("ATR止盈监控 - 当前盈利: %.2f, ATR值: %.5f, ATR金额: %.2f",
                   currentProfit, atr, atrValue);
        PrintFormat("目标1: %.2f (%.1f%%), 目标2: %.2f (%.1f%%), 目标3: %.2f (%.1f%%)",
                   target1, (currentProfit / target1) * 100,
                   target2, (currentProfit / target2) * 100,
                   target3, (currentProfit / target3) * 100);
        lastProfitLogTime = currentTime;
    }

    // 分批止盈策略
    if(!firstCloseDone && currentProfit >= target1)
    {
        Print("达到第一阶段ATR止盈目标 (", DoubleToString(target1, 2), "), 平仓", DoubleToString(ATR_TP_Portion1 * 100, 0), "%持仓");
        ClosePartialPositions(ATR_TP_Portion1); // 平掉第一阶段比例的仓位
        firstCloseDone = true;
    }
    else if(firstCloseDone && !secondCloseDone && currentProfit >= target2)
    {
        Print("达到第二阶段ATR止盈目标 (", DoubleToString(target2, 2), "), 平仓", DoubleToString(ATR_TP_Portion2 * 100, 0), "%持仓");
        ClosePartialPositions(ATR_TP_Portion2); // 平掉第二阶段比例的仓位
        secondCloseDone = true;
    }
    else if(firstCloseDone && secondCloseDone && currentProfit >= target3)
    {
        Print("达到最终ATR止盈目标 (", DoubleToString(target3, 2), "), 平仓所有持仓");
        CloseAllPositionsIncludingInitial();
        ResetTradingState();
    }
}

//+------------------------------------------------------------------+
//| 优化后的止盈策略                                                 |
//+------------------------------------------------------------------+
void CheckProfitTarget()
{
    // 根据设置选择使用基于ATR的止盈策略或原始策略
    if(UseATRBasedTP)
    {
        CheckATRBasedProfitTarget();
        return;
    }

    // 以下是原始的止盈策略代码
    // 计算当前盈利
    double currentProfit = currentEquity - initialEquity;

    // 如果没有盈利，直接返回
    if(currentProfit <= 0)
        return;

    // 获取波动率因子
    double volatilityFactor = GetVolatilityFactor();

    // 动态计算止盈目标
    double baseTarget = BaseProfitTarget * (1 + (currentLayer * 0.1));
    double adjustedTarget = baseTarget * volatilityFactor;

    // 输出止盈目标信息（每分钟一次）
    static datetime lastProfitLogTime = 0;
    datetime currentTime = TimeCurrent();
    if(currentTime - lastProfitLogTime >= 60)
    {
        PrintFormat("止盈监控 - 当前盈利: %.2f, 目标: %.2f, 进度: %.1f%%",
                   currentProfit, adjustedTarget, (currentProfit / adjustedTarget) * 100);
        lastProfitLogTime = currentTime;
    }

    // 分批止盈策略
    if(!firstCloseDone && currentProfit >= adjustedTarget * 0.7)
    {
        Print("达到第一阶段止盈目标 (", DoubleToString(adjustedTarget * 0.7, 2), "), 平仓30%持仓");
        ClosePartialPositions(0.3); // 平掉30%仓位
        firstCloseDone = true;
    }
    else if(firstCloseDone && !secondCloseDone && currentProfit >= adjustedTarget * 0.9)
    {
        Print("达到第二阶段止盈目标 (", DoubleToString(adjustedTarget * 0.9, 2), "), 平仓30%持仓");
        ClosePartialPositions(0.3); // 再平掉30%仓位
        secondCloseDone = true;
    }
    else if(firstCloseDone && secondCloseDone && currentProfit >= adjustedTarget)
    {
        Print("达到最终止盈目标 (", DoubleToString(adjustedTarget, 2), "), 平仓所有持仓");
        CloseAllPositionsIncludingInitial();
        ResetTradingState();
    }
}

// 2. 性能优化
//+------------------------------------------------------------------+
//| 增强型缓存管理器                                                 |
//+------------------------------------------------------------------+
class CacheManager
{
private:
    // 技术指标缓存
    static datetime lastATRUpdate;
    static datetime lastADXUpdate;
    static datetime lastRSIUpdate;
    static double cachedATR;
    static double cachedADX;
    static double cachedRSI;

    // 账户信息缓存
    static datetime lastAccountUpdate;
    static double cachedEquity;
    static double cachedBalance;
    static double cachedProfit;

    // 持仓信息缓存
    static datetime lastPositionUpdate;
    static double cachedLongVolume;
    static double cachedShortVolume;
    static double cachedTotalVolume;
    static int cachedPositionsCount;

    // 价格信息缓存
    static datetime lastPriceUpdate;
    static double cachedBid;
    static double cachedAsk;
    static double cachedSpread;

    // 缓存控制参数
    static const int INDICATOR_CACHE_SECONDS;   // 指标缓存60秒
    static const int ACCOUNT_CACHE_SECONDS;      // 账户信息缓存1秒
    static const int POSITION_CACHE_SECONDS;     // 持仓信息缓存5秒
    static const int PRICE_CACHE_SECONDS;        // 价格信息缓存1秒

public:
    // 技术指标缓存方法
    static double GetCachedATR()
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastATRUpdate >= INDICATOR_CACHE_SECONDS || cachedATR <= 0)
        {
            double newATR = CalculateATR();
            if(newATR > 0) // 只有在获取到有效值时才更新缓存
            {
                cachedATR = newATR;
                lastATRUpdate = currentTime;
            }
        }
        return cachedATR;
    }

    static double GetCachedADX()
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastADXUpdate >= INDICATOR_CACHE_SECONDS || cachedADX <= 0)
        {
            double newADX = CalculateADX();
            if(newADX >= 0) // ADX值应该大于等于0
            {
                cachedADX = newADX;
                lastADXUpdate = currentTime;
            }
        }
        return cachedADX;
    }

    static double GetCachedRSI()
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastRSIUpdate >= INDICATOR_CACHE_SECONDS || cachedRSI <= 0)
        {
            double newRSI = CalculateRSI();
            if(newRSI >= 0 && newRSI <= 100) // RSI值应该在0-100之间
            {
                cachedRSI = newRSI;
                lastRSIUpdate = currentTime;
            }
        }
        return cachedRSI;
    }

    // 账户信息缓存方法
    static void UpdateAccountCache()
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastAccountUpdate >= ACCOUNT_CACHE_SECONDS)
        {
            cachedEquity = AccountInfoDouble(ACCOUNT_EQUITY);
            cachedBalance = AccountInfoDouble(ACCOUNT_BALANCE);
            cachedProfit = AccountInfoDouble(ACCOUNT_PROFIT);
            lastAccountUpdate = currentTime;
        }
    }

    static double GetCachedEquity()
    {
        UpdateAccountCache();
        return cachedEquity;
    }

    static double GetCachedBalance()
    {
        UpdateAccountCache();
        return cachedBalance;
    }

    static double GetCachedProfit()
    {
        UpdateAccountCache();
        return cachedProfit;
    }

    // 持仓信息缓存方法
    static void UpdatePositionCache()
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastPositionUpdate >= POSITION_CACHE_SECONDS)
        {
            cachedLongVolume = 0;
            cachedShortVolume = 0;
            cachedPositionsCount = 0;

            int total = PositionsTotal();
            for(int i = 0; i < total; i++)
            {
                ulong ticket = PositionGetTicket(i);
                if(PositionSelectByTicket(ticket) && PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
                {
                    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                    double volume = PositionGetDouble(POSITION_VOLUME);

                    if(posType == POSITION_TYPE_BUY)
                        cachedLongVolume += volume;
                    else if(posType == POSITION_TYPE_SELL)
                        cachedShortVolume += volume;

                    cachedPositionsCount++;
                }
            }

            cachedTotalVolume = cachedLongVolume + cachedShortVolume;
            lastPositionUpdate = currentTime;
        }
    }

    static double GetCachedLongVolume()
    {
        UpdatePositionCache();
        return cachedLongVolume;
    }

    static double GetCachedShortVolume()
    {
        UpdatePositionCache();
        return cachedShortVolume;
    }

    static double GetCachedTotalVolume()
    {
        UpdatePositionCache();
        return cachedTotalVolume;
    }

    static int GetCachedPositionsCount()
    {
        UpdatePositionCache();
        return cachedPositionsCount;
    }

    // 价格信息缓存方法
    static void UpdatePriceCache()
    {
        datetime currentTime = TimeCurrent();
        if(currentTime - lastPriceUpdate >= PRICE_CACHE_SECONDS)
        {
            MqlTick tick;
            if(SymbolInfoTick(_Symbol, tick))
            {
                cachedBid = tick.bid;
                cachedAsk = tick.ask;
                cachedSpread = (tick.ask - tick.bid) / SymbolInfoDouble(_Symbol, SYMBOL_POINT);
                lastPriceUpdate = currentTime;
            }
        }
    }

    static double GetCachedBid()
    {
        UpdatePriceCache();
        return cachedBid;
    }

    static double GetCachedAsk()
    {
        UpdatePriceCache();
        return cachedAsk;
    }

    static double GetCachedSpread()
    {
        UpdatePriceCache();
        return cachedSpread;
    }

    // 辅助计算方法
    static double CalculateATR()
    {
        double atrBuffer[];
        ArraySetAsSeries(atrBuffer, true);

        if(CopyBuffer(atrHandle, 0, 0, 3, atrBuffer) > 0 && atrBuffer[0] > 0)
            return atrBuffer[0];

        return cachedATR > 0 ? cachedATR : 0.001; // 返回上次缓存值或默认值
    }

    static double CalculateADX()
    {
        double adxBuffer[];
        ArraySetAsSeries(adxBuffer, true);

        if(CopyBuffer(adxHandle, 0, 0, 3, adxBuffer) > 0)
            return adxBuffer[0];

        return cachedADX > 0 ? cachedADX : 25.0; // 返回上次缓存值或默认值
    }

    static double CalculateRSI()
    {
        static int rsiHandle = INVALID_HANDLE;

        // 如果RSI句柄无效，创建一个
        if(rsiHandle == INVALID_HANDLE)
        {
            rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
            if(rsiHandle == INVALID_HANDLE)
                return 50.0; // 返回中性值
        }

        double rsiBuffer[];
        ArraySetAsSeries(rsiBuffer, true);

        if(CopyBuffer(rsiHandle, 0, 0, 3, rsiBuffer) > 0)
            return rsiBuffer[0];

        return cachedRSI > 0 ? cachedRSI : 50.0; // 返回上次缓存值或默认值
    }
};

// 初始化CacheManager的静态成员变量
// 缓存控制参数
const int CacheManager::INDICATOR_CACHE_SECONDS = 60;   // 指标缓存60秒
const int CacheManager::ACCOUNT_CACHE_SECONDS = 1;      // 账户信息缓存1秒
const int CacheManager::POSITION_CACHE_SECONDS = 5;     // 持仓信息缓存5秒
const int CacheManager::PRICE_CACHE_SECONDS = 1;        // 价格信息缓存1秒

// 技术指标缓存
datetime CacheManager::lastATRUpdate = 0;
datetime CacheManager::lastADXUpdate = 0;
datetime CacheManager::lastRSIUpdate = 0;
double CacheManager::cachedATR = 0;
double CacheManager::cachedADX = 0;
double CacheManager::cachedRSI = 0;

// 账户信息缓存
datetime CacheManager::lastAccountUpdate = 0;
double CacheManager::cachedEquity = 0;
double CacheManager::cachedBalance = 0;
double CacheManager::cachedProfit = 0;

// 持仓信息缓存
datetime CacheManager::lastPositionUpdate = 0;
double CacheManager::cachedLongVolume = 0;
double CacheManager::cachedShortVolume = 0;
double CacheManager::cachedTotalVolume = 0;
int CacheManager::cachedPositionsCount = 0;

// 价格信息缓存
datetime CacheManager::lastPriceUpdate = 0;
double CacheManager::cachedBid = 0;
double CacheManager::cachedAsk = 0;
double CacheManager::cachedSpread = 0;

//+------------------------------------------------------------------+
//| 优化后的市场数据更新                                             |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
    static datetime lastUpdate = 0;
    datetime currentTime = TimeCurrent();

    // 控制更新频率
    if(currentTime - lastUpdate < 1) // 每秒最多更新一次
        return true;

    // 使用缓存系统获取所有需要的数据
    // 技术指标数据 - 这些调用会根据需要自动更新缓存
    /*
    double atr = CacheManager::GetCachedATR();
    double adx = CacheManager::GetCachedADX();
    double rsi = CacheManager::GetCachedRSI();
    */

    // 更新账户信息 - 使用缓存系统
    account_equity = CacheManager::GetCachedEquity();
    account_balance = CacheManager::GetCachedBalance();
    account_profit = CacheManager::GetCachedProfit();
    currentEquity = account_equity;

    // 更新最高净值记录
    if(currentEquity > highestEquity)
        highestEquity = currentEquity;

    // 更新持仓信息 - 使用缓存系统
    long_volume = CacheManager::GetCachedLongVolume();
    short_volume = CacheManager::GetCachedShortVolume();
    total_volume = CacheManager::GetCachedTotalVolume();

    lastUpdate = currentTime;
    return true;
}

//+------------------------------------------------------------------+
//| 优化后的指标计算 - 使用缓存系统                                  |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
    // 直接使用缓存系统获取ATR值
    return CacheManager::GetCachedATR();
}

//+------------------------------------------------------------------+
//| 获取当前ADX值 - 使用缓存系统                                     |
//+------------------------------------------------------------------+
double GetCurrentADX()
{
    // 直接使用缓存系统获取ADX值
    return CacheManager::GetCachedADX();
}

//+------------------------------------------------------------------+
//| 获取当前RSI值 - 使用缓存系统                                     |
//+------------------------------------------------------------------+
double GetCurrentRSI()
{
    // 直接使用缓存系统获取RSI值
    return CacheManager::GetCachedRSI();
}

//+------------------------------------------------------------------+
//| 计算指定时间框架的ATR                                            |
//+------------------------------------------------------------------+
double CalculateATRForTimeframe(ENUM_TIMEFRAMES timeframe, int period)
{
    int atr_handle = iATR(_Symbol, timeframe, period);
    if(atr_handle == INVALID_HANDLE)
        return 0.0;

    double atr_buffer[];
    ArraySetAsSeries(atr_buffer, true);

    // 获取最新的ATR值
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) > 0 && atr_buffer[0] > 0)
    {
        // 释放指标句柄
        IndicatorRelease(atr_handle);
        return atr_buffer[0];
    }

    // 释放指标句柄
    IndicatorRelease(atr_handle);
    return 0.0;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化持仓信息对象
    PositionInfo = new CPositionInfo();

    // 初始化技术指标
    atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    if(atrHandle == INVALID_HANDLE)
    {
        Print("ATR指标初始化失败");
        return(INIT_FAILED);
    }

    adxHandle = iADX(_Symbol, PERIOD_CURRENT, ADXPeriod);
    if(adxHandle == INVALID_HANDLE)
    {
        Print("ADX指标初始化失败");
        return(INIT_FAILED);
    }

    // 设置初始资金
    initialEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    currentEquity = initialEquity;
    highestEquity = initialEquity;

    // 初始化交易变量
    currentBuyLayer = 0;
    currentSellLayer = 0;
    lastBuyPrice = 0;
    lastSellPrice = 0;

    // 设置交易参数
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    trade.SetDeviationInPoints(10);

    // 设置品种信息
    SymbolInfo.Name(Symbol());
    symbolPoint = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    symbolMinLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    symbolLotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    symbolMaxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

    // 获取交易属性的数值
    symbolTradeStopsLevel = GetSymbolIntegerInfo(_Symbol, SYMBOL_TRADE_STOPS_LEVEL, "交易止损级别");
    symbolTradeFreezeLevel = GetSymbolIntegerInfo(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL, "交易冻结级别");

    // 初始化当日收益相关变量
    daily_profit = 0.0;
    daily_floating_profit = 0.0;
    daily_closed_profit = 0.0;
    last_deal_ticket = 0;

    // 获取当前日期和时间（使用服务器时间）
    datetime server_time = TimeTradeServer();
    string current_date = TimeToString(server_time, TIME_DATE);

    // 设置当日起始余额和净值
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    daily_start_equity = AccountInfoDouble(ACCOUNT_EQUITY);

    // 设置最后检查时间为当前时间
    last_daily_check = server_time;
    last_history_check = 0; // 强制首次检查历史订单

    Print("初始化当日收益计算 - 日期: ", current_date,
          ", 起始余额: ", DoubleToString(daily_start_balance, 2),
          ", 起始净值: ", DoubleToString(daily_start_equity, 2));

    // 初始化时立即检查历史订单，计算当日已平仓盈亏
    CheckClosedDeals();

    // 创建UI界面
    CreateUI();

    // 创建信息面板
    CreateInfoPanel();

    // 设置按钮对齐方式为右下角
    ObjectSetInteger(0, "gp_button_buysell", OBJPROP_CORNER, CORNER_RIGHT_LOWER);
    ObjectSetInteger(0, "gp_button_close", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

    // 立即更新信息面板，确保显示正确的初始值
    UpdateInfoPanel();

    // 输出交易品种属性
    Print(_Symbol, " 交易属性 - 最小手数: ", symbolMinLot,
          ", 手数步长: ", symbolLotStep,
          ", 最大手数: ", symbolMaxLot,
          "最小止损距离: ", symbolTradeStopsLevel, " points");

    // 测试加仓手数计算
    TestLotCalculation();

    // 初始化ATR指标（用于止盈）
    if(UseATRBasedTP)
    {
        int tempATRHandle = iATR(_Symbol, ATR_Timeframe, ATR_Period_TP);
        if(tempATRHandle == INVALID_HANDLE)
        {
            Print("ATR(止盈)指标初始化失败");
            return(INIT_FAILED);
        }
        IndicatorRelease(tempATRHandle); // 释放临时句柄
        Print("ATR止盈策略已启用 - 周期: ", EnumToString(ATR_Timeframe), ", ATR周期: ", ATR_Period_TP);
    }

    return(INIT_SUCCEEDED);
}

// 获取整型交易属性的数值
int GetSymbolIntegerInfo(const string symbol, const ENUM_SYMBOL_INFO_INTEGER property_id, const string info_name)
{
    long value = 0;
    if (SymbolInfoInteger(symbol, property_id, value))
    {
        //Print("获取交易", info_name, ": ", value);
        return (int)value;
    }
    else
    {
        Print("获取", info_name, "失败: ", GetLastError());
        return 0;
    }
}

//+------------------------------------------------------------------+
//| 创建UI界面                                                       |
//+------------------------------------------------------------------+
void CreateUI()
{
    // 创建多空双向按钮
    btnBuySell = new Button();
    btnBuySell.Create("gp_button_buysell", 122, 100);
    btnBuySell.Size(100, 32);
    btnBuySell.Text("提特玛");
    btnBuySell.Font("极影毁片荧圆", 12);
    btnBuySell.Color(clrWhite);
    btnBuySell.BGColor(clrMediumBlue);
    btnBuySell.BorderColor(clrDarkBlue);
    btnBuySell.State(false);
    btnBuySell.Update();

    // 创建平仓按钮
    btnCloseAll = new Button();
    btnCloseAll.Create("gp_button_close", 122, 100-50);
    btnCloseAll.Size(100, 32);
    btnCloseAll.Text("收一桶");
    btnCloseAll.Font("极影毁片荧圆", 12);
    btnCloseAll.Color(clrWhite);
    btnCloseAll.BGColor(clrCrimson);
    btnCloseAll.BorderColor(clrBrown);
    btnCloseAll.State(false);
    btnCloseAll.Update();
}

//+------------------------------------------------------------------+
//| 应用UI主题                                                       |
//+------------------------------------------------------------------+
void ApplyTheme(ENUM_UI_THEME theme)
{
    color bgColor, textColor, titleColor, borderColor;
    color positiveColor, negativeColor, neutralColor;
    string fontName;

    switch(theme) {
        case THEME_DARK:
            bgColor = C'25,25,25';       // 深灰色背景
            textColor = clrWhiteSmoke;   // 白色文本
            titleColor = clrGold;        // 金色标题
            borderColor = clrDimGray;    // 灰色边框
            positiveColor = clrLime;     // 绿色正值
            negativeColor = clrCrimson;  // 红色负值
            neutralColor = clrLightGray; // 灰色中性值
            fontName = "Arial";
            break;

        case THEME_LIGHT:
            bgColor = clrWhiteSmoke;     // 白色背景
            textColor = clrNavy;         // 深蓝色文本
            titleColor = clrDarkBlue;    // 深蓝色标题
            borderColor = clrSilver;     // 银色边框
            positiveColor = clrForestGreen; // 深绿色正值
            negativeColor = clrFireBrick;   // 砖红色负值
            neutralColor = clrDarkGray;     // 深灰色中性值
            fontName = "Arial";
            break;

        case THEME_BLUE:
        default:
            bgColor = clrMediumBlue;     // 蓝色背景
            textColor = clrLightYellow;  // 淡黄色文本
            titleColor = clrWhiteSmoke;  // 白色标题
            borderColor = clrBlack;      // 黑色边框
            positiveColor = clrLime;     // 绿色正值
            negativeColor = clrRed;      // 红色负值
            neutralColor = clrYellow;    // 黄色中性值
            fontName = "极影毁片荧圆";
            break;
    }

    // 应用背景颜色
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, borderColor);

    // 应用标题颜色
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, titleColor);
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, fontName);

    // 应用标签颜色
    string labels[] = {"Equity", "Position", "DailyPL", "RiskLevel", "ATR", "ATRTP", "ADX"};
    for(int i = 0; i < ArraySize(labels); i++)
    {
        ObjectSetInteger(0, "InfoPanel_" + labels[i], OBJPROP_COLOR, textColor);
        ObjectSetString(0, "InfoPanel_" + labels[i], OBJPROP_FONT, fontName);
    }

    // 保存主题颜色到全局变量，供其他函数使用
    g_positiveColor = positiveColor;
    g_negativeColor = negativeColor;
    g_neutralColor = neutralColor;
    g_textColor = textColor;
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
    // 创建信息面板背景
    ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, 5);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, 25);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 226);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 212); // 调整高度以适应新的标签间距
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);

    // 创建标题
    ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, 33);
    ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "Bittma EA");
    ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, 16);

    // 创建精简的标签（每个标签间隔2个像素）
    CreateInfoLabel("Equity", "净值: 0.00", 60);
    CreateInfoLabel("Position", "持仓: 0.00", 82);
    CreateInfoLabel("DailyPL", "当日盈亏: 0.00", 104); // 只显示已实现盈亏
    CreateInfoLabel("RiskLevel", "风险水平: 0.0%", 126);
    CreateInfoLabel("ATR", "ATR(14): 0.0", 148);
    CreateInfoLabel("ATRTP", "ATR止盈目标: 0.0", 170);
    CreateInfoLabel("ADX", "ADX(14): 0.0", 192);

    // 应用当前主题
    ApplyTheme(UITheme);
}

//+------------------------------------------------------------------+
//| 创建信息标签                                                     |
//+------------------------------------------------------------------+
void CreateInfoLabel(string name, string initial_text, int y_distance)
{
    ObjectCreate(0, "InfoPanel_" + name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "InfoPanel_" + name, OBJPROP_XDISTANCE, 17);
    ObjectSetInteger(0, "InfoPanel_" + name, OBJPROP_YDISTANCE, y_distance);
    ObjectSetString(0, "InfoPanel_" + name, OBJPROP_TEXT, initial_text);
    ObjectSetInteger(0, "InfoPanel_" + name, OBJPROP_FONTSIZE, 16);
    // 注意：颜色和字体将由ApplyTheme函数设置
}

//+------------------------------------------------------------------+
//| 更新信息面板 - 优化版                                            |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
    static datetime lastFullUpdate = 0;
    static datetime lastPartialUpdate = 0;
    static double lastEquity = 0;
    static double lastTotalVolume = 0;
    static double lastDailyProfit = 0;
    static double lastRisk = 0;
    static double lastATR = 0;
    static double lastADX = 0;

    datetime currentTime = TimeCurrent();

    // 使用缓存系统获取数据
    account_equity = CacheManager::GetCachedEquity();
    account_balance = CacheManager::GetCachedBalance();
    account_profit = CacheManager::GetCachedProfit();

    // 计算当日收益 - 每5秒更新一次
    if(currentTime - lastPartialUpdate >= 5)
    {
        CalculateDailyProfit();
        lastPartialUpdate = currentTime;
    }

    // 检查是否需要完整更新UI - 每30秒或数据变化时
    bool needFullUpdate = (currentTime - lastFullUpdate >= 30);

    // 检查关键数据是否有变化
    if(!needFullUpdate)
    {
        needFullUpdate = (MathAbs(account_equity - lastEquity) > 0.01) ||
                         (MathAbs(total_volume - lastTotalVolume) > 0.001) ||
                         (MathAbs(daily_profit - lastDailyProfit) > 0.01);
    }

    // 如果需要完整更新UI
    if(needFullUpdate)
    {
        // 更新账户净值
        ObjectSetString(0, "InfoPanel_Equity", OBJPROP_TEXT, StringFormat("净值: %.2f", account_equity));

        // 获取持仓信息 - 使用缓存系统
        long_volume = CacheManager::GetCachedLongVolume();
        short_volume = CacheManager::GetCachedShortVolume();
        total_volume = CacheManager::GetCachedTotalVolume();

        // 确定持仓颜色（多单绿色，空单红色，多空都有黄色）
        color position_color;
        if(long_volume > 0 && short_volume > 0)
            position_color = g_neutralColor;
        else if(long_volume > 0)
            position_color = g_positiveColor;
        else if(short_volume > 0)
            position_color = g_negativeColor;
        else
            position_color = g_textColor;

        // 更新持仓信息
        ObjectSetString(0, "InfoPanel_Position", OBJPROP_TEXT, StringFormat("持仓: %.2f", total_volume));
        ObjectSetInteger(0, "InfoPanel_Position", OBJPROP_COLOR, position_color);

        // 更新当日已实现盈亏
        string daily_pl_text = StringFormat("当日盈亏: %.2f", daily_profit);
        color daily_pl_color = (daily_profit >= 0) ? g_positiveColor : g_negativeColor;
        ObjectSetString(0, "InfoPanel_DailyPL", OBJPROP_TEXT, daily_pl_text);
        ObjectSetInteger(0, "InfoPanel_DailyPL", OBJPROP_COLOR, daily_pl_color);

        // 更新风险指标
        UpdateRiskIndicator();

        // 更新技术指标信息
        UpdateIndicatorInfo();

        // 保存当前值用于下次比较
        lastEquity = account_equity;
        lastTotalVolume = total_volume;
        lastDailyProfit = daily_profit;
        lastFullUpdate = currentTime;
    }
}

//+------------------------------------------------------------------+
//| 计算当日收益 - 仅计算已实现盈亏                                   |
//+------------------------------------------------------------------+
void CalculateDailyProfit()
{
    // 使用服务器时间而不是本地时间
    datetime serverTime = TimeTradeServer();
    string today = TimeToString(serverTime, TIME_DATE);
    string last_check_date = (last_daily_check == 0) ? "" : TimeToString(last_daily_check, TIME_DATE);

    // 检查是否是新的一天
    if(last_daily_check == 0 || last_check_date != today)
    {
        // 新的一天开始
        Print("检测到日期变更: ", last_check_date, " -> ", today);

        // 保存当前净值作为新一天的起始净值
        daily_start_equity = AccountInfoDouble(ACCOUNT_EQUITY);

        // 重置当日已平仓盈亏和入金/出金记录
        daily_closed_profit = 0.0;
        daily_net_deposits = 0.0;
        last_deal_ticket = 0;

        // 更新检查时间
        last_daily_check = serverTime;
        last_history_check = 0; // 强制检查历史订单

        Print("新的一天开始，起始净值设置为: ", DoubleToString(daily_start_equity, 2));
        return;
    }

    // 控制检查频率
    if(serverTime - last_history_check < 5 && last_history_check != 0)
        return;

    last_history_check = serverTime;

    // 计算已平仓盈亏和资金变动
    double closedProfit = 0.0;
    double netDeposits = 0.0;

    HistorySelect(last_daily_check, serverTime);
    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);

        // 跳过已处理的交易
        if((int)ticket <= last_deal_ticket && last_deal_ticket != 0)
            continue;

        // 只处理当前EA的交易
        if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber)
        {
            ENUM_DEAL_ENTRY entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(ticket, DEAL_ENTRY);
            ENUM_DEAL_TYPE type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);

            // 处理入金/出金
            if(type == DEAL_TYPE_BALANCE)
            {
                double amount = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                netDeposits += amount;
                Print("检测到资金变动: ", amount, ", 累计净入金: ", netDeposits);
            }
            // 处理平仓盈亏
            else if(entry == DEAL_ENTRY_OUT || entry == DEAL_ENTRY_INOUT)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                closedProfit += profit;
                Print("检测到平仓: ", profit, ", 累计平仓盈亏: ", closedProfit);
            }
        }

        // 更新最后处理的交易单号
        if((int)ticket > last_deal_ticket)
            last_deal_ticket = (int)ticket;
    }

    // 更新当日数据
    daily_closed_profit = closedProfit;
    daily_net_deposits = netDeposits;

    // 最终收益计算 = 已平仓盈亏
    daily_profit = daily_closed_profit;
}

//+------------------------------------------------------------------+
//| 检查已平仓订单，计算当日已实现盈亏                                |
//+------------------------------------------------------------------+
void CheckClosedDeals()
{
    // 控制检查频率，避免频繁查询历史订单
    datetime current_time = TimeCurrent();
    if(current_time - last_history_check < 5 && last_history_check != 0) // 每5秒检查一次
        return;

    last_history_check = current_time;

    // 获取当前日期的起始时间
    datetime today_start = StringToTime(TimeToString(current_time, TIME_DATE));

    // 获取历史交易记录
    HistorySelect(today_start, current_time);
    int total_deals = HistoryDealsTotal();

    // 如果没有新的交易记录，直接返回
    if(total_deals == 0)
        return;

    // 从上次检查的位置开始，计算新的已平仓盈亏
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);

        // 跳过已经检查过的交易
        if((int)deal_ticket <= last_deal_ticket && last_deal_ticket != 0)
            continue;

        // 只计算当前交易品种和魔术号的盈亏
        if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != _Symbol || HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != MagicNumber)
            continue;

        // 只计算OUT类型的交易（平仓交易）
        if(HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
        {
            double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
            daily_closed_profit += deal_profit;

            // 输出调试信息
            if(deal_profit != 0)
            {
                string deal_time = TimeToString((datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME), TIME_DATE|TIME_MINUTES|TIME_SECONDS);
                Print("检测到平仓交易 #", deal_ticket, " 时间: ", deal_time, ", 盈亏: ", DoubleToString(deal_profit, 2),
                      ", 累计已平仓盈亏: ", DoubleToString(daily_closed_profit, 2));
            }
        }

        // 更新最后检查的交易单号
        if((int)deal_ticket > last_deal_ticket)
            last_deal_ticket = (int)deal_ticket;
    }
}

//+------------------------------------------------------------------+
//| Expert tick function - 优化版                                      |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime lastTickProcessTime = 0;
    datetime currentTime = TimeCurrent();

    // 控制Tick处理频率 - 每毫秒处理一次
    if(currentTime == lastTickProcessTime)
        return;

    lastTickProcessTime = currentTime;

    // 更新市场数据 - 使用缓存系统
    if(!UpdateMarketData())
        return;

    // 更新信息面板 - 已优化为只在需要时更新
    UpdateInfoPanel();

    // 检查账户风险 - 已优化为使用缓存的风险值
    CheckAccountRisk();

    // 检查是否需要加仓 - 已优化为使用缓存的价格和指标值
    CheckScaleInPositions();

    // 管理已有仓位 - 已优化为减少不必要的计算
    ManageOpenPositions();
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 释放指标句柄
    if(atrHandle != INVALID_HANDLE)
        IndicatorRelease(atrHandle);
    if(adxHandle != INVALID_HANDLE)
        IndicatorRelease(adxHandle);

    // 删除信息面板和UI界面
    ObjectsDeleteAll(0, "InfoPanel_");
    ObjectsDeleteAll(0, "gp_button_");

    // 释放持仓信息对象
    if(CheckPointer(PositionInfo) == POINTER_DYNAMIC)
        delete PositionInfo;
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理按钮点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "gp_button_buysell")
        {
            // 多空双向下单按钮
            OpenBuyOrder();     // 开多单
            OpenSellOrder();   // 开空单
            btnBuySell.State(false);
            btnBuySell.Update();
        }
        else if(sparam == "gp_button_close")
        {
            // 平仓按钮点击 - 使用新函数平掉所有仓位包括首单
            CloseAllPositionsIncludingInitial();
            btnCloseAll.State(false);
            btnCloseAll.Update();
        }
        else if(sparam == "InfoPanel_Title")
        {
            // 点击标题切换主题
            static int currentTheme = (int)UITheme;
            currentTheme = (currentTheme + 1) % 3; // 循环切换三种主题
            ApplyTheme((ENUM_UI_THEME)currentTheme);
            Print("主题已切换为: ", EnumToString((ENUM_UI_THEME)currentTheme));
        }
    }
}

//+------------------------------------------------------------------+
//| 计算账户风险水平                                                  |
//+------------------------------------------------------------------+
double CalculateAccountRisk()
{
    // 确保使用最新的账户净值
    currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);

    // 计算账户风险因素 - 基于初始净值的回撤百分比
    double equityRisk = 0.0;
    if(initialEquity > 0 && currentEquity < initialEquity)
    {
        equityRisk = (initialEquity - currentEquity) / initialEquity * 100;
    }

    // 重要：直接使用账户回撤作为主要风险指标
    // 这样可以确保风险值直接反映账户实际损失百分比
    double mainRisk = equityRisk;

    // 计算持仓风险因素 - 仅作为参考，不直接影响主风险值
    double positionRisk = 0.0;
    if(account_equity > 0 && total_volume > 0)
    {
        double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double totalExposure = total_volume * contractSize * currentPrice;

        // 计算持仓风险 - 归一化到0-100范围
        positionRisk = (totalExposure / account_equity) * 25; // 降低系数，避免过高估计风险
        positionRisk = MathMin(positionRisk, 100);
    }

    // 计算波动性风险因素 - 仅作为参考，不直接影响主风险值
    double volatilityRisk = 0.0;
    double atr = GetCurrentATR();
    double atrPoints = atr / SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    volatilityRisk = MathMin(atrPoints / 20.0, 100); // 降低系数，避免过高估计风险

    // 计算辅助风险指标 - 仅用于日志和UI显示，不影响平仓决策
    double auxiliaryRisk = (positionRisk * 0.7) + (volatilityRisk * 0.3);

    // 输出详细的风险计算信息（每分钟一次）
    static datetime lastDetailedLogTime = 0;
    datetime currentTime = TimeCurrent();
    if(currentTime - lastDetailedLogTime >= 60) // 每60秒输出一次
    {
        PrintFormat("风险计算详情 - 账户回撤: %.2f%%, 持仓风险: %.2f%%, 波动性风险: %.2f%%, 辅助风险: %.2f%%",
                   equityRisk, positionRisk, volatilityRisk, auxiliaryRisk);
        PrintFormat("账户数据 - 初始净值: %.2f, 当前净值: %.2f, 差额: %.2f, 回撤比例: %.2f%%",
                   initialEquity, currentEquity, initialEquity - currentEquity, equityRisk);
        lastDetailedLogTime = currentTime;
    }

    // 返回主风险值（即账户回撤百分比）
    return mainRisk;
}

//+------------------------------------------------------------------+
//| 更新风险指标显示 - 优化版                                         |
//+------------------------------------------------------------------+
void UpdateRiskIndicator()
{
    static datetime lastRiskUpdate = 0;
    static double lastRiskValue = 0;

    datetime currentTime = TimeCurrent();

    // 控制风险计算频率 - 每5秒计算一次
    if(currentTime - lastRiskUpdate >= 5 || lastRiskValue == 0)
    {
        // 获取主风险值（账户回撤百分比）
        lastRiskValue = CalculateAccountRisk(); // 0-100%
        lastRiskUpdate = currentTime;
    }

    double risk = lastRiskValue;

    // 设置风险颜色
    color riskColor;
    if(risk < MaxRiskPercent * 0.5) // 低于最大风险的50%
        riskColor = g_positiveColor;
    else if(risk < MaxRiskPercent * 0.8) // 低于最大风险的80%
        riskColor = g_neutralColor;
    else // 接近或超过最大风险
        riskColor = g_negativeColor;

    // 显示风险百分比和最大允许风险
    string riskText = "风险: " + DoubleToString(risk, 1) + "% / " + DoubleToString(MaxRiskPercent, 1) + "%";
    ObjectSetString(0, "InfoPanel_RiskLevel", OBJPROP_TEXT, riskText);
    ObjectSetInteger(0, "InfoPanel_RiskLevel", OBJPROP_COLOR, riskColor);

    // 如果风险接近最大值，闪烁显示
    static bool blinkState = false;
    static datetime lastBlinkTime = 0;

    if(risk >= MaxRiskPercent * 0.9) // 风险超过最大值的90%
    {
        if(currentTime - lastBlinkTime >= 1) // 每秒切换一次颜色
        {
            blinkState = !blinkState;
            lastBlinkTime = currentTime;

            if(blinkState)
                ObjectSetInteger(0, "InfoPanel_RiskLevel", OBJPROP_COLOR, clrWhite);
            else
                ObjectSetInteger(0, "InfoPanel_RiskLevel", OBJPROP_COLOR, g_negativeColor);
        }
    }
}

//+------------------------------------------------------------------+
//| 更新ATR止盈目标信息                                               |
//+------------------------------------------------------------------+
void UpdateATRTakeProfitInfo()
{
    // 如果未启用ATR止盈策略，则显示禁用信息
    if(!UseATRBasedTP)
    {
        ObjectSetString(0, "InfoPanel_ATRTP", OBJPROP_TEXT, "ATR止盈: 未启用");
        ObjectSetInteger(0, "InfoPanel_ATRTP", OBJPROP_COLOR, g_textColor);
        return;
    }

    // 获取持仓总量
    double totalVolume = CacheManager::GetCachedTotalVolume();
    if(totalVolume <= 0)
    {
        ObjectSetString(0, "InfoPanel_ATRTP", OBJPROP_TEXT, "ATR止盈: 无持仓");
        ObjectSetInteger(0, "InfoPanel_ATRTP", OBJPROP_COLOR, g_textColor);
        return;
    }

    // 获取3日ATR值
    double atr = CalculateATRForTimeframe(ATR_Timeframe, ATR_Period_TP);
    if(atr <= 0)
    {
        ObjectSetString(0, "InfoPanel_ATRTP", OBJPROP_TEXT, "ATR止盈: 计算中...");
        ObjectSetInteger(0, "InfoPanel_ATRTP", OBJPROP_COLOR, g_textColor);
        return;
    }

    // 计算每点的价值
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double pointValue = tickValue / SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE) * SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    // 计算ATR对应的盈利金额
    double atrValue = atr * pointValue * totalVolume * 100; // 转换为标准手

    // 计算下一个目标
    double nextTarget = 0;
    string targetLabel = "";
    color targetColor = g_textColor;

    // 计算当前盈利
    double currentProfit = currentEquity - initialEquity;

    // 根据当前阶段确定下一个目标
    if(!firstCloseDone)
    {
        nextTarget = atrValue * ATR_TP_Multiplier1;
        targetLabel = "目标1";
        targetColor = g_positiveColor;
    }
    else if(!secondCloseDone)
    {
        nextTarget = atrValue * ATR_TP_Multiplier2;
        targetLabel = "目标2";
        targetColor = g_positiveColor;
    }
    else
    {
        nextTarget = atrValue * ATR_TP_Multiplier3;
        targetLabel = "目标3";
        targetColor = g_positiveColor;
    }

    // 计算进度百分比
    double progressPercent = (currentProfit > 0 && nextTarget > 0) ? (currentProfit / nextTarget) * 100 : 0;

    // 根据进度调整颜色
    if(progressPercent >= 90)
        targetColor = g_neutralColor; // 接近目标
    else if(progressPercent >= 70)
        targetColor = g_positiveColor; // 良好进度

    // 更新UI显示
    string atrTpText = StringFormat("ATR止盈(%s): %.1f%%", targetLabel, progressPercent);
    ObjectSetString(0, "InfoPanel_ATRTP", OBJPROP_TEXT, atrTpText);
    ObjectSetInteger(0, "InfoPanel_ATRTP", OBJPROP_COLOR, targetColor);
}

//+------------------------------------------------------------------+
//| 更新技术指标信息 - 优化版                                         |
//+------------------------------------------------------------------+
void UpdateIndicatorInfo()
{
    static datetime lastIndicatorUpdate = 0;
    static double lastATRValue = 0;
    static double lastADXValue = 0;
    static string lastATRText = "";
    static color lastATRColor = clrNONE;
    static color lastADXColor = clrNONE;

    datetime currentTime = TimeCurrent();

    // 控制更新频率 - 每10秒更新一次或首次调用
    if(currentTime - lastIndicatorUpdate >= 10 || lastATRValue == 0)
    {
        // 使用缓存系统获取指标值
        double rawAtr = CacheManager::GetCachedATR();
        double adx = CacheManager::GetCachedADX();

        // 只有在值变化时才更新UI
        if(MathAbs(rawAtr - lastATRValue) > 0.0001 || MathAbs(adx - lastADXValue) > 0.1 || lastATRText == "")
        {
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            double atrPoints = rawAtr / point;

            // 获取交易品种信息
            string symbolName = _Symbol;
            int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS); // 获取品种小数位数

            // 简化ATR显示格式，与指标面板保持一致
            string atrText = "";

            // 黄金特殊处理 - 使用与指标面板一致的格式
            if(StringFind(symbolName, "XAU") >= 0 || StringFind(symbolName, "GOLD") >= 0)
            {
                // 黄金ATR显示为原始值，保留3位小数
                atrText = "ATR(14): " + DoubleToString(rawAtr, 3);
            }
            // 外汇对特殊处理
            else if(StringFind(symbolName, "USD") >= 0 || StringFind(symbolName, "EUR") >= 0 ||
                    StringFind(symbolName, "GBP") >= 0 || StringFind(symbolName, "JPY") >= 0)
            {
                // 外汇对ATR显示为原始值，保留5位小数
                atrText = "ATR(14): " + DoubleToString(rawAtr, 5);
            }
            // 其他品种通用处理
            else
            {
                // 使用原始ATR值，保留与品种相同的小数位数
                atrText = "ATR(14): " + DoubleToString(rawAtr, digits);
            }

            // 根据ATR值设置颜色（高波动性显示为警告色）
            // 为不同品种设置不同的阈值
            double volatilityThreshold = 0.0;

            if(StringFind(symbolName, "XAU") >= 0 || StringFind(symbolName, "GOLD") >= 0)
            {
                // 黄金波动性阈值（点数）
                volatilityThreshold = atrPoints / 500.0; // 例如：500点为中等波动
            }
            else if(StringFind(symbolName, "JPY") >= 0)
            {
                // 日元对波动性阈值
                volatilityThreshold = atrPoints / 100.0; // 例如：100点为中等波动
            }
            else
            {
                // 其他品种波动性阈值
                volatilityThreshold = atrPoints / 50.0; // 例如：50点为中等波动
            }

            // 设置颜色
            color atrColor = g_textColor;
            if(volatilityThreshold > 0.8) // 高波动性
                atrColor = g_negativeColor;
            else if(volatilityThreshold > 0.4) // 中等波动性
                atrColor = g_neutralColor;
            else // 低波动性
                atrColor = g_positiveColor;

            // 颜色标识趋势强度
            color adxColor = adx > 25 ? g_positiveColor : g_negativeColor;

            // 只有在文本或颜色变化时才更新UI
            if(atrText != lastATRText || atrColor != lastATRColor)
            {
                ObjectSetString(0, "InfoPanel_ATR", OBJPROP_TEXT, atrText);
                ObjectSetInteger(0, "InfoPanel_ATR", OBJPROP_COLOR, atrColor);
                lastATRText = atrText;
                lastATRColor = atrColor;
            }

            if(adx != lastADXValue || adxColor != lastADXColor)
            {
                ObjectSetString(0, "InfoPanel_ADX", OBJPROP_TEXT, "ADX(14): " + DoubleToString(adx, 1));
                ObjectSetInteger(0, "InfoPanel_ADX", OBJPROP_COLOR, adxColor);
                lastADXColor = adxColor;
            }

            // 保存当前值用于下次比较
            lastATRValue = rawAtr;
            lastADXValue = adx;
        }

        // 更新ATR止盈目标信息
        UpdateATRTakeProfitInfo();

        lastIndicatorUpdate = currentTime;
    }
}

//+------------------------------------------------------------------+
//| 检查账户风险 - 优化版                                             |
//+------------------------------------------------------------------+
void CheckAccountRisk()
{
    static datetime lastRiskCheckTime = 0;
    static double lastRiskValue = 0;
    static bool warningIssued = false;

    datetime currentTime = TimeCurrent();

    // 控制风险检查频率 - 每5秒检查一次
    if(currentTime - lastRiskCheckTime < 5 && lastRiskValue > 0)
    {
        // 使用缓存的风险值
        double risk = lastRiskValue;

        // 如果风险值已经接近或超过最大允许值，则增加检查频率
        if(risk >= MaxRiskPercent * 0.9)
        {
            // 重新计算风险值以确保准确性
            risk = CalculateAccountRisk();
            lastRiskValue = risk;
        }
        else
        {
            // 使用缓存的风险值，不重新计算
            return;
        }
    }
    else
    {
        // 计算新的风险值
        double risk = CalculateAccountRisk();
        lastRiskValue = risk;
        lastRiskCheckTime = currentTime;

        // 添加详细的风险计算日志（每分钟输出一次）
        static datetime lastRiskLogTime = 0;
        if(currentTime - lastRiskLogTime >= 60)
        {
            // 简化风险日志输出
            double drawdown = (highestEquity > 0) ? (highestEquity - currentEquity) / highestEquity * 100 : 0;
            PrintFormat("风险: %.1f%% (最大: %.1f%%), 回撤: %.1f%%", risk, MaxRiskPercent, drawdown);

            lastRiskLogTime = currentTime;
        }
    }

    double risk = lastRiskValue;

    // 添加0.1%的容差值，避免在风险值非常接近最大风险值时触发平仓
    double riskTolerance = 0.1; // 0.1%的容差

    // 当风险接近最大允许值时，提供更详细的日志
    if(risk >= MaxRiskPercent * 0.8 && risk < MaxRiskPercent)
    {
        static datetime lastWarningTime = 0;
        if(currentTime - lastWarningTime >= 30) // 每30秒输出一次警告
        {
            Print("警告：风险接近上限 ", DoubleToString(risk, 1), "%");
            lastWarningTime = currentTime;
            warningIssued = true;
        }
    }
    // 当风险值接近但未超过最大风险值时发出警告
    else if(risk >= MaxRiskPercent && risk < (MaxRiskPercent + riskTolerance))
    {
        static datetime lastWarningTime = 0;
        if(currentTime - lastWarningTime >= 30) // 每30秒输出一次警告
        {
            Print("警告：风险达到上限 ", DoubleToString(risk, 1), "%");
            lastWarningTime = currentTime;
            warningIssued = true;
        }
    }
    // 当风险值超过最大允许值时触发平仓
    else if(risk >= (MaxRiskPercent + riskTolerance))
    {
        // 触发平仓前再次确认风险计算
        double confirmRisk = CalculateAccountRisk();
        if(confirmRisk >= (MaxRiskPercent + riskTolerance))
        {
            // 简化风险平仓日志
            Print("触发风险平仓 - 风险: ", DoubleToString(confirmRisk, 1), "%, 净值: ", DoubleToString(currentEquity, 2));

            CloseAllPositionsIncludingInitial();
            Print("账户风险达到 ", confirmRisk, "%, 已平仓所有持仓");
        }
    }
    // 如果风险值降低，重置警告标志
    else if(warningIssued && risk < MaxRiskPercent * 0.7)
    {
        warningIssued = false;
    }
}

//+------------------------------------------------------------------+
//| 获取动态加仓间距 - 优化版                                        |
//+------------------------------------------------------------------+
double GetDynamicSpacing()
{
    static datetime lastSpacingUpdate = 0;
    static double lastSpacingValue = 0;

    datetime currentTime = TimeCurrent();

    // 控制更新频率 - 每30秒更新一次或首次调用
    if(currentTime - lastSpacingUpdate < 30 && lastSpacingValue > 0)
    {
        return lastSpacingValue;
    }

    // 使用缓存系统获取ATR值
    double atr = CacheManager::GetCachedATR();
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double atrPoints = atr / point;  // 将ATR转换为点数

    // 计算基础调整后的间距
    double baseSpacing = ScaleInSpacing;
    double volatilityAdjustment = (atrPoints / 100.0) * VolatilityFactor; // 标准化ATR
    double adjustedSpacing = baseSpacing * (1 + volatilityAdjustment);

    // 确保间距在允许范围内
    adjustedSpacing = MathMax(adjustedSpacing, MinSpacing);
    adjustedSpacing = MathMin(adjustedSpacing, MaxSpacing);

    // 输出调试信息 - 减少日志频率
    static datetime lastPrintTime = 0;
    if(currentTime - lastPrintTime >= 300)  // 每5分钟输出一次
    {
        PrintFormat("交易间距计算详情：ATR值(点): %.1f, 波动率调整: %.2f, 最终间距: %.1f",
                   atrPoints, volatilityAdjustment, adjustedSpacing);
        lastPrintTime = currentTime;
    }

    // 更新缓存
    lastSpacingValue = adjustedSpacing;
    lastSpacingUpdate = currentTime;

    return adjustedSpacing;
}

//+------------------------------------------------------------------+
//| 检查趋势强度 - 优化版                                            |
//+------------------------------------------------------------------+
bool CheckTrendStrength()
{
    // 使用缓存系统获取ADX值
    double adx = CacheManager::GetCachedADX();

    // 如果ADX值无效，返回false
    if(adx <= 0)
        return false;

    return (adx >= MinADX);
}

//+------------------------------------------------------------------+
//| 检查是否需要加仓 - 优化版                                         |
//+------------------------------------------------------------------+
void CheckScaleInPositions()
{
    static datetime lastCheckTime = 0;
    datetime currentTime = TimeCurrent();

    // 控制检查频率 - 每秒检查一次
    if(currentTime - lastCheckTime < 1)
        return;

    lastCheckTime = currentTime;

    // 检查是否已达到最大层数
    if(currentLayer >= MaxLayers)
        return;

    // 检查趋势强度
    if(!CheckTrendStrength())
    {
        // 减少日志输出频率
        static datetime lastTrendLogTime = 0;
        if(currentTime - lastTrendLogTime >= 60) // 每分钟输出一次
        {
            Print("趋势强度不足，暂停加仓");
            lastTrendLogTime = currentTime;
        }
        return;
    }

    // 获取动态间距
    double spacing = GetDynamicSpacing() * symbolPoint;

    // 使用缓存系统获取价格
    double ask = CacheManager::GetCachedAsk();
    double bid = CacheManager::GetCachedBid();

    // 检查多单加仓条件
    if(lastBuyPrice > 0 && (ask - lastBuyPrice) >= spacing)
    {
        OpenScaleInBuy();
    }

    // 检查空单加仓条件
    if(lastSellPrice > 0 && (lastSellPrice - bid) >= spacing)
    {
        OpenScaleInSell();
    }
}

//+------------------------------------------------------------------+
//| 格式化手数                                                      |
//+------------------------------------------------------------------+
double formatlots(string symbol, double lots)
{
    double minilots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double steplots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
    double maxlots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);

    // 添加调试日志
    static datetime lastLogTime = 0;
    datetime currentTime = TimeCurrent();
    bool shouldLog = (currentTime - lastLogTime >= 60); // 每分钟记录一次

    if(shouldLog)
    {
        Print("手数格式化 - 原始手数: ", DoubleToString(lots, 3),
              ", 最小手数: ", DoubleToString(minilots, 3),
              ", 步长: ", DoubleToString(steplots, 3),
              ", 最大手数: ", DoubleToString(maxlots, 3));
    }

    // 检查最小手数
    if(lots < minilots)
    {
        if(shouldLog)
            Print("手数小于最小值，使用最小手数: ", DoubleToString(minilots, 3));
        return minilots;
    }

    // 检查最大手数
    if(lots > maxlots)
    {
        if(shouldLog)
            Print("手数大于最大值，使用最大手数: ", DoubleToString(maxlots, 3));
        return maxlots;
    }

    // 计算步数（使用四舍五入取整）
    int steps = (int)MathRound(lots / steplots);

    // 计算规范化后的手数
    double normalizedLots = steps * steplots;

    // 确保手数在允许范围内
    normalizedLots = MathMax(normalizedLots, minilots);
    normalizedLots = MathMin(normalizedLots, maxlots);

    // 添加结果日志
    if(shouldLog)
    {
        Print("手数格式化结果 - 原始手数: ", DoubleToString(lots, 3),
              ", 规范化后: ", DoubleToString(normalizedLots, 3),
              ", 步数: ", steps);
        lastLogTime = currentTime;
    }

    return normalizedLots;
}

//+------------------------------------------------------------------+
//| 测试加仓手数计算                                                 |
//+------------------------------------------------------------------+
#ifdef DEBUG
void TestLotCalculation()
{
    double initialLot = FixedLotSize;  // 初始手数
    double currentLot = initialLot;

    // 模拟加仓层级1到15的过程
    for(int layer = 1; layer <= MaxLayers; layer++)
    {
        currentLot = CalculateNewLotSize(currentLot, layer);
    }
}
#else
// 发布版本中的简化版本
void TestLotCalculation()
{
    // 仅测试第一层加仓
    double initialLot = FixedLotSize;
    double newLot = CalculateNewLotSize(initialLot, 1);
    // 不输出任何日志
}
#endif

//+------------------------------------------------------------------+
//| 计算新的加仓手数                                                |
//| previousLot: 上一单的手数                                       |
//| layer: 当前加仓层级（从1开始计数）                             |
//+------------------------------------------------------------------+
double CalculateNewLotSize(double previousLot, int layer)
{
    double multiplier = GetMultiplierForLayer(layer);

    // 计算新手数，保留到小数点后第3位
    double newLot = NormalizeDouble(previousLot * multiplier, 3);

    // 添加日志输出
    static datetime lastLogTime = 0;
    datetime currentTime = TimeCurrent();
    if(currentTime - lastLogTime >= 60) // 每分钟记录一次
    {
        Print("手数计算 - 上一手数: ", DoubleToString(previousLot, 3),
              ", 乘数: ", DoubleToString(multiplier, 2),
              ", 计算结果: ", DoubleToString(newLot, 3));
        lastLogTime = currentTime;
    }

    // 确保手数不小于最小手数
    if(newLot < symbolMinLot)
        newLot = symbolMinLot;

    // 确保手数不超过最大手数
    if(newLot > symbolMaxLot)
        newLot = symbolMaxLot;

    return newLot;
}

//+------------------------------------------------------------------+
//| 多单加仓 - 优化版                                                |
//+------------------------------------------------------------------+
void OpenScaleInBuy()
{
    // 使用缓存系统获取价格
    double ask = CacheManager::GetCachedAsk();

    // 如果价格无效，尝试直接获取
    if(ask <= 0)
    {
        MqlTick last_tick;
        if(!SymbolInfoTick(Symbol(), last_tick))
        {
            Print("获取市场价格失败!");
            return;
        }
        ask = last_tick.ask;
    }

    // 获取上一次多单的手数
    double lastLotSize = GetLastPositionVolume(true);

    // 计算新的加仓手数
    double newLotSize = CalculateNewLotSize(lastLotSize, currentBuyLayer + 1);
    double adjustedLotSize = formatlots(_Symbol, newLotSize);

    if(adjustedLotSize > 0)
    {
        string comment = "Buy-" + IntegerToString(currentBuyLayer + 1);
        if(trade.Buy(adjustedLotSize, Symbol(), ask, 0, 0, comment))
        {
            lastBuyPrice = ask;
            currentBuyLayer++;
            Print("多单加仓成功，层数: ", currentBuyLayer, ", 手数: ", DoubleToString(adjustedLotSize, 2));
        }
        else
        {
            int error = GetLastError();
            Print("多单加仓失败，错误: ", GetErrorDescription(error));
        }
    }
}

//+------------------------------------------------------------------+
//| 空单加仓 - 优化版                                                |
//+------------------------------------------------------------------+
void OpenScaleInSell()
{
    // 使用缓存系统获取价格
    double bid = CacheManager::GetCachedBid();

    // 如果价格无效，尝试直接获取
    if(bid <= 0)
    {
        MqlTick last_tick;
        if(!SymbolInfoTick(Symbol(), last_tick))
        {
            Print("获取市场价格失败!");
            return;
        }
        bid = last_tick.bid;
    }

    // 获取上一次空单的手数
    double lastLotSize = GetLastPositionVolume(false);

    // 计算新的加仓手数
    double newLotSize = CalculateNewLotSize(lastLotSize, currentSellLayer + 1);
    double adjustedLotSize = formatlots(_Symbol, newLotSize);

    if(adjustedLotSize > 0)
    {
        string comment = "Sell-" + IntegerToString(currentSellLayer + 1);
        if(trade.Sell(adjustedLotSize, Symbol(), bid, 0, 0, comment))
        {
            lastSellPrice = bid;
            currentSellLayer++;
            Print("空单加仓成功，层数: ", currentSellLayer, ", 手数: ", DoubleToString(adjustedLotSize, 2));
        }
        else
        {
            int error = GetLastError();
            Print("空单加仓失败，错误: ", GetErrorDescription(error));
        }
    }
}

//+------------------------------------------------------------------+
//| 获取错误描述                                                     |
//+------------------------------------------------------------------+
string GetErrorDescription(int error_code)
{
    string error_string;

    switch(error_code)
    {
        case 10014:  // TRADE_RETCODE_INVALID_VOLUME
            error_string = "无效的交易量";
            break;
        case 10016:  // TRADE_RETCODE_INVALID_SL_OR_TP
            error_string = "无效的止损/止盈价格";
            break;
        case 10015:  // TRADE_RETCODE_INVALID_PRICE
            error_string = "无效的价格";
            break;
        case 10019:  // TRADE_RETCODE_NO_MONEY
            error_string = "资金不足";
            break;
        case 10017:  // TRADE_RETCODE_TRADE_DISABLED
            error_string = "交易被禁用";
            break;
        case 10018:  // TRADE_RETCODE_MARKET_CLOSED
            error_string = "市场已关闭";
            break;
        case 10013:  // TRADE_RETCODE_INVALID_TRADE_PARAMETERS
            error_string = "无效的交易参数";
            break;
        case 10021:  // TRADE_RETCODE_CONNECTION
            error_string = "没有连接到交易服务器";
            break;
        case 10012:  // TRADE_RETCODE_FROZEN
            error_string = "交易被冻结";
            break;
        default:
            error_string = "未知错误 " + IntegerToString(error_code);
    }

    return error_string;
}

//+------------------------------------------------------------------+
//| 手动开仓函数 - 买入 - 优化版                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    // 使用缓存系统获取价格
    double ask = CacheManager::GetCachedAsk();

    // 如果价格无效，尝试直接获取
    if(ask <= 0)
    {
        MqlTick last_tick;
        if(!SymbolInfoTick(Symbol(), last_tick))
        {
            Print("获取市场价格失败!");
            return;
        }
        ask = last_tick.ask;
    }

    double lotSize = CalculateLotSize();

    // 首单不设置止损，仅用于确定加仓方向
    if(!trade.Buy(lotSize, Symbol(), ask, 0, 0, "Buy-0"))
    {
        Print("买入订单执行失败! 错误: ", GetErrorDescription(GetLastError()));
        return;
    }

    lastBuyPrice = ask;
    currentBuyLayer = 0;  // 初始开仓设置为第0层
    Print("买入首单成功! 手数: ", DoubleToString(lotSize, 2));
}

//+------------------------------------------------------------------+
//| 手动开仓函数 - 卖出 - 优化版                                      |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    // 使用缓存系统获取价格
    double bid = CacheManager::GetCachedBid();

    // 如果价格无效，尝试直接获取
    if(bid <= 0)
    {
        MqlTick last_tick;
        if(!SymbolInfoTick(Symbol(), last_tick))
        {
            Print("获取市场价格失败!");
            return;
        }
        bid = last_tick.bid;
    }

    double lotSize = CalculateLotSize();

    // 首单不设置止损，仅用于确定加仓方向
    if(!trade.Sell(lotSize, Symbol(), bid, 0, 0, "Sell-0"))
    {
        Print("卖出订单执行失败! 错误: ", GetErrorDescription(GetLastError()));
        return;
    }

    lastSellPrice = bid;
    currentSellLayer = 0;  // 初始开仓设置为第0层
    Print("卖出首单成功! 手数: ", DoubleToString(lotSize, 2));
}

//+------------------------------------------------------------------+
//| 获取最后一个同向订单的手数 - 优化版                               |
//+------------------------------------------------------------------+
double GetLastPositionVolume(bool isBuy)
{
    static datetime lastCheckTime = 0;
    static double cachedBuyLotSize = 0;
    static double cachedSellLotSize = 0;
    static int cachedBuyLayer = -1;
    static int cachedSellLayer = -1;

    datetime currentTime = TimeCurrent();

    // 如果缓存有效且距离上次检查不超过5秒，直接使用缓存值
    if(currentTime - lastCheckTime < 5)
    {
        if(isBuy && cachedBuyLotSize > 0)
            return cachedBuyLotSize;
        else if(!isBuy && cachedSellLotSize > 0)
            return cachedSellLotSize;
    }

    // 需要重新查询
    double lastLotSize = 0;
    datetime lastTime = 0;
    string commentToMatch = isBuy ? "Buy-" : "Sell-";
    int highestLayer = 0;

    // 创建临时数组存储符合条件的持仓信息，避免重复查询
    struct PositionInfo
    {
        int layerNum;
        double volume;
        datetime time;
    };

    PositionInfo positions[];
    int posCount = 0;

    // 一次性获取所有持仓信息
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket) && PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            bool isCurrentBuy = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY);
            if(isCurrentBuy == isBuy)
            {
                string comment = PositionGetString(POSITION_COMMENT);

                // 提取层数，例如从"Buy-5"获取5
                if(StringFind(comment, commentToMatch) == 0)
                {
                    string layerStr = StringSubstr(comment, StringLen(commentToMatch));
                    int layerNum = (int)StringToInteger(layerStr);

                    // 扩展数组并存储持仓信息
                    ArrayResize(positions, posCount + 1);
                    positions[posCount].layerNum = layerNum;
                    positions[posCount].volume = PositionGetDouble(POSITION_VOLUME);
                    positions[posCount].time = (datetime)PositionGetInteger(POSITION_TIME);
                    posCount++;

                    // 更新最高层数
                    if(layerNum > highestLayer)
                        highestLayer = layerNum;
                }
            }
        }
    }

    // 如果找到了持仓，查找最高层数的最新订单
    if(posCount > 0)
    {
        for(int i = 0; i < posCount; i++)
        {
            if(positions[i].layerNum == highestLayer)
            {
                if(positions[i].time > lastTime)
                {
                    lastTime = positions[i].time;
                    lastLotSize = positions[i].volume;
                }
            }
        }
    }

    // 如果没有找到上一次开仓，使用基础手数
    if(lastLotSize == 0)
    {
        lastLotSize = CalculateLotSize();
    }

    // 更新缓存
    if(isBuy)
    {
        cachedBuyLotSize = lastLotSize;
        cachedBuyLayer = highestLayer;
    }
    else
    {
        cachedSellLotSize = lastLotSize;
        cachedSellLayer = highestLayer;
    }

    lastCheckTime = currentTime;

    return lastLotSize;
}

//+------------------------------------------------------------------+
//| 计算手数大小                                                      |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    double lotSize = 0;

    if(!UseCompoundMode)
    {
        lotSize = FixedLotSize;
    }
    else
    {
        // 获取当前账户余额
        double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);

        // 根据复利模式计算新仓位
        lotSize = FixedLotSize * (currentBalance / BaseEquityPerLot);

        //Print("复利模式计算 - 当前余额: ", currentBalance,
        //      ", 基准资金: ", BaseEquityPerLot,
        //      ", 基础手数: ", FixedLotSize,
        //      ", 计算手数: ", lotSize);
    }

    // 规范化手数
    double normalizedLot = formatlots(_Symbol, lotSize);

    //Print("手数计算结果 - 原始手数: ", lotSize,
    //      ", 规范化后: ", normalizedLot,
    //      ", 品种: ", _Symbol,
    //      ", 复利模式: ", UseCompoundMode);

    return normalizedLot;
}

//+------------------------------------------------------------------+
//| 管理已有仓位                                                      |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
    CheckProfitTarget();
}

//+------------------------------------------------------------------+
//| 平掉部分仓位                                                     |
//+------------------------------------------------------------------+
void ClosePartialPositions(double percentage)
{
    if(percentage <= 0 || percentage > 1)
    {
        Print("无效的平仓百分比: ", percentage);
        return;
    }

    trade.SetDeviationInPoints(INT_MAX);
    trade.SetAsyncMode(true);
    trade.SetMarginMode();
    trade.LogLevel(LOG_LEVEL_ERRORS);

    // 获取所有持仓
    CArrayLong tickets;
    tickets.Shutdown();

    int total = PositionsTotal();
    for(int i = total - 1; i >= 0; i--)
    {
        if(PositionInfo.SelectByIndex(i) && PositionInfo.Symbol() == Symbol() && PositionInfo.Magic() == MagicNumber)
        {
            tickets.Add(PositionInfo.Ticket());
        }
    }

    // 计算要平仓的总量
    int ticketsCount = tickets.Total();
    if(ticketsCount == 0)
        return;

    // 平掉指定百分比的仓位
    for(int i = 0; i < ticketsCount && !IsStopped(); i++)
    {
        ulong ticket = (ulong)MathRound(tickets.At(i)); // 安全类型转换
        if(PositionInfo.SelectByTicket(ticket))
        {
            double volume = PositionInfo.Volume();
            // 计算要平仓的手数，保留到小数点后第3位
            double rawVolumeToClose = NormalizeDouble(volume * percentage, 3);
            // 使用formatlots进行四舍五入
            double volumeToClose = formatlots(_Symbol, rawVolumeToClose);

            // 记录详细的手数计算日志
            Print("部分平仓计算 - 原始手数: ", DoubleToString(volume, 3),
                  ", 百分比: ", DoubleToString(percentage, 2),
                  ", 计算手数: ", DoubleToString(rawVolumeToClose, 3),
                  ", 规范化手数: ", DoubleToString(volumeToClose, 3));

            if(volumeToClose > 0)
            {
                // 使用正确的参数类型，volumeToClose是double类型
                if(trade.PositionClose(ticket, (ulong)volumeToClose))
                {
                    // 简化成功日志
                    Print("部分平仓: #", ticket, ", 量: ", volumeToClose);
                }
                else
                {
                    // 简化失败日志
                    Print("部分平仓失败: #", ticket, ", 错误: ", GetErrorDescription(GetLastError()));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 重置交易状态                                                     |
//+------------------------------------------------------------------+
void ResetTradingState()
{
    // 重置止盈相关变量
    firstCloseDone = false;
    secondCloseDone = false;

    // 重置层数计数
    currentBuyLayer = 0;
    currentSellLayer = 0;

    // 重置价格记录
    lastBuyPrice = 0;
    lastSellPrice = 0;

    // 重置资金记录
    initialEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    currentEquity = initialEquity;
    highestEquity = initialEquity;

    Print("交易状态已重置");
}



//+------------------------------------------------------------------+
//| 平掉所有仓位(包括首单)                                            |
//+------------------------------------------------------------------+
void CloseAllPositionsIncludingInitial()
{
    trade.SetDeviationInPoints(INT_MAX);
    trade.SetAsyncMode(true);
    trade.SetMarginMode();
    trade.LogLevel(LOG_LEVEL_ERRORS);

    for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
    {
        bool allClosed = true;
        m_arr_tickets.Shutdown();

        int total = PositionsTotal();
        for(int i = total - 1; i >= 0; i--)
        {
            if(PositionInfo.SelectByIndex(i) && PositionInfo.Symbol() == Symbol() && PositionInfo.Magic() == MagicNumber)
            {
                // 将所有订单添加到待平仓列表，包括Buy-0和Sell-0
                m_arr_tickets.Add(PositionInfo.Ticket());
            }
        }

        int ticketCount = m_arr_tickets.Total();
        for(int i = 0; i < ticketCount && !IsStopped(); i++)
        {
            ulong ticket = (ulong)m_arr_tickets.At(i);
            if(PositionInfo.SelectByTicket(ticket))
            {
                int freeze_level = (int)SymbolInfoInteger(PositionInfo.Symbol(), SYMBOL_TRADE_FREEZE_LEVEL);
                double point = SymbolInfoDouble(PositionInfo.Symbol(), SYMBOL_POINT);
                bool TP_check = (MathAbs(PositionInfo.PriceCurrent() - PositionInfo.TakeProfit()) > freeze_level * point);
                bool SL_check = (MathAbs(PositionInfo.PriceCurrent() - PositionInfo.StopLoss()) > freeze_level * point);

                if(TP_check && SL_check)
                {
                    trade.SetExpertMagicNumber(PositionInfo.Magic());
                    trade.SetTypeFillingBySymbol(PositionInfo.Symbol());

                    string comment = PositionInfo.Comment();

                    if(trade.PositionClose(ticket) &&
                       (trade.ResultRetcode() == TRADE_RETCODE_DONE ||
                        trade.ResultRetcode() == TRADE_RETCODE_PLACED))
                    {
                        // 平仓成功，不输出日志
                    }
                    else
                    {
                        // 只在平仓失败时输出简洁日志
                        Print("平仓失败 #", ticket, ": ", GetErrorDescription(trade.ResultRetcode()));
                        allClosed = false;
                    }
                }
                else
                {
                    // 简化冻结日志
                    Print("无法平仓 #", ticket, ": 冻结");
                    allClosed = false;
                }
            }
        }

        if(allClosed)
            break;

        Sleep(SLEEPTIME);
        PlaySound("timeout.wav");
    }

    // 重置所有状态变量
    currentBuyLayer = 0;   // 重置多单层数
    currentSellLayer = 0;  // 重置空单层数
    lastBuyPrice = 0;      // 重置最后买入价格
    lastSellPrice = 0;     // 重置最后卖出价格
    Print("已平掉所有仓位，包括首单");
}