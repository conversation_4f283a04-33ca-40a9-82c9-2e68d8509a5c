//+------------------------------------------------------------------+
//|                                              CompressionDetection.mqh |
//|                                      Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.metaquotes.net |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.metaquotes.net"
#property version   "1.00"

// 压缩状态枚举
enum COMPRESSION_STATE {
    NO_COMPRESSION,         // 无压缩
    LIGHT_COMPRESSION,      // 轻度压缩
    MEDIUM_COMPRESSION,     // 中度压缩
    HEAVY_COMPRESSION,      // 重度压缩
    EXTREME_COMPRESSION     // 极度压缩
};

// 主要指标结构
struct CompressionIndicators {
    double volatility;      // 价格波动率
    double bbWidth;         // 布林带宽度
    double atrValue;        // ATR值
    double volumeCompress;  // 成交量压缩度
    string candlePattern;   // 蜡烛图形态
};

//+------------------------------------------------------------------+
//| 价格波动率压缩计算                                                 |
//+------------------------------------------------------------------+
double CalculateVolatilityCompression(const int period = 20) {
    return CalculateVolatilityCompressionTF(PERIOD_CURRENT, period);
}

//+------------------------------------------------------------------+
//| 价格波动率压缩计算（指定时间框架）                                   |
//+------------------------------------------------------------------+
double CalculateVolatilityCompressionTF(ENUM_TIMEFRAMES timeframe, const int period = 20) {
    // 计算最近N个周期的标准差
    double sum = 0.0;
    double sumSquared = 0.0;
    
    // 获取收盘价数据
    double closes[];
    ArraySetAsSeries(closes, true);
    int longPeriod = period * 5; // 使用5倍周期作为历史参考
    int copied = CopyClose(_Symbol, timeframe, 0, longPeriod + period, closes);
    
    if(copied != longPeriod + period) {
        Print("数据复制失败! 错误代码 = ", GetLastError());
        return 0.0;
    }
    
    // 计算平均值
    for(int i = 0; i < period; i++) {
        sum += closes[i];
    }
    double mean = sum / period;
    
    // 计算标准差
    for(int i = 0; i < period; i++) {
        sumSquared += MathPow(closes[i] - mean, 2);
    }
    double stdDev = MathSqrt(sumSquared / period);
    
    // 计算历史波动率水平（使用更长期的数据作为参考）
    double historicalStdDev[];
    ArraySetAsSeries(historicalStdDev, true);
    int numPeriods = longPeriod - period + 1;
    
    if(numPeriods <= 0) {
        Print("周期计算错误：numPeriods = ", numPeriods);
        return 0.0;
    }
    
    ArrayResize(historicalStdDev, numPeriods);
    
    // 计算历史每个周期的标准差
    for(int i = 0; i < numPeriods && i + period <= longPeriod + period; i++) {
        sum = 0.0;
        sumSquared = 0.0;
        
        for(int j = 0; j < period; j++) {
            if(i + j >= ArraySize(closes)) {
                Print("数组访问越界：i = ", i, ", j = ", j, ", ArraySize = ", ArraySize(closes));
                return 0.0;
            }
            sum += closes[i + j];
        }
        mean = sum / period;
        
        for(int j = 0; j < period; j++) {
            sumSquared += MathPow(closes[i + j] - mean, 2);
        }
        historicalStdDev[i] = MathSqrt(sumSquared / period);
    }
    
    // 对历史标准差进行排序以计算百分位
    ArraySort(historicalStdDev);
    
    // 找到当前标准差在历史数据中的位置
    int position = 0;
    while(position < numPeriods && historicalStdDev[position] < stdDev) {
        position++;
    }
    
    // 计算压缩程度（0-100），数值越小表示压缩程度越高
    double compressionLevel = numPeriods > 0 ? 100.0 * position / numPeriods : 50.0;
    
    // 返回压缩程度，这里我们反转数值使得数值越大表示压缩程度越高
    return 100.0 - compressionLevel;
}

//+------------------------------------------------------------------+
//| 布林带宽度变化计算                                                 |
//+------------------------------------------------------------------+
double CalculateBollingerBandWidth(const int period = 20, const double deviation = 2.0) {
    return CalculateBollingerBandWidthTF(PERIOD_CURRENT, period, deviation);
}

//+------------------------------------------------------------------+
//| 布林带宽度变化计算（指定时间框架）                                   |
//+------------------------------------------------------------------+
double CalculateBollingerBandWidthTF(ENUM_TIMEFRAMES timeframe, const int period = 20, const double deviation = 2.0) {
    // 计算布林带指标
    double middleBand[];
    double upperBand[];
    double lowerBand[];
    
    ArraySetAsSeries(middleBand, true);
    ArraySetAsSeries(upperBand, true);
    ArraySetAsSeries(lowerBand, true);
    
    ArrayResize(middleBand, period);
    ArrayResize(upperBand, period);
    ArrayResize(lowerBand, period);
    
    // 获取布林带值
    int handle = iBands(_Symbol, timeframe, period, 0, deviation, PRICE_CLOSE);
    
    if(handle == INVALID_HANDLE) {
        Print("布林带指标创建失败! 错误代码 = ", GetLastError());
        return 0.0;
    }
    
    // 复制指标数据
    if(CopyBuffer(handle, 0, 0, period, middleBand) <= 0 ||
       CopyBuffer(handle, 1, 0, period, upperBand) <= 0 ||
       CopyBuffer(handle, 2, 0, period, lowerBand) <= 0) {
        Print("布林带数据复制失败! 错误代码 = ", GetLastError());
        IndicatorRelease(handle);
        return 0.0;
    }
    
    IndicatorRelease(handle);
    
    // 计算当前布林带宽度
    double currentWidth = (upperBand[0] - lowerBand[0]) / middleBand[0];
    
    // 计算历史布林带宽度
    double historicalWidths[];
    ArrayResize(historicalWidths, period);
    
    for(int i = 0; i < period; i++) {
        historicalWidths[i] = (upperBand[i] - lowerBand[i]) / middleBand[i];
    }
    
    // 计算历史宽度的平均值和标准差
    double sumWidth = 0.0;
    for(int i = 0; i < period; i++) {
        sumWidth += historicalWidths[i];
    }
    double avgWidth = sumWidth / period;
    
    double sumSquared = 0.0;
    for(int i = 0; i < period; i++) {
        sumSquared += MathPow(historicalWidths[i] - avgWidth, 2);
    }
    double stdDevWidth = MathSqrt(sumSquared / period);
    
    // 计算当前宽度的Z分数
    double zScore = (currentWidth - avgWidth) / stdDevWidth;
    
    // 将Z分数转换为0-100的范围，其中较低的值表示更高的压缩程度
    // 使用正态分布近似替代MathErf
    double normalizedWidth = 100 * (0.5 + 0.5 * NormalCDF(zScore));
    
    // 返回压缩程度（反转值使得数值越大表示压缩程度越高）
    return 100.0 - normalizedWidth;
}

//+------------------------------------------------------------------+
//| 正态分布累积分布函数近似                                           |
//+------------------------------------------------------------------+
double NormalCDF(double x) {
    double t = 1.0 / (1.0 + 0.2316419 * MathAbs(x));
    double d = 0.3989423 * MathExp(-x * x / 2.0);
    double prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    
    if(x > 0) prob = 1.0 - prob;
    return prob;
}

//+------------------------------------------------------------------+
//| ATR动态监测                                                       |
//+------------------------------------------------------------------+
double CalculateATRDynamics(const int period = 14) {
    return CalculateATRDynamicsTF(PERIOD_CURRENT, period);
}

//+------------------------------------------------------------------+
//| ATR动态监测（指定时间框架）                                         |
//+------------------------------------------------------------------+
double CalculateATRDynamicsTF(ENUM_TIMEFRAMES timeframe, const int period = 14) {
    // 计算ATR指标
    double atrValues[];
    ArraySetAsSeries(atrValues, true);
    ArrayResize(atrValues, period * 2); // 获取更多数据用于比较
    
    int handle = iATR(_Symbol, timeframe, period);
    
    if(handle == INVALID_HANDLE) {
        Print("ATR指标创建失败! 错误代码 = ", GetLastError());
        return 0.0;
    }
    
    // 复制指标数据
    if(CopyBuffer(handle, 0, 0, period * 2, atrValues) <= 0) {
        Print("ATR数据复制失败! 错误代码 = ", GetLastError());
        IndicatorRelease(handle);
        return 0.0;
    }
    
    IndicatorRelease(handle);
    
    // 计算当前ATR值
    double currentATR = atrValues[0];
    
    // 计算前一周期的平均ATR
    double previousATRSum = 0.0;
    for(int i = period; i < period * 2; i++) {
        previousATRSum += atrValues[i];
    }
    double previousATRAvg = previousATRSum / period;
    
    // 计算当前周期的平均ATR
    double currentATRSum = 0.0;
    for(int i = 0; i < period; i++) {
        currentATRSum += atrValues[i];
    }
    double currentATRAvg = currentATRSum / period;
    
    // 计算ATR变化率
    double atrChangeRate = (previousATRAvg - currentATRAvg) / previousATRAvg * 100.0;
    
    // 将ATR变化率转换为0-100的压缩指标
    // 正值表示ATR下降（压缩增加），负值表示ATR上升（压缩减少）
    double compressionScore = 50.0 + atrChangeRate;
    
    // 限制在0-100范围内
    compressionScore = MathMax(0.0, MathMin(100.0, compressionScore));
    
    return compressionScore;
}

//+------------------------------------------------------------------+
//| 成交量压缩分析                                                     |
//+------------------------------------------------------------------+
double CalculateVolumeCompression(const int period = 20) {
    return CalculateVolumeCompressionTF(PERIOD_CURRENT, period);
}

//+------------------------------------------------------------------+
//| 成交量压缩分析（指定时间框架）                                       |
//+------------------------------------------------------------------+
double CalculateVolumeCompressionTF(ENUM_TIMEFRAMES timeframe, const int period = 20) {
    // 获取成交量数据
    long volumes[];
    ArraySetAsSeries(volumes, true);
    ArrayResize(volumes, period * 2); // 获取更多数据用于比较
    
    int copied = CopyTickVolume(_Symbol, timeframe, 0, period * 2, volumes);
    
    if(copied != period * 2) {
        Print("成交量数据复制失败! 错误代码 = ", GetLastError());
        return 0.0;
    }
    
    // 计算当前周期的成交量标准差
    double currentSum = 0.0;
    for(int i = 0; i < period; i++) {
        currentSum += (double)volumes[i];
    }
    double currentMean = currentSum / period;
    
    double currentSumSquared = 0.0;
    for(int i = 0; i < period; i++) {
        currentSumSquared += MathPow((double)volumes[i] - currentMean, 2);
    }
    double currentStdDev = MathSqrt(currentSumSquared / period);
    
    // 计算前一周期的成交量标准差
    double previousSum = 0.0;
    for(int i = period; i < period * 2; i++) {
        previousSum += (double)volumes[i];
    }
    double previousMean = previousSum / period;
    
    double previousSumSquared = 0.0;
    for(int i = period; i < period * 2; i++) {
        previousSumSquared += MathPow((double)volumes[i] - previousMean, 2);
    }
    double previousStdDev = MathSqrt(previousSumSquared / period);
    
    // 计算成交量变化率
    double volumeChangeRate = (previousStdDev - currentStdDev) / previousStdDev * 100.0;
    
    // 计算成交量趋势（下降趋势表示可能的压缩）
    double volumeTrend = 0.0;
    if(currentMean < previousMean) {
        volumeTrend = (previousMean - currentMean) / previousMean * 50.0;
    }
    
    // 综合成交量变化和趋势计算压缩分数
    double compressionScore = 50.0 + volumeChangeRate * 0.5 + volumeTrend;
    
    // 限制在0-100范围内
    compressionScore = MathMax(0.0, MathMin(100.0, compressionScore));
    
    return compressionScore;
}

//+------------------------------------------------------------------+
//| 蜡烛图形态识别                                                     |
//+------------------------------------------------------------------+
string IdentifyCandlePattern() {
    return IdentifyCandlePatternTF(PERIOD_CURRENT);
}

//+------------------------------------------------------------------+
//| 蜡烛图形态识别（指定时间框架）                                       |
//+------------------------------------------------------------------+
string IdentifyCandlePatternTF(ENUM_TIMEFRAMES timeframe) {
    // 获取最近的K线数据
    double open[], high[], low[], close[];
    ArraySetAsSeries(open, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    int copied = CopyOpen(_Symbol, timeframe, 0, 5, open);
    copied = MathMin(copied, CopyHigh(_Symbol, timeframe, 0, 5, high));
    copied = MathMin(copied, CopyLow(_Symbol, timeframe, 0, 5, low));
    copied = MathMin(copied, CopyClose(_Symbol, timeframe, 0, 5, close));
    
    if(copied < 5) {
        Print("K线数据复制失败! 错误代码 = ", GetLastError());
        return "";
    }
    
    // 计算当前K线的实体和影线
    double bodySize = MathAbs(close[0] - open[0]);
    double upperShadow = high[0] - MathMax(open[0], close[0]);
    double lowerShadow = MathMin(open[0], close[0]) - low[0];
    double totalRange = high[0] - low[0];
    
    // 计算前一根K线的实体和影线
    double prevBodySize = MathAbs(close[1] - open[1]);
    double prevUpperShadow = high[1] - MathMax(open[1], close[1]);
    double prevLowerShadow = MathMin(open[1], close[1]) - low[1];
    double prevTotalRange = high[1] - low[1];
    
    // 计算平均实体大小
    double avgBodySize = 0.0;
    for(int i = 0; i < 5; i++) {
        avgBodySize += MathAbs(close[i] - open[i]);
    }
    avgBodySize /= 5;
    
    // 识别十字星形态（小实体，长上下影线）
    if(bodySize < avgBodySize * 0.3 && 
       (upperShadow > bodySize * 2 || lowerShadow > bodySize * 2)) {
        return "十字星";
    }
    
    // 识别锤子线（小实体，长下影线，几乎没有上影线）
    if(bodySize < avgBodySize * 0.5 && 
       lowerShadow > bodySize * 2 && 
       upperShadow < bodySize * 0.3) {
        return "锤子线";
    }
    
    // 识别吊颈线（小实体，长上影线，几乎没有下影线）
    if(bodySize < avgBodySize * 0.5 && 
       upperShadow > bodySize * 2 && 
       lowerShadow < bodySize * 0.3) {
        return "吊颈线";
    }
    
    // 识别星线形态（前一根大实体，当前小实体且位置有跳空）
    if(bodySize < avgBodySize * 0.3 && 
       prevBodySize > avgBodySize * 1.5 &&
       ((open[1] < close[1] && MathMin(open[0], close[0]) > open[1]) || // 看涨星
        (open[1] > close[1] && MathMax(open[0], close[0]) < open[1]))) { // 看跌星
        return "星线形态";
    }
    
    // 识别窗口缺口
    if((MathMin(open[0], close[0]) > MathMax(open[1], close[1])) || // 向上缺口
       (MathMax(open[0], close[0]) < MathMin(open[1], close[1]))) { // 向下缺口
        return "窗口缺口";
    }
    
    // 识别多空平衡（实体小，上下影线长度相近）
    if(bodySize < avgBodySize * 0.3 && 
       MathAbs(upperShadow - lowerShadow) < totalRange * 0.2) {
        return "多空平衡";
    }
    
    // 没有识别出特定形态
    return "";
}

//+------------------------------------------------------------------+
//| 压缩状态评估                                                       |
//+------------------------------------------------------------------+
COMPRESSION_STATE EvaluateCompression(const CompressionIndicators &indicators) {
    // 计算综合压缩分数
    double totalScore = 0.0;
    
    // 各指标权重分配
    totalScore += indicators.volatility * 0.25;     // 波动率压缩权重
    totalScore += indicators.bbWidth * 0.25;        // 布林带宽度权重
    totalScore += indicators.atrValue * 0.20;       // ATR变化权重
    totalScore += indicators.volumeCompress * 0.20; // 成交量压缩权重
    
    // 蜡烛图形态额外加分（最多10分）
    if(indicators.candlePattern != "") {
        totalScore += 10.0;
    }
    
    // 返回压缩状态
    if(totalScore >= 90.0) return EXTREME_COMPRESSION;
    if(totalScore >= 75.0) return HEAVY_COMPRESSION;
    if(totalScore >= 60.0) return MEDIUM_COMPRESSION;
    if(totalScore >= 40.0) return LIGHT_COMPRESSION;
    return NO_COMPRESSION;
}

//+------------------------------------------------------------------+
//| 获取压缩状态描述                                                   |
//+------------------------------------------------------------------+
string GetCompressionStateDescription(COMPRESSION_STATE state) {
    switch(state) {
        case EXTREME_COMPRESSION: return "极度压缩";
        case HEAVY_COMPRESSION:   return "重度压缩";
        case MEDIUM_COMPRESSION:  return "中度压缩";
        case LIGHT_COMPRESSION:   return "轻度压缩";
        default:                  return "无压缩";
    }
}

//+------------------------------------------------------------------+
//| 计算ADX指标值                                                    |
//+------------------------------------------------------------------+
double CalculateADX(int period) {
    int handle = iADX(_Symbol, PERIOD_H4, period);
    if(handle == INVALID_HANDLE) {
        Print("ADX指标初始化失败");
        return -1;
    }

    double adx[];
    ArraySetAsSeries(adx, true);
    if(CopyBuffer(handle, 0, 0, 1, adx) < 1) {
        IndicatorRelease(handle);
        Print("ADX指标数据获取失败");
        return -1;
    }

    IndicatorRelease(handle);
    return adx[0];
}

//+------------------------------------------------------------------+
//| 检查均线缠绕状态                                                  |
//+------------------------------------------------------------------+
bool CheckMAEntanglement(int maPeriod1 = 5, int maPeriod2 = 10, int maPeriod3 = 20,
                        int maPeriod4 = 50, int maPeriod5 = 100, double maEntangleThreshold = 0.004) {
    // 定义均线周期数组
    int maPeriods[5] = {maPeriod1, maPeriod2, maPeriod3, maPeriod4, maPeriod5};
    
    double maValues[5];
    
    // 计算各均线值
    for(int i=0; i<5; i++) {
        int handle = iMA(_Symbol, PERIOD_H4, maPeriods[i], 0, MODE_SMA, PRICE_CLOSE);
        if(handle == INVALID_HANDLE) {
            Print("MA指标初始化失败: 周期=", maPeriods[i]);
            continue;
        }
        
        double ma[];
        ArraySetAsSeries(ma, true);
        if(CopyBuffer(handle, 0, 0, 1, ma) < 1) {
            IndicatorRelease(handle);
            Print("MA指标数据获取失败: 周期=", maPeriods[i]);
            continue;
        }
        
        maValues[i] = ma[0];
        IndicatorRelease(handle);
    }
    
    int maxIndex = ArrayMaximum(maValues);
    int minIndex = ArrayMinimum(maValues);
    double maxMA = maValues[maxIndex];
    double minMA = maValues[minIndex];
    double rangePct = (maxMA - minMA) / iClose(_Symbol, PERIOD_H4, 0);
    
    return rangePct < maEntangleThreshold;
}