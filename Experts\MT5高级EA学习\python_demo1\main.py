import datetime

from tools.trade import *
import time

# 按间距中的绿色按钮以运行脚本。
if __name__ == '__main__':
    # path = "F:\\Software\MetaTrader 5\\terminal64.exe"
    path = "D:\\MetaTrader 5\\terminal64.exe"
    if not login(path=path,
                 server="Exness-MT5Trial5",
                 username=76314481,
                 password="6ixuhbWp",
                 timeout=2000):
        quit()

    symbol = "EURUSDz"
    magic = 8199231
    symbol_info: mt5.SymbolInfo = mt5.symbol_info(symbol)
    m_lots = val_volume(symbol, 0.01)
    print("下单手数: ", m_lots)

    m_price = symbol_info.ask
    print("当前价格: ", m_price)

    m_sl = val_stoploss(symbol, 0, symbol_info.ask, 100)
    print("止损价格: ", m_sl)

    m_tp = val_takeprofit(symbol, 0, symbol_info.ask, 100)
    print("止盈价格: ", m_tp)

    # 发送交易请求
    # sand(symbol, 0, 0.0211111, 100, 200, 0, "buy", magic)
    # sand(symbol, 1, 0.01, 100, 0, 0, "sell", magic)

    # buy(symbol, 0.01, 100, 200, 0, "buy + 1", magic)
    # sell(symbol, 0.01, 100, 200, 0, "sell + 1", magic)

    # 修改订单操作
    # count = 0
    # while count < 100:  # True
    #     # 自由修改订单
    #     # update_position(symbol, 0, -1, -1, magic)
    #     # 盈利方向修改止损
    #     # 如果 sl 或者 tp = -1 表示不修改原来的值
    #     # 如果 sl 或者 tp = 0 表示清除原来的值
    #     # 如果  sl 或者 tp 和原来值不一样 就发生修改
    #     update_position_up(symbol, 0, 80, -1, magic)
    #     update_position_up(symbol, 1, 80, -1, magic)
    #     time.sleep(1)
    #     print("count: ", count)
    #     count += 1

    # 平仓
    # close_position(symbol, 0, magic)
    # close_position(symbol, 1, magic)
    # close_position(symbol, -1, magic)

    stop()
