#property copyright "Copyright 2022-2023, Author:阿龙."
#property link      "https://www.guapit.com"
#property description "MT5智能交易编程课程"
#property description "QQ: 8199231"
#property description "交流QQ群: 492653640 660302810"
#property version   "1.00"


// #property indicator_chart_window // 在主窗口位置 
#property indicator_separate_window // 在独立窗口位置
#property indicator_buffers 4 // 预备多少个数组容器 2 + 2
#property indicator_plots 4 // 2条柱状线, 2条趋势线

#include <MovingAverages.mqh>

// 参数配置
input int init_Test = 12; // 周期
input ENUM_APPLIED_PRICE init_applied = PRICE_CLOSE; // 参考目标
// 需要保存统计结果的数组容器
double Test[];
double TestMA[];
int TestMAHandle;

int OnInit()
  {
   SetIndexBuffer(0,Test,INDICATOR_DATA);
   SetIndexBuffer(1,TestMA,INDICATOR_DATA);
   // 设置Test类型
   PlotStyle(0,"Test",DRAW_LINE,STYLE_SOLID,C'223,24,24',2);
   // 设置TestMA类型
   PlotStyle(1,"TestMA",DRAW_LINE,STYLE_SOLID,C'13,230,21',2);
   // 获取EMA数据
   TestMAHandle = iMA(NULL,0,init_Test,0,MODE_EMA,init_applied);
   // 
   string title=StringFormat("Test(%d)",init_Test);
   IndicatorSetString(INDICATOR_SHORTNAME,title);
   
   return(INIT_SUCCEEDED);
  }
  
void OnDeinit(const int reason)
  {
    Comment("");
   
  }
//+------------------------------------------------------------------+
//| 指标编写接口                                                        |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
    // 1.判断K线是否满足最大的计算数量
    if(rates_total < init_Test)
      return 0;
    // 2.如果客户端卡住了,就停止计算,等待恢复在继续计算
    if(IsStopped()) 
        return 0;
    
    
    // 4.开始计算指标
    
    // 4.1如果数据不足,暂时不进行计算
    int calculated = BarsCalculated(TestMAHandle);
    
    if(calculated<rates_total)
        return 0;
    // 4.2处理各种数据无法获取问题   
    if(IsStopped()) // checking for stop flag
      return(0); 
    int to_copy=0;
    if(prev_calculated <= 0)
        to_copy = rates_total;
    else if(prev_calculated < rates_total)
        to_copy = rates_total - prev_calculated;
    else
        to_copy++;
    // printf("to_copy: %d",to_copy);    
    // 4.3 获取 OneBuffer 和 TwoBuffer数据
    if(CopyBuffer(TestMAHandle,0,0,to_copy,TestMA) <=0)
        return 0;

    // 扩容数组
    // 3.保持更新
    int start = 0;
    if(prev_calculated < rates_total)
        start = prev_calculated;
    else 
        start = prev_calculated - 1;
    // printf("start: %d",start);    
    for(int i=start; i<rates_total  && !IsStopped(); i++)
    {
        Test[i] = close[i];
    }

    // SimpleMAOnBuffer(rates_total,prev_calculated,0,init_Mid,DiffBuffer,DeaBuffer);

    return(rates_total);
  }

void PlotStyle(const int pos,const string label,const ENUM_DRAW_TYPE draw_type, const ENUM_LINE_STYLE line_style,
               const color line_color,const int width)
{
    PlotIndexSetString(pos,PLOT_LABEL,label);
    PlotIndexSetInteger(pos,PLOT_DRAW_TYPE,draw_type);
    PlotIndexSetInteger(pos,PLOT_LINE_STYLE,line_style);
    PlotIndexSetInteger(pos,PLOT_LINE_WIDTH,width);
    PlotIndexSetInteger(pos,PLOT_LINE_COLOR,line_color);
}
//+------------------------------------------------------------------+
//| 获取指定周期最大值                                               |
//+------------------------------------------------------------------+
double Highest(const double &array[],int period,int cur_position)
  {
   double res=array[cur_position];
   for(int i=cur_position-1; i>cur_position-period && i>=0; i--)
      if(res<array[i])
         res=array[i];
   return(res);
  }
//+------------------------------------------------------------------+
//| 获取指定周期最大值                                               |
//+------------------------------------------------------------------+
double Lowest(const double &array[],int period,int cur_position)
  {
   double res=array[cur_position];
   for(int i=cur_position-1; i>cur_position-period && i>=0; i--)
      if(res>array[i])
         res=array[i];
   return(res);
  }