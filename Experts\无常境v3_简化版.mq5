//+------------------------------------------------------------------+
//|                                                  无常境v3_简化版.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "3.0"
#include <Trade\Trade.mqh>

//--- 输入参数
input double   InitialLots = 0.01;       // 开仓手数
input int      StopLoss = 100;           // 止损点数
input int      TakeProfit = 200;         // 止盈点数
input int      BreakevenPoints = 50;     // 保本止损点数
input int      AdditionalPoints = 10;    // 附加盈利点数
input int      TimeInterval = 60;        // 开仓时间间隔(秒)
input string   StartTime = "00:00";      // 交易开始时间
input string   EndTime = "23:59";        // 交易结束时间
input long     MagicNumber = 123456;     // 魔术号
input string   OrderComment = "";        // 订单注释

//--- 全局变量
CTrade trade;                            // 交易对象
datetime lastOrderTime = 0;              // 上次开单时间
ulong processedTickets[];                // 已处理订单数组

//--- 时间缓存结构
struct TimeCache {
    int startMinutes;                    // 开始时间(分钟)
    int endMinutes;                      // 结束时间(分钟)
};
TimeCache timeCache;

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化随机数生成器
    MathSrand(GetTickCount());
    
    // 解析交易时间
    StringToTimeCache(StartTime, EndTime, timeCache);
    
    // 配置交易对象
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(20);
    trade.SetTypeFillingBySymbol(_Symbol);
    trade.SetAsyncMode(false);
    
    // 设置定时器，每秒触发一次
    EventSetTimer(1);
    
    Print("EA初始化成功 | 品种=", _Symbol, " 点值=", _Point, 
          " 时段=", StartTime, "-", EndTime);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA反初始化函数                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();
    
    Print("EA停止 | 原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| 价格跳动处理函数                                                 |
//+------------------------------------------------------------------+
void OnTick()
{
    // 获取当前时间
    datetime now = TimeCurrent();
    
    // 检查是否在交易时段内
    bool isInTradingHours = IsTradeTime();
    
    // 在交易时段内才执行开单逻辑
    if(isInTradingHours)
    {
        // 检查开单时间间隔
        if(now - lastOrderTime >= TimeInterval)
        {
            ExecuteTrade();
        }
        
        // 管理现有持仓
        ManagePositions();
    }
    else
    {
        // 非交易时段仍然管理现有持仓
        ManagePositions();
    }
}

//+------------------------------------------------------------------+
//| 定时器函数                                                       |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 定期清理已处理但不再存在的订单
    CleanupProcessedOrders();
}

//+------------------------------------------------------------------+
//| 交易执行函数                                                     |
//+------------------------------------------------------------------+
void ExecuteTrade()
{
    // 1. 获取市场数据
    MqlTick tick;
    if(!SymbolInfoTick(_Symbol, tick)) {
        Print("获取市场报价失败, 错误代码=", GetLastError());
        return;
    }
    
    // 2. 生成交易方向
    static ulong randSeed = GetTickCount();
    randSeed ^= randSeed << 13;
    randSeed ^= randSeed >> 17;
    randSeed ^= randSeed << 5;
    ENUM_ORDER_TYPE type = (randSeed%2 == 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    
    // 3. 计算交易参数
    double lots = NormalizeVolume(InitialLots);
    double price = (type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
    double sl = CalculateSL(price, type);
    double tp = CalculateTP(price, type);
    
    // 4. 发送交易指令
    bool result = false;
    string orderTypeStr = (type == ORDER_TYPE_BUY) ? "多单" : "空单";
    
    if(type == ORDER_TYPE_BUY) {
        result = trade.Buy(lots, _Symbol, price, sl, tp, OrderComment);
    } else {
        result = trade.Sell(lots, _Symbol, price, sl, tp, OrderComment);
    }
    
    uint resultCode = trade.ResultRetcode();
    if(result && resultCode == TRADE_RETCODE_DONE) {
        lastOrderTime = TimeCurrent();
        Print("开仓成功: 类型=", orderTypeStr, ", 票号=", trade.ResultOrder(), 
              ", 手数=", lots, ", 价格=", price, ", 止损=", sl, ", 止盈=", tp);
    } else {
        Print("开仓失败: 类型=", orderTypeStr, ", 错误代码=", resultCode, 
              ", 描述=", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| 持仓管理函数                                                     |
//+------------------------------------------------------------------+
void ManagePositions()
{
    int total = PositionsTotal();
    if(total == 0) return;
    
    // 获取最新市场报价
    MqlTick currentTick;
    if(!SymbolInfoTick(_Symbol, currentTick)) {
        Print("获取报价失败！错误代码：", GetLastError());
        return;
    }
    
    // 遍历所有持仓
    for(int i = 0; i < total; i++) {
        ulong ticket = PositionGetTicket(i);
        if(ticket == 0) continue;
        
        // 检查是否为当前品种和魔术号
        string symbol = PositionGetString(POSITION_SYMBOL);
        long magic = PositionGetInteger(POSITION_MAGIC);
        if(symbol != _Symbol || magic != MagicNumber) continue;
        
        // 跳过已处理的订单
        if(IsOrderProcessed(ticket)) continue;
        
        // 获取持仓信息
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentPrice = (posType == POSITION_TYPE_BUY) ? currentTick.bid : currentTick.ask;
        double profit = MathAbs(currentPrice - openPrice);
        double currentSL = PositionGetDouble(POSITION_SL);
        double currentTP = PositionGetDouble(POSITION_TP);
        
        // 检查是否达到保本条件
        if(BreakevenPoints > 0 && profit >= BreakevenPoints * _Point) {
            // 计算新的止损价格
            double newSL = (posType == POSITION_TYPE_BUY) ?
                          openPrice + AdditionalPoints * _Point :
                          openPrice - AdditionalPoints * _Point;
            
            // 确保新止损不会使订单立即平仓
            int stops_level = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
            double min_distance = stops_level * _Point;
            
            if((posType == POSITION_TYPE_BUY && newSL >= currentPrice - min_distance) ||
               (posType == POSITION_TYPE_SELL && newSL <= currentPrice + min_distance)) {
                newSL = (posType == POSITION_TYPE_BUY) ?
                      currentPrice - min_distance :
                      currentPrice + min_distance;
            }
            
            // 避免无效更新
            bool needUpdate = (posType == POSITION_TYPE_BUY) ?
                            (newSL > currentSL + _Point/2 || currentSL == 0) :
                            (newSL < currentSL - _Point/2 || currentSL == 0);
            
            // 执行订单修改
            if(needUpdate) {
                trade.SetExpertMagicNumber(MagicNumber);
                if(trade.PositionModify(ticket, NormalizeDouble(newSL, _Digits), NormalizeDouble(currentTP, _Digits))) {
                    // 添加到已处理订单数组
                    MarkOrderAsProcessed(ticket);
                    
                    Print("保本止损设置成功: 票号=", ticket,
                          ", 类型=", (posType == POSITION_TYPE_BUY ? "多单" : "空单"),
                          ", 开仓价=", openPrice,
                          ", 新止损=", newSL);
                } else {
                    Print("保本止损设置失败: 票号=", ticket,
                          ", 错误=", trade.ResultRetcode(),
                          ": ", trade.ResultRetcodeDescription());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 清理已处理订单                                                   |
//+------------------------------------------------------------------+
void CleanupProcessedOrders()
{
    int size = ArraySize(processedTickets);
    if(size == 0) return;
    
    // 创建临时数组存储有效的票号
    ulong validTickets[];
    int validCount = 0;
    
    // 检查每个订单是否仍然存在
    for(int i = 0; i < size; i++) {
        if(PositionSelectByTicket(processedTickets[i])) {
            ArrayResize(validTickets, validCount + 1);
            validTickets[validCount++] = processedTickets[i];
        }
    }
    
    // 更新已处理订单数组
    ArrayFree(processedTickets);
    ArrayCopy(processedTickets, validTickets);
    
    if(size != validCount) {
        Print("已清理过期订单，当前处理订单数量：", validCount);
    }
}

//+------------------------------------------------------------------+
//| 检查订单是否已处理                                               |
//+------------------------------------------------------------------+
bool IsOrderProcessed(ulong ticket)
{
    for(int i = 0; i < ArraySize(processedTickets); i++) {
        if(processedTickets[i] == ticket) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 标记订单为已处理                                                 |
//+------------------------------------------------------------------+
void MarkOrderAsProcessed(ulong ticket)
{
    int size = ArraySize(processedTickets);
    ArrayResize(processedTickets, size + 1);
    processedTickets[size] = ticket;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CalculateSL(double price, ENUM_ORDER_TYPE orderType)
{
    if(StopLoss <= 0) return 0; // 如果止损点数为0，则禁用止损
    
    double sl = 0;
    if(orderType == ORDER_TYPE_BUY) {
        sl = price - StopLoss * _Point;
    } else {
        sl = price + StopLoss * _Point;
    }
    
    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                     |
//+------------------------------------------------------------------+
double CalculateTP(double price, ENUM_ORDER_TYPE orderType)
{
    if(TakeProfit <= 0) return 0; // 如果止盈点数为0，则禁用止盈
    
    double tp = 0;
    if(orderType == ORDER_TYPE_BUY) {
        tp = price + TakeProfit * _Point;
    } else {
        tp = price - TakeProfit * _Point;
    }
    
    return NormalizeDouble(tp, _Digits);
}

//+------------------------------------------------------------------+
//| 标准化交易量                                                     |
//+------------------------------------------------------------------+
double NormalizeVolume(double volume)
{
    double minVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double stepVolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    // 确保在最小和最大范围内
    volume = MathMax(minVolume, MathMin(maxVolume, volume));
    
    // 确保是步进的整数倍
    int steps = (int)MathRound(volume / stepVolume);
    volume = steps * stepVolume;
    
    // 格式化到正确的小数位
    int digits = 0;
    if(stepVolume == 0.01) digits = 2;
    else if(stepVolume == 0.1) digits = 1;
    
    return NormalizeDouble(volume, digits);
}

//+------------------------------------------------------------------+
//| 时间参数转换函数                                                 |
//+------------------------------------------------------------------+
void StringToTimeCache(const string start, const string end, TimeCache &cache)
{
    string t[2];
    // 处理开始时间
    if(StringSplit(start, ':', t) == 2) {
        int hours = (int)StringToInteger(t[0]);
        int minutes = (int)StringToInteger(t[1]);
        
        // 确保时间在有效范围内
        hours = MathMin(MathMax(hours, 0), 23);
        minutes = MathMin(MathMax(minutes, 0), 59);
        
        cache.startMinutes = hours * 60 + minutes;
    }
    
    // 处理结束时间
    if(StringSplit(end, ':', t) == 2) {
        int hours = (int)StringToInteger(t[0]);
        int minutes = (int)StringToInteger(t[1]);
        
        // 特殊处理"24:00"，将其转换为"23:59"
        if(hours == 24) {
            hours = 23;
            minutes = 59;
        } else {
            // 确保时间在有效范围内
            hours = MathMin(MathMax(hours, 0), 23);
            minutes = MathMin(MathMax(minutes, 0), 59);
        }
        
        cache.endMinutes = hours * 60 + minutes;
    }
}

//+------------------------------------------------------------------+
//| 交易时间验证函数                                                 |
//+------------------------------------------------------------------+
bool IsTradeTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    // 计算当前分钟数
    int current = dt.hour * 60 + dt.min;
    
    // 处理跨午夜的时间段
    if(timeCache.startMinutes < timeCache.endMinutes) {
        // 正常时间段（例如 09:00-17:00）
        return (current >= timeCache.startMinutes && current <= timeCache.endMinutes);
    } else {
        // 跨午夜时间段（例如 22:00-06:00）
        return (current >= timeCache.startMinutes || current <= timeCache.endMinutes);
    }
}
