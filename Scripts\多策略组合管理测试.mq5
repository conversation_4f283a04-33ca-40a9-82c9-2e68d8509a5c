//+------------------------------------------------------------------+
//|                                          多策略组合管理测试.mq5 |
//|                                      Copyright 2024, Deepseek AI |
//|                                             https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Deepseek AI"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property description "多策略组合管理系统功能测试脚本"
#property script_show_inputs

// 测试参数
input bool EnableDetailedLog = true; // 启用详细日志
input double TestPortfolioEquity = 10000.0; // 测试组合净值

//+------------------------------------------------------------------+
//| 多策略组合管理系统测试脚本                                        |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("🚀 开始多策略组合管理系统测试");
    Print("详细日志: ", EnableDetailedLog ? "启用" : "禁用");
    Print("测试组合净值: ", DoubleToString(TestPortfolioEquity, 2));
    Print("========================================");
    
    // 测试1: 组合指标计算
    TestPortfolioMetricsCalculation();
    
    // 测试2: 权重分配方法
    TestWeightAllocationMethods();
    
    // 测试3: 风险预算管理
    TestRiskBudgetManagement();
    
    // 测试4: 重平衡机制
    TestRebalancingMechanisms();
    
    // 测试5: 相关性分析
    TestCorrelationAnalysis();
    
    // 测试6: 完整组合管理流程
    TestCompletePortfolioWorkflow();
    
    Print("========================================");
    Print("✅ 多策略组合管理系统测试完成");
}

//+------------------------------------------------------------------+
//| 测试组合指标计算                                                  |
//+------------------------------------------------------------------+
void TestPortfolioMetricsCalculation()
{
    Print("📋 测试1: 组合指标计算");
    
    // 模拟策略数据
    struct TestStrategy {
        double weight;
        double return_rate;
        double volatility;
        double correlation[3];
        string name;
    };
    
    TestStrategy strategies[] = {
        {0.4, 0.15, 0.12, {1.0, 0.3, 0.7}, "趋势跟踪"},
        {0.35, 0.12, 0.10, {0.3, 1.0, -0.2}, "均值回归"},
        {0.25, 0.18, 0.15, {0.7, -0.2, 1.0}, "突破策略"}
    };
    
    // 计算组合收益率
    double portfolioReturn = 0.0;
    for(int i = 0; i < 3; i++) {
        portfolioReturn += strategies[i].weight * strategies[i].return_rate;
    }
    
    // 计算组合波动率
    double portfolioVariance = 0.0;
    for(int i = 0; i < 3; i++) {
        double weight_i = strategies[i].weight;
        double vol_i = strategies[i].volatility;
        
        // 自身方差贡献
        portfolioVariance += weight_i * weight_i * vol_i * vol_i;
        
        // 协方差贡献
        for(int j = i + 1; j < 3; j++) {
            double weight_j = strategies[j].weight;
            double vol_j = strategies[j].volatility;
            double correlation = strategies[i].correlation[j];
            
            portfolioVariance += 2 * weight_i * weight_j * vol_i * vol_j * correlation;
        }
    }
    double portfolioVolatility = MathSqrt(portfolioVariance);
    
    // 计算夏普比率
    double riskFreeRate = 0.02;
    double sharpeRatio = (portfolioReturn - riskFreeRate) / portfolioVolatility;
    
    Print("组合指标计算结果:");
    Print("  组合收益率: ", DoubleToString(portfolioReturn * 100, 2), "%");
    Print("  组合波动率: ", DoubleToString(portfolioVolatility * 100, 2), "%");
    Print("  夏普比率: ", DoubleToString(sharpeRatio, 2));
    
    // 验证计算
    double expectedReturn = 0.4 * 0.15 + 0.35 * 0.12 + 0.25 * 0.18; // 14.4%
    bool returnCorrect = (MathAbs(portfolioReturn - expectedReturn) < 0.001);
    Print("  收益率验证: ", returnCorrect ? "✓" : "✗");
    
    Print("✅ 组合指标计算测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试权重分配方法                                                  |
//+------------------------------------------------------------------+
void TestWeightAllocationMethods()
{
    Print("📋 测试2: 权重分配方法");
    
    // 模拟策略数据
    double volatilities[] = {0.12, 0.10, 0.15};
    double sharpeRatios[] = {1.2, 1.0, 0.9};
    string strategyNames[] = {"趋势跟踪", "均值回归", "突破策略"};
    
    // 测试等权重分配
    Print("等权重分配:");
    double equalWeights[3];
    for(int i = 0; i < 3; i++) {
        equalWeights[i] = 1.0 / 3.0;
        Print("  ", strategyNames[i], ": ", DoubleToString(equalWeights[i] * 100, 1), "%");
    }
    
    // 测试风险平价分配
    Print("风险平价分配:");
    double totalInverseVol = 0.0;
    for(int i = 0; i < 3; i++) {
        totalInverseVol += 1.0 / volatilities[i];
    }
    
    double riskParityWeights[3];
    for(int i = 0; i < 3; i++) {
        riskParityWeights[i] = (1.0 / volatilities[i]) / totalInverseVol;
        Print("  ", strategyNames[i], ": ", DoubleToString(riskParityWeights[i] * 100, 1), "%");
    }
    
    // 测试基于性能的分配
    Print("基于性能分配:");
    double totalSharpe = 0.0;
    for(int i = 0; i < 3; i++) {
        if(sharpeRatios[i] > 0) totalSharpe += sharpeRatios[i];
    }
    
    double performanceWeights[3];
    for(int i = 0; i < 3; i++) {
        performanceWeights[i] = (sharpeRatios[i] > 0) ? sharpeRatios[i] / totalSharpe : 0.0;
        Print("  ", strategyNames[i], ": ", DoubleToString(performanceWeights[i] * 100, 1), "%");
    }
    
    // 验证权重总和
    double equalSum = equalWeights[0] + equalWeights[1] + equalWeights[2];
    double riskParitySum = riskParityWeights[0] + riskParityWeights[1] + riskParityWeights[2];
    double performanceSum = performanceWeights[0] + performanceWeights[1] + performanceWeights[2];
    
    Print("权重总和验证:");
    Print("  等权重总和: ", DoubleToString(equalSum, 3), " ", (MathAbs(equalSum - 1.0) < 0.001) ? "✓" : "✗");
    Print("  风险平价总和: ", DoubleToString(riskParitySum, 3), " ", (MathAbs(riskParitySum - 1.0) < 0.001) ? "✓" : "✗");
    Print("  性能分配总和: ", DoubleToString(performanceSum, 3), " ", (MathAbs(performanceSum - 1.0) < 0.001) ? "✓" : "✗");
    
    Print("✅ 权重分配方法测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试风险预算管理                                                  |
//+------------------------------------------------------------------+
void TestRiskBudgetManagement()
{
    Print("📋 测试3: 风险预算管理");
    
    // 模拟风险预算场景
    struct RiskScenario {
        double totalRiskBudget;
        double strategyWeights[3];
        double strategyVolatilities[3];
        double expectedUtilization;
        string description;
    };
    
    RiskScenario scenarios[] = {
        {0.15, {0.4, 0.35, 0.25}, {0.12, 0.10, 0.15}, 0.75, "正常风险配置"},
        {0.20, {0.5, 0.3, 0.2}, {0.18, 0.12, 0.20}, 1.05, "高风险配置"},
        {0.10, {0.6, 0.3, 0.1}, {0.08, 0.06, 0.10}, 0.54, "低风险配置"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        RiskScenario rs = scenarios[i];
        
        // 计算风险利用率
        double riskUtilization = 0.0;
        for(int j = 0; j < 3; j++) {
            double strategyRisk = rs.strategyWeights[j] * rs.strategyVolatilities[j];
            riskUtilization += strategyRisk;
        }
        riskUtilization /= rs.totalRiskBudget;
        
        Print("风险预算场景: ", rs.description);
        Print("  总风险预算: ", DoubleToString(rs.totalRiskBudget * 100, 1), "%");
        Print("  计算风险利用率: ", DoubleToString(riskUtilization * 100, 1), "%");
        Print("  预期风险利用率: ", DoubleToString(rs.expectedUtilization * 100, 1), "%");
        
        bool utilizationCorrect = (MathAbs(riskUtilization - rs.expectedUtilization) < 0.1);
        Print("  利用率验证: ", utilizationCorrect ? "✓" : "✗");
        
        // 检查风险限制
        bool riskExceeded = (riskUtilization > 1.0);
        Print("  风险状态: ", riskExceeded ? "超出限制" : "正常范围");
    }
    
    Print("✅ 风险预算管理测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试重平衡机制                                                    |
//+------------------------------------------------------------------+
void TestRebalancingMechanisms()
{
    Print("📋 测试4: 重平衡机制");
    
    // 测试重平衡触发条件
    struct RebalanceScenario {
        double currentWeights[3];
        double targetWeights[3];
        double performanceThreshold;
        bool expectedTrigger;
        string description;
    };
    
    RebalanceScenario scenarios[] = {
        {{0.40, 0.35, 0.25}, {0.42, 0.33, 0.25}, 0.05, false, "小幅偏差-不触发"},
        {{0.45, 0.30, 0.25}, {0.40, 0.35, 0.25}, 0.05, true, "中等偏差-触发"},
        {{0.50, 0.25, 0.25}, {0.40, 0.35, 0.25}, 0.05, true, "大幅偏差-触发"}
    };
    
    for(int i = 0; i < ArraySize(scenarios); i++) {
        RebalanceScenario rs = scenarios[i];
        
        // 检查是否需要重平衡
        bool shouldRebalance = false;
        for(int j = 0; j < 3; j++) {
            double deviation = MathAbs(rs.currentWeights[j] - rs.targetWeights[j]);
            if(deviation > rs.performanceThreshold) {
                shouldRebalance = true;
                break;
            }
        }
        
        Print("重平衡场景: ", rs.description);
        Print("  当前权重: ", DoubleToString(rs.currentWeights[0]*100, 1), "%, ", 
              DoubleToString(rs.currentWeights[1]*100, 1), "%, ", 
              DoubleToString(rs.currentWeights[2]*100, 1), "%");
        Print("  目标权重: ", DoubleToString(rs.targetWeights[0]*100, 1), "%, ", 
              DoubleToString(rs.targetWeights[1]*100, 1), "%, ", 
              DoubleToString(rs.targetWeights[2]*100, 1), "%");
        Print("  阈值: ", DoubleToString(rs.performanceThreshold*100, 1), "%");
        Print("  计算触发: ", shouldRebalance ? "是" : "否");
        Print("  预期触发: ", rs.expectedTrigger ? "是" : "否");
        
        bool triggerCorrect = (shouldRebalance == rs.expectedTrigger);
        Print("  触发验证: ", triggerCorrect ? "✓" : "✗");
    }
    
    Print("✅ 重平衡机制测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试相关性分析                                                    |
//+------------------------------------------------------------------+
void TestCorrelationAnalysis()
{
    Print("📋 测试5: 相关性分析");
    
    // 模拟相关性矩阵
    double correlationMatrix[3][3] = {
        {1.0, 0.3, 0.7},    // 趋势跟踪
        {0.3, 1.0, -0.2},   // 均值回归
        {0.7, -0.2, 1.0}    // 突破策略
    };
    
    string strategyNames[] = {"趋势跟踪", "均值回归", "突破策略"};
    
    Print("相关性矩阵:");
    for(int i = 0; i < 3; i++) {
        string row = "  " + strategyNames[i] + ": ";
        for(int j = 0; j < 3; j++) {
            row += DoubleToString(correlationMatrix[i][j], 2) + " ";
        }
        Print(row);
    }
    
    // 分析相关性特征
    Print("相关性分析:");
    
    // 找到最高相关性
    double maxCorrelation = 0.0;
    int maxI = -1, maxJ = -1;
    for(int i = 0; i < 3; i++) {
        for(int j = i + 1; j < 3; j++) {
            if(MathAbs(correlationMatrix[i][j]) > MathAbs(maxCorrelation)) {
                maxCorrelation = correlationMatrix[i][j];
                maxI = i;
                maxJ = j;
            }
        }
    }
    
    if(maxI >= 0 && maxJ >= 0) {
        Print("  最高相关性: ", strategyNames[maxI], " vs ", strategyNames[maxJ], 
              " (", DoubleToString(maxCorrelation, 2), ")");
    }
    
    // 计算平均相关性
    double totalCorrelation = 0.0;
    int correlationCount = 0;
    for(int i = 0; i < 3; i++) {
        for(int j = i + 1; j < 3; j++) {
            totalCorrelation += MathAbs(correlationMatrix[i][j]);
            correlationCount++;
        }
    }
    double avgCorrelation = totalCorrelation / correlationCount;
    Print("  平均相关性: ", DoubleToString(avgCorrelation, 2));
    
    // 分散化效果评估
    string diversificationEffect = "";
    if(avgCorrelation < 0.3) diversificationEffect = "优秀分散化";
    else if(avgCorrelation < 0.5) diversificationEffect = "良好分散化";
    else if(avgCorrelation < 0.7) diversificationEffect = "中等分散化";
    else diversificationEffect = "分散化不足";
    
    Print("  分散化效果: ", diversificationEffect);
    
    Print("✅ 相关性分析测试完成\n");
}

//+------------------------------------------------------------------+
//| 测试完整组合管理流程                                              |
//+------------------------------------------------------------------+
void TestCompletePortfolioWorkflow()
{
    Print("📋 测试6: 完整组合管理流程");
    
    // 模拟完整的组合管理流程
    Print("模拟组合管理流程:");
    
    // 步骤1: 初始化组合
    Print("步骤1: 初始化组合");
    double initialEquity = TestPortfolioEquity;
    int activeStrategies = 3;
    string allocationMethod = "风险平价";
    Print("  初始净值: ", DoubleToString(initialEquity, 2));
    Print("  活跃策略: ", activeStrategies, "个");
    Print("  分配方法: ", allocationMethod);
    
    // 步骤2: 计算初始权重
    Print("步骤2: 计算初始权重");
    double volatilities[] = {0.12, 0.10, 0.15};
    double totalInverseVol = 0.0;
    for(int i = 0; i < 3; i++) {
        totalInverseVol += 1.0 / volatilities[i];
    }
    
    double weights[3];
    for(int i = 0; i < 3; i++) {
        weights[i] = (1.0 / volatilities[i]) / totalInverseVol;
        Print("  策略", i+1, "权重: ", DoubleToString(weights[i] * 100, 1), "%");
    }
    
    // 步骤3: 模拟市场变化
    Print("步骤3: 模拟市场变化");
    double returns[] = {0.02, -0.01, 0.03}; // 模拟收益率
    double portfolioReturn = 0.0;
    for(int i = 0; i < 3; i++) {
        portfolioReturn += weights[i] * returns[i];
        Print("  策略", i+1, "收益: ", DoubleToString(returns[i] * 100, 2), "%");
    }
    Print("  组合收益: ", DoubleToString(portfolioReturn * 100, 2), "%");
    
    // 步骤4: 检查重平衡需求
    Print("步骤4: 检查重平衡需求");
    double newEquity = initialEquity * (1 + portfolioReturn);
    bool needRebalance = (MathAbs(portfolioReturn) > 0.05); // 5%阈值
    Print("  新净值: ", DoubleToString(newEquity, 2));
    Print("  重平衡需求: ", needRebalance ? "是" : "否");
    
    // 步骤5: 风险检查
    Print("步骤5: 风险检查");
    double riskBudget = 0.15;
    double currentRisk = 0.0;
    for(int i = 0; i < 3; i++) {
        currentRisk += weights[i] * volatilities[i];
    }
    double riskUtilization = currentRisk / riskBudget;
    bool riskExceeded = (riskUtilization > 1.0);
    
    Print("  风险预算: ", DoubleToString(riskBudget * 100, 1), "%");
    Print("  当前风险: ", DoubleToString(currentRisk * 100, 1), "%");
    Print("  风险利用率: ", DoubleToString(riskUtilization * 100, 1), "%");
    Print("  风险状态: ", riskExceeded ? "超出" : "正常");
    
    // 步骤6: 生成组合报告
    Print("步骤6: 生成组合报告");
    double portfolioVol = 0.11; // 假设组合波动率
    double sharpeRatio = (portfolioReturn - 0.02/365) / portfolioVol; // 假设日无风险利率
    
    Print("  组合状态: 活跃");
    Print("  组合收益: ", DoubleToString(portfolioReturn * 100, 2), "%");
    Print("  组合波动率: ", DoubleToString(portfolioVol * 100, 1), "%");
    Print("  夏普比率: ", DoubleToString(sharpeRatio, 2));
    
    // 步骤7: 流程验证
    Print("步骤7: 流程验证");
    bool workflowValid = (newEquity > 0 && activeStrategies > 0 && riskUtilization >= 0);
    Print("  流程有效性: ", workflowValid ? "✓" : "✗");
    Print("  验证条件: 净值>0 策略数>0 风险利用率≥0");
    
    Print("✅ 完整组合管理流程完成\n");
}
