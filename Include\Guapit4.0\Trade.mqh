#include "TradeRequest.mqh"
#include "validate.mqh"
#include <Guapit4.0/Position/Positions.mqh>
class Trade
{
   public:
      Trade(void);
      Trade(const string symbol, const ulong magic);
      ~Trade(void);
      // 获取ask
      double Ask(void) const { return m_req.getAsk(); }
      // 获取 bid
      double Bid(void) const { return m_req.getBid(); }
      // 获取 point
      double Point(void) const { return m_req.getPoint(); }
      // 获取digits
      double Digits(void) const { return m_req.getDigits(); }
      // 获取点差
      int Spread(void) const { return (int)SymbolInfoInteger(m_req.getSymbol(), SYMBOL_SPREAD); }

      // 设置交易品种
      void setSymbol(const string symbol){ m_req.setSymbol(symbol); m_poss.setSymbol(symbol); }
      // 设置识别码
      void setMagic(const ulong magic){ m_req.setMagic(magic); m_poss.setMagic(magic);}
      // 是否开启异步
      void setAsync(const bool async) { m_req.setAsync(async); }
      // 是否打印交易订单信息, 0: 有错误时打印, 1 成功或者失败打印, -1: 成功或者失败都不打印
      void setPrint(const int print_level) { m_req.setPrint(print_level); }
      // 是否开启错误日志
      void setLogs(const bool logs_level) { m_req.setLogs(logs_level); }
      // 是否开启数据校验
      void setCheck(const bool is_check) { m_req.setCheck(is_check); }
      // 语言版本
      void setLanguage(const string language) { m_req.setLanguage(language); }
      // 设置是否开启开仓间隔时间, 每单间隔时间(秒数)
      void setStepOpen(const bool is_step) { m_step_open = is_step; }
      void setStepTime(const int step_sec) { m_step_time = step_sec; }

      // 自由开仓函数
      bool Buy(const double volume, const int sl, const int tp, const string comment);
      // 自由开仓函数
      bool Sell(const double volume, const int sl, const int tp, const string comment);
      // 自由开仓函数
      bool Send(const int type,const double volume, const int sl, const int tp, const string comment);
      // 根据指定的价格修改指定订单
      void Modify(const int type, const double price, const int sl, const int tp);
      // 自动跟踪止损
      void AutoModify(const int type, const double price, const int sl);
      // 自动阶梯止损
      void AutoModifyLadder(const int type, const double price, const int sl);
      // 修改止盈
      void ModifyProfit(const int type, const double price, const int tp);
      // 清除止损或者止盈
      void ModifyZero(const int type, const bool sl_zero=true, const bool tp_zero=true);
      bool ModifyZeroByPos(Position &pos, const bool sl_zero=true, const bool tp_zero=true);
      // 修改指定持仓单数据
      bool ModifyByID(const ulong ticket, const double stoploss, const double takeprofit);
      // 修改指定
      bool ModifyByPos(Position &pos, const double stoploss, const double takeprofit);
      // 平仓指定范围订单
      void Close(const int type);
      void CloseBuy(void);
      void CloseSell(void);
      // 平仓指定单号持仓单
      bool CloseByID(const ulong ticket);
      bool CloseByPos(Position &pos);

   protected:
      Positions m_poss;
   private:
      TradeRequest m_req;
      bool m_step_open;
      int m_step_time;
};
Trade::Trade(void)
{
    m_req.setSymbol(_Symbol);
    m_req.setMagic(8199231);
    m_poss.setSymbol(_Symbol);
    m_poss.setMagic(8199231);
    m_step_open = false;
}
Trade::Trade(const string symbol, const ulong magic)
{
    string n_symbol = valiSymbol(symbol);
    if(n_symbol == NULL)
    {
       printf("Trade::Trade初始化失败, 交易品种不存在, 当前交易品种: %s",symbol);
    }
    m_req.setSymbol(symbol);
    m_req.setMagic(magic);
    m_poss.setSymbol(symbol);
    m_poss.setMagic(magic);
    m_step_open = false;
}
Trade::~Trade(void)
{
   // delete m_req;
   // delete m_poss;
}

bool Trade::Send(const int type,const double volume, const int sl, const int tp, const string comment)
{
   // 如果开启开仓间隔功能
   if(m_step_open == true)
   {
      Position dst[];
      int size = m_poss.ToArray(type, dst);
      if(size > 0)
      {
         int diff = int(TimeCurrent() -  dst[size - 1].time);
         if(diff < m_step_time){
            return false;
         }
      }
   }

   if(m_poss.Check(type, comment)){
      return false;
   }
   m_req.setType(type);
   m_req.setVolume(volume);
   m_req.setSL(valiSL(sl,m_req.getSymbol(),type,m_req.getAsk()));
   m_req.setTP(valiTP(tp,m_req.getSymbol(),type,m_req.getBid()));
   m_req.setComment(comment);
   return m_req.baseSend();
}

bool Trade::Buy(const double volume, const int sl, const int tp, const string comment)
{
   return Send(0,volume,sl,tp,comment);
}

bool Trade::Sell(const double volume, const int sl, const int tp, const string comment)
{
   return Send(1,volume,sl,tp,comment);
}

void Trade::Modify(const int type, const double price, const int sl, const int tp)
{
   // 获取指定返回内的持仓单数据 
   Position dst[];
   int size = m_poss.ToArray(type, dst);
   for (int i = 0; i < size; i++)
   {
      double m_sl = valiSL(sl,dst[i].symbol,type,price);
      double m_tp = valiTP(tp,dst[i].symbol,type,price);
      ModifyByPos(dst[i],m_sl,m_tp);
   }
   
}

void Trade::AutoModify(const int type, const double price, const int sl)
{
   // 获取指定返回内的持仓单数据 
   Position dst[];
   int size = m_poss.ToArray(type, dst);
   for (int i = 0; i < size; i++)
   {
      double m_sl = valiSL(sl,dst[i].symbol,type,price);
      // 如果是盈利方向
      if(dst[i].type == type && type == 0){
         if(dst[i].sl <= m_sl){
            ModifyByPos(dst[i],m_sl,dst[i].tp);
         }
      }
      else if(dst[i].type == type && type == 1){
         if(dst[i].sl >= m_sl){
            ModifyByPos(dst[i],m_sl,dst[i].tp);
         }
      }
      else if(type == -1)
      {
         if(dst[i].type == 0){
         if(dst[i].sl <= m_sl){
               ModifyByPos(dst[i],m_sl,dst[i].tp);
            }
         }
         else if(dst[i].type == 1){
            if(dst[i].sl >= m_sl){
               ModifyByPos(dst[i],m_sl,dst[i].tp);
            }
         }
      }
   }
   
}

void Trade::AutoModifyLadder(const int type, const double price, const int sl)
{
   // 获取指定返回内的持仓单数据 
   Position dst[];
   int size = m_poss.ToArray(type, dst);
   for (int i = 0; i < size; i++)
   {
      double m_sl = valiSL(sl,dst[i].symbol,type,price);
      // 如果是盈利方向
      if(dst[i].type == type && type == 0){
         // 1.50 - 1.00  >=  50 * 0.01
         if( m_sl - dst[i].sl >= sl * dst[i].point){
            ModifyByPos(dst[i],m_sl,dst[i].tp);
         }
      }
      else if(dst[i].type == type && type == 1){
         if(dst[i].sl - m_sl >= sl * dst[i].point){
            ModifyByPos(dst[i],m_sl,dst[i].tp);
         }
      }
      else if(type == -1)
      {
         // 如果是盈利方向
         if(dst[i].type == 0){
            // 1.50 - 1.00  >=  50 * 0.01
            if( m_sl - dst[i].sl >= sl * dst[i].point){
               ModifyByPos(dst[i],m_sl,dst[i].tp);
            }
         }
         else if(dst[i].type == 1){
            if(dst[i].sl - m_sl >= sl * dst[i].point){
               ModifyByPos(dst[i],m_sl,dst[i].tp);
            }
         }
      }
   }
}

void Trade::ModifyProfit(const int type, const double price, const int tp)
{
   Modify(type,price,0,tp);
}

bool Trade::ModifyByID(const ulong ticket, const double stoploss, const double takeprofit)
{
   if(stoploss < 0 || takeprofit < 0)
   {
      printf("修改失败, sl或者tp的值不能小于0,当前sl : %g, 当前tp: %g", stoploss,takeprofit);
      return false;
   }
   
   m_req.setID(ticket);
   m_req.setSL(stoploss);
   m_req.setTP(takeprofit);
   return m_req.baseUpdate();
}

bool Trade::ModifyByPos(Position &pos, const double stoploss, const double takeprofit)
{
   if(pos.id <= 0)
      return false;
   
   // 止损要改 和 止盈要改
   if(stoploss > 0 && takeprofit > 0)
   {  
      
      if(stoploss != pos.sl || takeprofit != pos.tp)
      {
         return ModifyByID(pos.id,stoploss,takeprofit);
      }
   }
   // 止损要改 和 止盈不改
   else if(stoploss > 0 && takeprofit == 0)
   {
      if(stoploss != pos.sl)
      {
         return ModifyByID(pos.id,stoploss,pos.tp);
      }
   }
   // 止损不改 和 止盈要改
   else if(stoploss == 0 && takeprofit > 0)
   {
      if(takeprofit != pos.tp)
      {
         return ModifyByID(pos.id,pos.sl,takeprofit);
      }
   }
   // 止损不改 和 止盈不改 或者非法数据都都不用修改
   return false;
}

bool Trade::ModifyZeroByPos(Position &pos, const bool sl_zero=true, const bool tp_zero=true)
{
   if(pos.id <= 0)
      return false;
   
   if(sl_zero == true && tp_zero == true)
   {
      return ModifyByID(pos.id, 0, 0);
   }
   else if(sl_zero == true && tp_zero == false)
   {
      return ModifyByID(pos.id, 0, pos.tp);
   }
   else if(sl_zero == false && tp_zero == true)
   {
      return ModifyByID(pos.id, pos.sl, 0);
   }
   else return false;
      
}
void Trade::ModifyZero(const int type, const bool sl_zero=true, const bool tp_zero=true)
{
   // 获取指定返回内的持仓单数据 
   Position dst[];
   int size = m_poss.ToArray(type, dst);
   for (int i = 0; i < size; i++)
   {
      ModifyZeroByPos(dst[i],sl_zero,tp_zero);
   }
}

bool Trade::CloseByID(const ulong ticket)
{
   Position pos;
   if(!getPositionByTicket(ticket,pos))
   {
      printf("平仓订单失败, 当前单号: %d 不存在!", ticket);
      return false;
   }
      
   m_req.setID(ticket);
   m_req.setType(pos.type);
   m_req.setVolume(pos.volume);
   return m_req.baseClose();
}

bool Trade::CloseByPos(Position &pos)
{
   if(pos.id == 0)
      return false;
   
   m_req.setID(pos.id);
   m_req.setType(pos.type);
   m_req.setVolume(pos.volume);
   return m_req.baseClose();

}
void Trade::Close(const int type)
{
   // 获取指定返回内的持仓单数据 
   Position dst[];
   int size = m_poss.ToArray(type, dst);
   for (int i = 0; i < size; i++)
   {
      CloseByPos(dst[i]);
   }
}

void Trade::CloseBuy(void)
{
   Close(0);
}

void Trade::CloseSell(void)
{
   Close(1);
}