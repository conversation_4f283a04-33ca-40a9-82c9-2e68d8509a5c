//+------------------------------------------------------------------+
//|                                OptimizedTrendlineBreakout.mq5   |
//|                                  深度优化的趋势线突破策略        |
//|                                                   Augment Agent |
//+------------------------------------------------------------------+
#property copyright "Augment Agent"
#property link      ""
#property version   "2.0"
#property strict

// 输入参数
input group "=== 基础参数 ==="
input int    InpStrength = 3;                    // 趋势线检测强度
input double InpRiskPercent = 2.0;               // 风险百分比
input int    InpMagicNumber = 20241218;          // 魔术数字

input group "=== 信号过滤参数 ==="
input double InpMinMomentum = 0.001;             // 最小动量要求
input double InpVolumeMultiplier = 1.2;          // 成交量倍数
input int    InpMinBarsDistance = 5;             // 最小K线距离
input int    InpMaxBarsDistance = 50;            // 最大K线距离

input group "=== 风险管理参数 ==="
input double InpStopLossATR = 2.0;               // 止损ATR倍数
input double InpTrailingATR = 1.5;               // 追踪止损ATR倍数
input double InpBreakEvenATR = 1.0;              // 盈亏平衡ATR倍数
input double InpMaxPositionPercent = 10.0;       // 最大仓位百分比

input group "=== 多时间框架参数 ==="
input bool   InpUseMultiTimeframe = true;        // 启用多时间框架
input ENUM_TIMEFRAMES InpHigherTimeframe = PERIOD_H4; // 高级时间框架

// 全局变量
struct TrendPoint {
    double price;
    int bar;
    datetime time;
};

struct TrendLine {
    TrendPoint point1;
    TrendPoint point2;
    double slope;
    bool isValid;
    int violations;
};

// 数组存储高低点
TrendPoint highPoints[20];
TrendPoint lowPoints[20];
int highPointCount = 0;
int lowPointCount = 0;

// 市场环境枚举
enum MarketEnvironment {
    MARKET_STRONG_UPTREND,
    MARKET_WEAK_UPTREND,
    MARKET_SIDEWAYS,
    MARKET_WEAK_DOWNTREND,
    MARKET_STRONG_DOWNTREND,
    MARKET_HIGH_VOLATILITY
};

// 风险管理类
class RiskManager {
private:
    double m_initialStopLoss;
    double m_trailingStop;
    bool m_breakEvenActivated;
    
public:
    RiskManager() : m_initialStopLoss(0), m_trailingStop(0), m_breakEvenActivated(false) {}
    
    // 计算初始止损
    double CalculateInitialStopLoss(double entryPrice, double trendLine, int direction) {
        double atr = iATR(Symbol(), Period(), 14, 0);
        double atrStop = atr * InpStopLossATR;
        double trendStop = MathAbs(entryPrice - trendLine) * 1.5;
        
        double stopDistance = MathMax(atrStop, trendStop);
        stopDistance = MathMax(stopDistance, SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * Point() * 2);
        
        return (direction > 0) ? entryPrice - stopDistance : entryPrice + stopDistance;
    }
    
    // 更新追踪止损
    double UpdateTrailingStop(double currentPrice, double currentStop, int direction) {
        double atr = iATR(Symbol(), Period(), 14, 0);
        double trailDistance = atr * InpTrailingATR;
        
        if(direction > 0) {
            double newStop = currentPrice - trailDistance;
            return MathMax(newStop, currentStop);
        } else {
            double newStop = currentPrice + trailDistance;
            return MathMin(newStop, currentStop);
        }
    }
    
    // 检查盈亏平衡
    bool CheckBreakEven(double entryPrice, double currentPrice, int direction) {
        double profit = (direction > 0) ? currentPrice - entryPrice : entryPrice - currentPrice;
        double atr = iATR(Symbol(), Period(), 14, 0);
        
        return profit >= atr * InpBreakEvenATR;
    }
};

// 市场环境检测器
class MarketEnvironmentDetector {
public:
    MarketEnvironment IdentifyEnvironment() {
        double atr = iATR(Symbol(), Period(), 14, 0);
        double avgATR = 0;
        for(int i = 1; i <= 50; i++) {
            avgATR += iATR(Symbol(), Period(), 14, i);
        }
        avgATR /= 50;
        
        double volatility = atr / avgATR;
        double adx = iADX(Symbol(), Period(), 14, PRICE_CLOSE, MODE_MAIN, 0);
        double rsi = iRSI(Symbol(), Period(), 14, PRICE_CLOSE, 0);
        
        // 高波动环境
        if(volatility > 1.5) return MARKET_HIGH_VOLATILITY;
        
        // 趋势环境判断
        if(adx > 25) {
            if(rsi > 60) return MARKET_STRONG_UPTREND;
            else if(rsi < 40) return MARKET_STRONG_DOWNTREND;
            else if(rsi > 55) return MARKET_WEAK_UPTREND;
            else if(rsi < 45) return MARKET_WEAK_DOWNTREND;
        }
        
        return MARKET_SIDEWAYS;
    }
    
    // 获取环境相关的交易参数
    bool ShouldTrade(MarketEnvironment env) {
        switch(env) {
            case MARKET_SIDEWAYS:
            case MARKET_HIGH_VOLATILITY:
                return false;
            default:
                return true;
        }
    }
    
    double GetPositionMultiplier(MarketEnvironment env) {
        switch(env) {
            case MARKET_STRONG_UPTREND:
            case MARKET_STRONG_DOWNTREND:
                return 1.5;
            case MARKET_WEAK_UPTREND:
            case MARKET_WEAK_DOWNTREND:
                return 1.0;
            case MARKET_SIDEWAYS:
                return 0.5;
            case MARKET_HIGH_VOLATILITY:
                return 0.3;
            default:
                return 1.0;
        }
    }
};

// 全局对象
RiskManager g_riskManager;
MarketEnvironmentDetector g_marketDetector;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("优化趋势线突破策略启动 - 版本 2.0");
    
    // 初始化数组
    ArrayInitialize(highPoints, 0);
    ArrayInitialize(lowPoints, 0);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("策略停止，原因: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // 检查新K线
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(Symbol(), Period(), 0);
    
    if(currentBarTime == lastBarTime) return;
    lastBarTime = currentBarTime;
    
    // 更新高低点
    UpdateHighLowPoints();
    
    // 检测市场环境
    MarketEnvironment currentEnv = g_marketDetector.IdentifyEnvironment();
    
    // 根据环境决定是否交易
    if(!g_marketDetector.ShouldTrade(currentEnv)) {
        return;
    }
    
    // 检查趋势线突破
    CheckTrendlineBreakouts(currentEnv);
    
    // 管理现有持仓
    ManagePositions();
}

//+------------------------------------------------------------------+
//| 更新高低点数据                                                   |
//+------------------------------------------------------------------+
void UpdateHighLowPoints() {
    // 检测新高点
    if(IsNewHigh(InpStrength)) {
        AddHighPoint(iHigh(Symbol(), Period(), InpStrength), InpStrength);
    }
    
    // 检测新低点
    if(IsNewLow(InpStrength)) {
        AddLowPoint(iLow(Symbol(), Period(), InpStrength), InpStrength);
    }
}

//+------------------------------------------------------------------+
//| 检查是否为新高点                                                 |
//+------------------------------------------------------------------+
bool IsNewHigh(int strength) {
    double currentHigh = iHigh(Symbol(), Period(), strength);
    
    // 检查左右两边是否都低于当前高点
    for(int i = 1; i <= strength; i++) {
        if(iHigh(Symbol(), Period(), strength - i) >= currentHigh ||
           iHigh(Symbol(), Period(), strength + i) >= currentHigh) {
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查是否为新低点                                                 |
//+------------------------------------------------------------------+
bool IsNewLow(int strength) {
    double currentLow = iLow(Symbol(), Period(), strength);
    
    // 检查左右两边是否都高于当前低点
    for(int i = 1; i <= strength; i++) {
        if(iLow(Symbol(), Period(), strength - i) <= currentLow ||
           iLow(Symbol(), Period(), strength + i) <= currentLow) {
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 添加高点                                                         |
//+------------------------------------------------------------------+
void AddHighPoint(double price, int barIndex) {
    // 移动数组元素
    for(int i = 19; i > 0; i--) {
        highPoints[i] = highPoints[i-1];
    }
    
    // 添加新高点
    highPoints[0].price = price;
    highPoints[0].bar = barIndex;
    highPoints[0].time = iTime(Symbol(), Period(), barIndex);
    
    if(highPointCount < 20) highPointCount++;
}

//+------------------------------------------------------------------+
//| 添加低点                                                         |
//+------------------------------------------------------------------+
void AddLowPoint(double price, int barIndex) {
    // 移动数组元素
    for(int i = 19; i > 0; i--) {
        lowPoints[i] = lowPoints[i-1];
    }
    
    // 添加新低点
    lowPoints[0].price = price;
    lowPoints[0].bar = barIndex;
    lowPoints[0].time = iTime(Symbol(), Period(), barIndex);
    
    if(lowPointCount < 20) lowPointCount++;
}

//+------------------------------------------------------------------+
//| 检查趋势线突破                                                   |
//+------------------------------------------------------------------+
void CheckTrendlineBreakouts(MarketEnvironment env) {
    // 检查上升趋势线突破（做空信号）
    TrendLine upTrendLine = BuildUpTrendLine();
    if(upTrendLine.isValid) {
        if(CheckBreakoutSignal(upTrendLine, -1, env)) {
            OpenPosition(-1, upTrendLine, env);
        }
    }

    // 检查下降趋势线突破（做多信号）
    TrendLine downTrendLine = BuildDownTrendLine();
    if(downTrendLine.isValid) {
        if(CheckBreakoutSignal(downTrendLine, 1, env)) {
            OpenPosition(1, downTrendLine, env);
        }
    }
}

//+------------------------------------------------------------------+
//| 构建上升趋势线                                                   |
//+------------------------------------------------------------------+
TrendLine BuildUpTrendLine() {
    TrendLine trendLine;
    trendLine.isValid = false;

    if(lowPointCount < 2) return trendLine;

    // 寻找两个合适的低点
    for(int i = 0; i < lowPointCount - 1; i++) {
        for(int j = i + 1; j < lowPointCount; j++) {
            // 检查时间距离
            int barDistance = lowPoints[i].bar - lowPoints[j].bar;
            if(barDistance < InpMinBarsDistance || barDistance > InpMaxBarsDistance) continue;

            // 检查价格关系（上升趋势线）
            if(lowPoints[i].price <= lowPoints[j].price) continue;

            // 验证趋势线有效性
            if(ValidateTrendLine(lowPoints[j], lowPoints[i], 1)) {
                trendLine.point1 = lowPoints[j];
                trendLine.point2 = lowPoints[i];
                trendLine.slope = (lowPoints[i].price - lowPoints[j].price) / (lowPoints[i].bar - lowPoints[j].bar);
                trendLine.isValid = true;
                return trendLine;
            }
        }
    }

    return trendLine;
}

//+------------------------------------------------------------------+
//| 构建下降趋势线                                                   |
//+------------------------------------------------------------------+
TrendLine BuildDownTrendLine() {
    TrendLine trendLine;
    trendLine.isValid = false;

    if(highPointCount < 2) return trendLine;

    // 寻找两个合适的高点
    for(int i = 0; i < highPointCount - 1; i++) {
        for(int j = i + 1; j < highPointCount; j++) {
            // 检查时间距离
            int barDistance = highPoints[i].bar - highPoints[j].bar;
            if(barDistance < InpMinBarsDistance || barDistance > InpMaxBarsDistance) continue;

            // 检查价格关系（下降趋势线）
            if(highPoints[i].price >= highPoints[j].price) continue;

            // 验证趋势线有效性
            if(ValidateTrendLine(highPoints[j], highPoints[i], -1)) {
                trendLine.point1 = highPoints[j];
                trendLine.point2 = highPoints[i];
                trendLine.slope = (highPoints[i].price - highPoints[j].price) / (highPoints[i].bar - highPoints[j].bar);
                trendLine.isValid = true;
                return trendLine;
            }
        }
    }

    return trendLine;
}

//+------------------------------------------------------------------+
//| 验证趋势线有效性                                                 |
//+------------------------------------------------------------------+
bool ValidateTrendLine(TrendPoint p1, TrendPoint p2, int direction) {
    double slope = (p2.price - p1.price) / (p2.bar - p1.bar);
    int violations = 0;
    double atr = iATR(Symbol(), Period(), 14, 0);
    double tolerance = atr * 0.5;

    // 检查趋势线之间的价格是否违反趋势线
    for(int i = p1.bar - 1; i > p2.bar; i--) {
        double trendValue = p1.price + slope * (p1.bar - i);

        if(direction > 0) { // 上升趋势线
            if(iLow(Symbol(), Period(), i) < trendValue - tolerance) {
                violations++;
            }
        } else { // 下降趋势线
            if(iHigh(Symbol(), Period(), i) > trendValue + tolerance) {
                violations++;
            }
        }
    }

    // 允许少量违反
    int maxViolations = (p1.bar - p2.bar) / 10; // 10%的违反率
    return violations <= maxViolations;
}

//+------------------------------------------------------------------+
//| 检查突破信号                                                     |
//+------------------------------------------------------------------+
bool CheckBreakoutSignal(TrendLine &trendLine, int direction, MarketEnvironment env) {
    // 计算当前趋势线值
    double currentTrendValue = trendLine.point1.price +
        trendLine.slope * (trendLine.point1.bar - 0);

    double currentPrice = (direction > 0) ? iClose(Symbol(), Period(), 0) : iClose(Symbol(), Period(), 0);
    double atr = iATR(Symbol(), Period(), 14, 0);

    // 基础突破检查
    bool priceBreakout = false;
    if(direction > 0) { // 做多信号
        priceBreakout = currentPrice > currentTrendValue + atr * 0.2;
    } else { // 做空信号
        priceBreakout = currentPrice < currentTrendValue - atr * 0.2;
    }

    if(!priceBreakout) return false;

    // 成交量确认
    if(!ConfirmVolume()) return false;

    // 动量确认
    if(!ConfirmMomentum(direction)) return false;

    // 多时间框架确认
    if(InpUseMultiTimeframe && !ConfirmHigherTimeframe(direction)) return false;

    return true;
}

//+------------------------------------------------------------------+
//| 成交量确认                                                       |
//+------------------------------------------------------------------+
bool ConfirmVolume() {
    long currentVolume = iVolume(Symbol(), Period(), 0);
    long avgVolume = 0;

    for(int i = 1; i <= 20; i++) {
        avgVolume += iVolume(Symbol(), Period(), i);
    }
    avgVolume /= 20;

    return currentVolume > avgVolume * InpVolumeMultiplier;
}

//+------------------------------------------------------------------+
//| 动量确认                                                         |
//+------------------------------------------------------------------+
bool ConfirmMomentum(int direction) {
    double momentum = (iClose(Symbol(), Period(), 0) - iClose(Symbol(), Period(), 3)) / iClose(Symbol(), Period(), 3);

    if(direction > 0) {
        return momentum > InpMinMomentum;
    } else {
        return momentum < -InpMinMomentum;
    }
}

//+------------------------------------------------------------------+
//| 高时间框架确认                                                   |
//+------------------------------------------------------------------+
bool ConfirmHigherTimeframe(int direction) {
    double higherSMA20 = iMA(Symbol(), InpHigherTimeframe, 20, 0, MODE_SMA, PRICE_CLOSE, 1);
    double higherSMA50 = iMA(Symbol(), InpHigherTimeframe, 50, 0, MODE_SMA, PRICE_CLOSE, 1);
    double higherPrice = iClose(Symbol(), InpHigherTimeframe, 1);

    if(direction > 0) {
        return higherPrice > higherSMA20 && higherSMA20 > higherSMA50;
    } else {
        return higherPrice < higherSMA20 && higherSMA20 < higherSMA50;
    }
}

//+------------------------------------------------------------------+
//| 开仓函数                                                         |
//+------------------------------------------------------------------+
void OpenPosition(int direction, TrendLine &trendLine, MarketEnvironment env) {
    // 检查是否已有同方向持仓
    if(HasPosition(direction)) return;

    // 计算仓位大小
    double lotSize = CalculatePositionSize(direction, trendLine, env);
    if(lotSize <= 0) return;

    // 计算入场价格
    double entryPrice = (direction > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    // 计算止损价格
    double currentTrendValue = trendLine.point1.price +
        trendLine.slope * (trendLine.point1.bar - 0);
    double stopLoss = g_riskManager.CalculateInitialStopLoss(entryPrice, currentTrendValue, direction);

    // 计算止盈价格
    double takeProfit = CalculateTakeProfit(entryPrice, stopLoss, direction, env);

    // 执行交易
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lotSize;
    request.type = (direction > 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.magic = InpMagicNumber;
    request.comment = StringFormat("TrendBreak_%s", direction > 0 ? "BUY" : "SELL");

    if(OrderSend(request, result)) {
        Print(StringFormat("开仓成功: %s %.2f手 @ %.5f, SL: %.5f, TP: %.5f",
              direction > 0 ? "BUY" : "SELL", lotSize, entryPrice, stopLoss, takeProfit));
    } else {
        Print("开仓失败: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                     |
//+------------------------------------------------------------------+
double CalculatePositionSize(int direction, TrendLine &trendLine, MarketEnvironment env) {
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * InpRiskPercent / 100.0;

    // 计算止损距离
    double entryPrice = (direction > 0) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                                         SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double currentTrendValue = trendLine.point1.price +
        trendLine.slope * (trendLine.point1.bar - 0);
    double stopLoss = g_riskManager.CalculateInitialStopLoss(entryPrice, currentTrendValue, direction);

    double stopDistance = MathAbs(entryPrice - stopLoss);
    if(stopDistance <= 0) return 0;

    // 计算基础仓位
    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double lotSize = riskAmount / (stopDistance / tickSize * tickValue);

    // 根据市场环境调整
    lotSize *= g_marketDetector.GetPositionMultiplier(env);

    // 限制最大仓位
    double maxLot = accountBalance * InpMaxPositionPercent / 100.0 / entryPrice;
    lotSize = MathMin(lotSize, maxLot);

    // 标准化手数
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLotLimit = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    lotSize = MathMax(minLot, MathMin(maxLotLimit,
              MathRound(lotSize / lotStep) * lotStep));

    return lotSize;
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                     |
//+------------------------------------------------------------------+
double CalculateTakeProfit(double entryPrice, double stopLoss, int direction, MarketEnvironment env) {
    double stopDistance = MathAbs(entryPrice - stopLoss);
    double profitRatio = 2.0; // 默认风险回报比 1:2

    // 根据市场环境调整风险回报比
    switch(env) {
        case MARKET_STRONG_UPTREND:
        case MARKET_STRONG_DOWNTREND:
            profitRatio = 3.0; // 强趋势中提高目标
            break;
        case MARKET_WEAK_UPTREND:
        case MARKET_WEAK_DOWNTREND:
            profitRatio = 1.5; // 弱趋势中降低目标
            break;
        default:
            profitRatio = 2.0;
    }

    double profitDistance = stopDistance * profitRatio;

    if(direction > 0) {
        return entryPrice + profitDistance;
    } else {
        return entryPrice - profitDistance;
    }
}

//+------------------------------------------------------------------+
//| 检查是否有持仓                                                   |
//+------------------------------------------------------------------+
bool HasPosition(int direction) {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByIndex(i)) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == InpMagicNumber) {
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                if((direction > 0 && posType == POSITION_TYPE_BUY) ||
                   (direction < 0 && posType == POSITION_TYPE_SELL)) {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 管理现有持仓                                                     |
//+------------------------------------------------------------------+
void ManagePositions() {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByIndex(i)) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == InpMagicNumber) {

                double entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
                double currentSL = PositionGetDouble(POSITION_SL);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                int direction = (posType == POSITION_TYPE_BUY) ? 1 : -1;

                // 更新追踪止损
                double newSL = g_riskManager.UpdateTrailingStop(currentPrice, currentSL, direction);

                // 检查盈亏平衡
                if(g_riskManager.CheckBreakEven(entryPrice, currentPrice, direction)) {
                    newSL = entryPrice + (direction > 0 ? SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * Point() :
                                                         -SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * Point());
                }

                // 修改止损
                if(MathAbs(newSL - currentSL) > Point()) {
                    ModifyPosition(PositionGetInteger(POSITION_TICKET), newSL,
                                  PositionGetDouble(POSITION_TP));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 修改持仓                                                         |
//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double stopLoss, double takeProfit) {
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = stopLoss;
    request.tp = takeProfit;

    if(OrderSend(request, result)) {
        Print(StringFormat("持仓修改成功: Ticket %d, SL: %.5f, TP: %.5f",
              ticket, stopLoss, takeProfit));
        return true;
    } else {
        Print("持仓修改失败: ", result.retcode, " - ", result.comment);
        return false;
    }
}
