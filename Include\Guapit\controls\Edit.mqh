#property copyright "Copyright 2022-2023, Author:阿龙."
#property link      "https://www.guapit.com"
#property description "MT5智能交易编程课程"
#property description "QQ: 8199231"
#property description "交流QQ群: 492653640 660302810"
#property version   "1.00"

#include "GObject.mqh"

class Edit: public GObject
{
  public:
    Edit(void);
    ~Edit(void);
    // 创建编辑框对象
    void Create(string name,int x,int y);
    // 设置尺寸
    void Size(int width,int height);
    // 设置输入默认文本
    void Text(string text);
    string Text(void);
    // 修改字体样式和大小
    void Font(string font, int font_size);
    // 设置文本颜色
    void Color(color clr);
    // 设置背景色和边框颜色
    void BGColor(color bg_clr);
    void BorderColor(color border_color);
    
    // 对齐方式
    void Anchor(ENUM_ANCHOR_POINT anchor);
    
    // 设置成只读模式
    void ReadOnly(bool is_only); 
    // 移动控件
    void Move(int x, int y);
    // 主动更新控件设置
    void Update(void);
    // 删除当前控件
    void Delete(void);
    // 删除控件
    void DeleteAll(string prefix);
    // 是否接受鼠标点击事件
    void IsSelected(const bool selected);
    // 是否可以被拖动
    void IsSelectable(const bool selectable);
    // 在对象列表菜单中隐藏
    void IsHidden(const bool hidden);
    // 是否接受优先点击
    void IsZorder(const bool zorder);
};

Edit::Edit(void)
{
}

Edit::~Edit(void)
{
}

void Edit::Create(string name,int x,int y)
{
   BaseCreateXY(name,OBJ_EDIT);
   SetInt(OBJPROP_XDISTANCE,x);
   SetInt(OBJPROP_YDISTANCE,y);
}

void Edit::Size(int width,int height)
{
  SetInt(OBJPROP_XSIZE,width);
  SetInt(OBJPROP_YSIZE,height);
}

void Edit::Text(string text)
{
  SetString(OBJPROP_TEXT, text);
}

string Edit::Text()
{
  return GetString(OBJPROP_TEXT,0);
}

void Edit::Font(string font, int font_size)
{
  SetString(OBJPROP_FONT,font);
  SetInt(OBJPROP_FONTSIZE,font_size);
}

void Edit::Color(color clr)
{
  SetInt(OBJPROP_COLOR,clr);
}

void Edit::BGColor(color bg_clr)
{
  SetInt(OBJPROP_BGCOLOR,bg_clr);
}

void Edit::BorderColor(color border_color)
{
  SetInt(OBJPROP_BORDER_COLOR,border_color);
}

void Edit::Anchor(ENUM_ANCHOR_POINT anchor)
{
  SetInt(OBJPROP_ANCHOR,anchor);
}

void Edit::ReadOnly(bool is_only)
{
    SetInt(OBJPROP_READONLY,is_only);
}

//--- 公共使用方法
// 是否接受鼠标点击事件
void Edit::IsSelected(const bool selected)
{
  SetInt(OBJPROP_SELECTED,selected);
}

// 是否可以被拖动
void Edit::IsSelectable(const bool selectable)
{
  SetInt(OBJPROP_SELECTABLE,selectable);
}
// 在对象列表菜单中隐藏
void Edit::IsHidden(const bool hidden)
{
  SetInt(OBJPROP_HIDDEN,hidden);
}
// 是否接受优先点击
void Edit::IsZorder(const bool zorder)
{
  SetInt(OBJPROP_ZORDER,zorder);
}


void Edit::Move(int x, int y)
{
   SetInt(OBJPROP_XDISTANCE,x);
   SetInt(OBJPROP_YDISTANCE,y);
}
void Edit::Update(void)
{
  BaseUpdate();
}

void Edit::Delete()
{
   BaseDelete();
}

void Edit::DeleteAll(string prefix)
{
  string prefix_name = StringSubstr(prefix,0,3); 
  ObjectsDeleteAll(ChartId(),SubWindow(),-1);
}


