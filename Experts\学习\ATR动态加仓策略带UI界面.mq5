//+------------------------------------------------------------------+
//|                                      ATR动态加仓策略带UI界面.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

// 包含必要的库文件
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Arrays\ArrayLong.mqh>
#include <Guapit\controls\Button.mqh>

// 全局变量
CTrade         Trade;          // 交易对象
CPositionInfo  PositionInfo;   // 持仓信息对象
CSymbolInfo    SymbolInfo;     // 品种信息对象
CArrayLong     m_arr_tickets;  // 订单号数组

// UI控件
Button btnBuySell, btnCloseAll;

// 订单管理数组
struct OrderData
{
   ulong    ticket;           // 订单号
   datetime open_time;        // 开仓时间
   double   open_price;       // 开仓价格
   double   lot;              // 手数
   double   sl;               // 止损价格
   double   tp;               // 止盈价格
   int      direction;        // 方向 (1=多单, -1=空单)
   int      level;            // 加仓层级
   bool     is_revived;       // 是否为复活订单
   int      revival_count;    // 复活次数
   datetime last_revival_time;// 上次复活时间
   string   comment;          // 订单注释
};

OrderData orders[];           // 订单数据数组

// 定义RTOTAL和SLEEPTIME (用于平仓模块)
#define RTOTAL 3
#define SLEEPTIME 1000

//--- 资金管理参数
enum MONEY_MANAGEMENT_MODE
{
   MODE_COMPOUND,            // 复利模式
   MODE_FIXED                 // 固定模式
};

input group "===== 资金管理设置 ====="
input MONEY_MANAGEMENT_MODE InpMoneyManagementMode = MODE_COMPOUND; // 资金管理模式
input double InpEquityPerLot = 5000.0;                           // 每多少净值建仓0.01手
input double InpMaxLotDivisor = 0.01;                            // 基础仓位(手)
input double InpMaxRiskPercent = 10.0;                           // 最大总仓位风险比例(%)

//--- 加仓参数
input group "===== 加仓设置 ====="
input int    InpMaxLevels = 15;                                  // 最大加仓层数
input double InpATRMultiplier = 5.0;                             // ATR加仓间距系数
input double InpFirstLevelMultiplier = 2.0;                      // 首次加仓间距系数(相对于普通间距)
input double InpInitialLotMultiplier = 1.5;                      // 初始加仓倍数(1-5层)
input double InpMidLotMultiplier = 1.3;                          // 中期加仓倍数(6-10层)
input double InpLateLotMultiplier = 1.2;                         // 后期加仓倍数(11-15层)
input double InpFinalLotMultiplier = 1.1;                        // 最终加仓倍数(>15层)

//--- 止损设置
enum STOP_LOSS_MODE
{
   SL_ATR,                   // ATR动态止损
   SL_FIXED                  // 固定点数止损
};

input group "===== 止损设置 ====="
input STOP_LOSS_MODE InpStopLossMode = SL_ATR;                   // 止损模式
input double InpATRStopMultiplier = 2.0;                         // ATR止损倍数
input int    InpFixedStopLoss = 400;                             // 固定止损点数
input bool   InpUseBreakEven = true;                             // 启用移动保本止损
input int    InpBreakEvenStart = 300;                            // 移动保本启动点数
input int    InpBreakEvenProfit = 10;                            // 保本后额外获利点数

//--- 止盈设置
input group "===== 止盈设置 ====="
input double InpBaseProfitTarget = 2000.0;                        // 基础目标盈利金额
input double InpRiskPercent = 2.0;                               // 风险比例(%)
input bool   InpUsePartialClose = true;                           // 启用分批平仓
input double InpPartialClosePercent1 = 50.0;                     // 第一次平仓比例(%)
input double InpPartialClosePercent2 = 30.0;                     // 第二次平仓比例(%)

//--- 订单复活设置
input group "===== 订单复活设置 ====="
input bool   InpUseRevival = true;                                // 启用订单复活
input double InpRevivalATRMultiplier = 1.5;                      // 复活ATR倍数
input int    InpRevivalRSIThresholdBuy = 55;                     // 多单复活RSI阈值
input int    InpRevivalRSIThresholdSell = 45;                    // 空单复活RSI阈值
input double InpRevivalATRFilter = 0.8;                          // 复活波动率过滤(原ATR比例)
input int    InpRevivalTimeInterval = 15;                         // 复活时间间隔(分钟)
input double InpRevivalLotMultiplier = 0.8;                      // 复活手数倍数
input int    InpMaxRevivalCount = 3;                             // 最大复活次数

//--- 指标参数
input group "===== 指标设置 ====="
input int    InpATRPeriod = 14;                                  // ATR周期
input int    InpRSIPeriod = 14;                                  // RSI周期

//--- UI设置
input group "===== 界面设置 ====="
input int    InpButtonX = 1750;                                  // 按钮X坐标
input int    InpButtonY = 410;                                   // 按钮Y坐标起点
input int    InpButtonWidth = 100;                               // 按钮宽度
input int    InpButtonHeight = 32;                               // 按钮高度
input int    InpButtonSpacing = 40;                              // 按钮间距
input string InpButtonFont = "极影毁片荧圆";                      // 按钮字体
input int    InpButtonFontSize = 12;                             // 按钮字体大小

//--- 全局变量
int handle_atr;                // ATR指标句柄
int handle_rsi;                // RSI指标句柄
double current_atr;            // 当前ATR值
double current_rsi;            // 当前RSI值
int total_orders;              // 当前订单总数
double total_lot;              // 当前总手数
double account_equity;         // 账户净值
double account_balance;        // 账户余额
double account_profit;         // 当前浮动盈亏
double point_value;            // 点值
double current_spread;         // 当前点差

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化交易对象
   Trade.SetExpertMagicNumber(123456);
   Trade.SetMarginMode();
   Trade.SetTypeFillingBySymbol(Symbol());
   Trade.SetDeviationInPoints(10);
   
   // 初始化品种信息
   SymbolInfo.Name(Symbol());
   point_value = SymbolInfo.Point();
   
   // 创建指标句柄
   handle_atr = iATR(Symbol(), PERIOD_CURRENT, InpATRPeriod);
   handle_rsi = iRSI(Symbol(), PERIOD_CURRENT, InpRSIPeriod, PRICE_CLOSE);
   
   if(handle_atr == INVALID_HANDLE || handle_rsi == INVALID_HANDLE)
   {
      Print("指标句柄创建失败!");
      return INIT_FAILED;
   }
   
   // 初始化订单数组
   ArrayResize(orders, 0);
   LoadExistingPositions();
   
   // 创建UI界面
   CreateUI();
   
   // 显示信息面板
   CreateInfoPanel();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   if(handle_atr != INVALID_HANDLE)
      IndicatorRelease(handle_atr);
      
   if(handle_rsi != INVALID_HANDLE)
      IndicatorRelease(handle_rsi);
      
   // 删除信息面板和UI界面
   ObjectsDeleteAll(0, "InfoPanel_");
   ObjectsDeleteAll(0, "gp_button_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新市场数据
   if(!UpdateMarketData())
      return;
      
   // 更新订单状态
   UpdateOrdersStatus();
   
   // 检查订单复活
   if(InpUseRevival)
      CheckOrderRevival();
      
   // 更新移动止损
   if(InpUseBreakEven)
      UpdateBreakEven();
      
   // 更新信息面板
   UpdateInfoPanel();
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // 处理按钮点击事件
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      if(sparam == "gp_button_buysell")
      {
         // 多空同开按钮点击
         OpenBuyOrder();  // 开多单
         OpenSellOrder(); // 开空单
         btnBuySell.State(false);
         btnBuySell.Update();
      }
      else if(sparam == "gp_button_close")
      {
         // 平仓按钮点击
         CloseAllPositions();
         btnCloseAll.State(false);
         btnCloseAll.Update();
      }
   }
}

//+------------------------------------------------------------------+
//| 创建UI界面                                                       |
//+------------------------------------------------------------------+
void CreateUI()
{
   // 创建多空同开按钮
   btnBuySell = new Button();
   btnBuySell.Create("gp_button_buysell", InpButtonX, InpButtonY);
   btnBuySell.Size(InpButtonWidth, InpButtonHeight);
   btnBuySell.Text("多空同开");
   btnBuySell.Font(InpButtonFont, InpButtonFontSize);
   btnBuySell.Color(clrWhite);
   btnBuySell.BGColor(clrMediumPurple);
   btnBuySell.BorderColor(clrDimGray);
   btnBuySell.State(false);
   btnBuySell.Update();
   
   // 创建平仓按钮
   btnCloseAll = new Button();
   btnCloseAll.Create("gp_button_close", InpButtonX, InpButtonY + InpButtonSpacing);
   btnCloseAll.Size(InpButtonWidth, InpButtonHeight);
   btnCloseAll.Text("平仓");
   btnCloseAll.Font(InpButtonFont, InpButtonFontSize);
   btnCloseAll.Color(clrWhite);
   btnCloseAll.BGColor(clrDarkOrange);
   btnCloseAll.BorderColor(clrDimGray);
   btnCloseAll.State(false);
   btnCloseAll.Update();
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 创建信息面板背景
   ObjectCreate(0, "InfoPanel_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XDISTANCE, InpButtonX - 150);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YDISTANCE, InpButtonY - 100);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_XSIZE, 250);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_YSIZE, 100);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BGCOLOR, clrWhiteSmoke);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_COLOR, clrBlack);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_BACK, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_SELECTED, false);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, "InfoPanel_BG", OBJPROP_ZORDER, 0);
   
   // 创建标题
   ObjectCreate(0, "InfoPanel_Title", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_XDISTANCE, InpButtonX - 140);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_YDISTANCE, InpButtonY - 90);
   ObjectSetString(0, "InfoPanel_Title", OBJPROP_TEXT, "账户信息");
   ObjectSetString(0, "InfoPanel_Title", OBJPROP_FONT, InpButtonFont);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_FONTSIZE, InpButtonFontSize);
   ObjectSetInteger(0, "InfoPanel_Title", OBJPROP_COLOR, clrNavy);
   
   // 创建账户余额标签
   ObjectCreate(0, "InfoPanel_Balance", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_XDISTANCE, InpButtonX - 140);
   ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_YDISTANCE, InpButtonY - 70);
   ObjectSetString(0, "InfoPanel_Balance", OBJPROP_FONT, InpButtonFont);
   ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_FONTSIZE, InpButtonFontSize - 2);
   ObjectSetInteger(0, "InfoPanel_Balance", OBJPROP_COLOR, clrBlack);
   
   // 创建账户净值标签
   ObjectCreate(0, "InfoPanel_Equity", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_XDISTANCE, InpButtonX - 140);
   ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_YDISTANCE, InpButtonY - 50);
   ObjectSetString(0, "InfoPanel_Equity", OBJPROP_FONT, InpButtonFont);
   ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_FONTSIZE, InpButtonFontSize - 2);
   ObjectSetInteger(0, "InfoPanel_Equity", OBJPROP_COLOR, clrBlack);
   
   // 创建浮动盈亏标签
   ObjectCreate(0, "InfoPanel_Profit", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_XDISTANCE, InpButtonX - 140);
   ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_YDISTANCE, InpButtonY - 30);
   ObjectSetString(0, "InfoPanel_Profit", OBJPROP_FONT, InpButtonFont);
   ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_FONTSIZE, InpButtonFontSize - 2);
   ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, clrBlack);
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   // 更新账户余额
   ObjectSetString(0, "InfoPanel_Balance", OBJPROP_TEXT, StringFormat("余额: %.2f", account_balance));
   
   // 更新账户净值
   ObjectSetString(0, "InfoPanel_Equity", OBJPROP_TEXT, StringFormat("净值: %.2f", account_equity));
   
   // 更新浮动盈亏
   string profit_text = StringFormat("盈亏: %.2f", account_profit);
   color profit_color = (account_profit >= 0) ? clrGreen : clrRed;
   ObjectSetString(0, "InfoPanel_Profit", OBJPROP_TEXT, profit_text);
   ObjectSetInteger(0, "InfoPanel_Profit", OBJPROP_COLOR, profit_color);
}

//+------------------------------------------------------------------+
//| 更新市场数据                                                     |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
   // 获取账户信息
   account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
   
   // 获取点差
   current_spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * SymbolInfo.Point();
   
   // 更新指标数据
   double atr_buffer[];
   double rsi_buffer[];
   
   if(CopyBuffer(handle_atr, 0, 0, 1, atr_buffer) <= 0)
      return false;
      
   if(CopyBuffer(handle_rsi, 0, 0, 1, rsi_buffer) <= 0)
      return false;
      
   current_atr = atr_buffer[0];
   current_rsi = rsi_buffer[0];
   
   return true;
}

//+------------------------------------------------------------------+
//| 加载现有持仓                                                     |
//+------------------------------------------------------------------+
void LoadExistingPositions()
{
   total_orders = 0;
   total_lot = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetString(POSITION_SYMBOL) == Symbol() && 
            PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
         {
            int idx = ArraySize(orders);
            ArrayResize(orders, idx + 1);
            
            orders[idx].ticket = PositionGetInteger(POSITION_TICKET);
            orders[idx].open_time = (datetime)PositionGetInteger(POSITION_TIME);
            orders[idx].open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            orders[idx].lot = PositionGetDouble(POSITION_VOLUME);
            orders[idx].sl = PositionGetDouble(POSITION_SL);
            orders[idx].tp = PositionGetDouble(POSITION_TP);
            orders[idx].direction = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 1 : -1;
            orders[idx].comment = PositionGetString(POSITION_COMMENT);
            
            // 解析注释中的层级和复活信息
            string comment = orders[idx].comment;
            if(StringFind(comment, "Level=") >= 0)
            {
               string level_str = StringSubstr(comment, StringFind(comment, "Level=") + 6, 2);
               orders[idx].level = (int)StringToInteger(level_str);
            }
            else
            {
               orders[idx].level = 1; // 默认为第一层
            }
            
            if(StringFind(comment, "Revived=") >= 0)
            {
               string revived_str = StringSubstr(comment, StringFind(comment, "Revived=") + 8, 1);
               orders[idx].is_revived = true;
               orders[idx].revival_count = (int)StringToInteger(revived_str);
            }
            else
            {
               orders[idx].is_revived = false;
               orders[idx].revival_count = 0;
            }
            
            total_orders++;
            total_lot += orders[idx].lot;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 更新订单状态                                                     |
//+------------------------------------------------------------------+
void UpdateOrdersStatus()
{
   // 重新加载所有持仓
   LoadExistingPositions();
   
   // 检查是否需要加仓
   CheckAddPosition();
   
   // 检查是否需要部分平仓
   if(InpUsePartialClose)
      CheckPartialClose();
}

//+------------------------------------------------------------------+
//| 检查加仓条件                                                     |
//+------------------------------------------------------------------+
void CheckAddPosition()
{
   if(total_orders >= InpMaxLevels)
      return; // 达到最大加仓层数
      
   // 计算当前总风险
   double total_risk = CalculateTotalRisk();
   if(total_risk > InpMaxRiskPercent)
      return; // 超过最大风险比例
   
   // 获取最后一个订单
   int last_idx = -1;
   datetime last_time = 0;
   
   for(int i = 0; i < ArraySize(orders); i++)
   {
      if(orders[i].open_time > last_time)
      {
         last_time = orders[i].open_time;
         last_idx = i;
      }
   }
   
   if(last_idx < 0)
      return;
      
   // 计算加仓间距
   double add_distance = CalculateAddDistance(orders[last_idx].level);
   
   // 获取当前价格
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   // 检查是否满足加仓条件
   bool add_condition = false;
   double entry_price = 0;
   
   if(orders[last_idx].direction > 0) // 多单
   {
      if(last_tick.ask >= orders[last_idx].open_price + add_distance)
      {
         add_condition = true;
         entry_price = last_tick.ask;
      }
   }
   else // 空单
   {
      if(last_tick.bid <= orders[last_idx].open_price - add_distance)
      {
         add_condition = true;
         entry_price = last_tick.bid;
      }
   }
   
   // 执行加仓
   if(add_condition)
   {
      // 计算加仓手数
      double add_lot = CalculateAddLot(orders[last_idx].lot, orders[last_idx].level + 1);
      
      // 计算止损止盈
      double sl = CalculateStopLoss(entry_price, orders[last_idx].direction);
      double tp = CalculateTakeProfit(entry_price, orders[last_idx].direction);
      
      // 构建注释
      string comment = StringFormat("Level=%d", orders[last_idx].level + 1);
      
      // 执行交易
      if(orders[last_idx].direction > 0) // 多单
      {
         Trade.Buy(add_lot, Symbol(), entry_price, sl, tp, comment);
      }
      else // 空单
      {
         Trade.Sell(add_lot, Symbol(), entry_price, sl, tp, comment);
      }
   }
}

//+------------------------------------------------------------------+
//| 计算止盈价格
//+------------------------------------------------------------------+
double CalculateTakeProfit(double entry_price, int direction)
{
   // 基于ATR计算止盈
   double atr_distance = current_atr * InpATRMultiplier;
   
   if(direction > 0) // 多单
      return entry_price + atr_distance;
   else // 空单
      return entry_price - atr_distance;
}

//+------------------------------------------------------------------+
//| 计算总风险                                                       |
//+------------------------------------------------------------------+
double CalculateTotalRisk()
{
   // 简单实现：总风险 = 总手数 / 账户净值 * 100%
   return (total_lot * 100000) / account_equity * 100.0;
}

//+------------------------------------------------------------------+
//| 计算加仓间距                                                     |
//+------------------------------------------------------------------+
double CalculateAddDistance(int level)
{
   double base_distance = current_atr * InpATRMultiplier;
   
   // 首次加仓间距更大
   if(level == 1)
      return base_distance * InpFirstLevelMultiplier;
      
   // 趋势加速时可以缩短间距（这里可以添加趋势加速判断逻辑）
   // 简单示例：如果RSI超过70或低于30，认为趋势加速
   if(current_rsi > 70 || current_rsi < 30)
      return base_distance * 0.8;
      
   return base_distance;
}

//+------------------------------------------------------------------+
//| 格式化手数，确保符合交易品种的最小手数和步长要求                 |
//+------------------------------------------------------------------+
double formatlots(string symbol, double lots)
{
   double a = 0;
   double minilots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double steplots = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(lots < minilots)
      return(0);
   else
   {
      double a1 = MathFloor(lots / minilots) * minilots;
      a = a1 + MathFloor((lots - a1) / steplots) * steplots;
   }
   
   return(a);
}

//+------------------------------------------------------------------+
//| 计算加仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateAddLot(double prev_lot, int level)
{
   // 根据层级选择倍数
   double multiplier;
   
   if(level <= 5)
      multiplier = InpInitialLotMultiplier;
   else if(level <= 10)
      multiplier = InpMidLotMultiplier;
   else if(level <= 15)
      multiplier = InpLateLotMultiplier;
   else
      multiplier = InpFinalLotMultiplier;
      
   // 非线性递减公式
   double lot_multiplier = 1 + (multiplier - 1) / level;
   
   // 计算新手数
   double new_lot = NormalizeDouble(prev_lot * lot_multiplier, 2);
   
   // 检查是否超过最大手数限制
   double max_lot = NormalizeDouble(InpMaxLotDivisor * (account_equity / InpEquityPerLot), 2);
   if(new_lot > max_lot)
      new_lot = max_lot;
      
   // 使用formatlots函数格式化手数
   new_lot = formatlots(Symbol(), new_lot);
      
   return new_lot;
}

//+------------------------------------------------------------------+
//| 计算开仓手数                                                     |
//+------------------------------------------------------------------+
double CalculateInitialLot()
{
   double lot;
   
   if(InpMoneyManagementMode == MODE_COMPOUND)
   {
      // 复利模式 - 新仓位=基础仓位×（当前账户余额/5000）
      lot = NormalizeDouble(InpMaxLotDivisor * (account_balance / InpEquityPerLot), 2);
   }
   else
   {
      // 固定模式
      lot = InpMaxLotDivisor; // 使用基础仓位作为固定模式手数
   }
   
   // 检查最小/最大手数限制
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   
   if(lot < min_lot) lot = min_lot;
   if(lot > max_lot) lot = max_lot;
   
   // 使用formatlots函数格式化手数
   lot = formatlots(Symbol(), lot);
   
   return lot;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                     |
//+------------------------------------------------------------------+
double CalculateStopLoss(double entry_price, int direction)
{
   double sl;
   
   // 获取品种的最小止损距离（点数）
   int stops_level = (int)SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL);
   double min_distance = stops_level * point_value;
   
   // 获取当前点差
   double local_spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * point_value;
   
   // 计算止损距离
   double sl_distance;
   
   if(InpStopLossMode == SL_ATR)
   {
      // ATR动态止损
      sl_distance = current_atr * InpATRStopMultiplier;
   }
   else
   {
      // 固定点数止损
      sl_distance = InpFixedStopLoss * point_value;
   }
   
   // 确保止损距离不小于最小止损距离，并添加额外的安全缓冲
   // 考虑点差因素，增加更多的安全边际
   sl_distance = MathMax(sl_distance, min_distance + local_spread + 20 * point_value); // 额外添加20点和当前点差作为安全缓冲
   
   if(direction > 0) // 多单
      sl = entry_price - sl_distance;
   else // 空单
      sl = entry_price + sl_distance;
   
   return NormalizeDouble(sl, SymbolInfo.Digits());
}

//+------------------------------------------------------------------+
//| 计算动态目标盈利金额                                             |
//+------------------------------------------------------------------+
double CalculateDynamicTarget()
{
   double atr = iATR(Symbol(), PERIOD_D1, 14); // 日线ATR
   double riskAmount = AccountInfoDouble(ACCOUNT_EQUITY) * 0.02; // 2%净值风险
   return MathMax(2000, riskAmount * (atr / 0.0015)); // 基础2000+波动率补偿
}

//+------------------------------------------------------------------+
//| 检查分批止盈条件                                                 |
//+------------------------------------------------------------------+
double CheckPartialClose()
{
   // 计算动态目标金额
   double target = CalculateDynamicTarget();
   
   // 计算当前总盈利
   double total_profit = 0;
   for(int i = 0; i < ArraySize(orders); i++)
      total_profit += PositionGetDouble(POSITION_PROFIT);
   
   // 检查是否达到分批止盈条件
   if(total_profit >= target)
   {
      // 第一轮平仓50%
      if(total_profit >= target * 0.5)
      {
         ClosePartialPositions(0.5);
      }
      
      // 第二轮平仓30%
      if(total_profit >= target * 0.8)
      {
         ClosePartialPositions(0.3);
      }
      
      // 第三轮平仓20%
      if(total_profit >= target)
      {
         ClosePartialPositions(0.2);
      }
   }
}

//+------------------------------------------------------------------+
//| 部分平仓函数                                                     |
//+------------------------------------------------------------------+
void ClosePartialPositions(double ratio)
{
   for(int i = 0; i < ArraySize(orders); i++)
   {
      if(PositionSelectByTicket(orders[i].ticket))
      {
         double lot_to_close = orders[i].lot * ratio;
         Trade.PositionClosePartial(orders[i].ticket, lot_to_close);
      }
   }
}

//+------------------------------------------------------------------+
//| 检查分批止盈条件                                                 |
//+------------------------------------------------------------------+


//+------------------------------------------------------------------+
//| 更新移动保本止损                                                 |
//+------------------------------------------------------------------+
void UpdateBreakEven()
{
   for(int i = 0; i < ArraySize(orders); i++)
   {
      // 获取当前价格
      MqlTick last_tick;
      SymbolInfoTick(Symbol(), last_tick);
      
      double current_price;
      if(orders[i].direction > 0) // 多单
         current_price = last_tick.bid;
      else // 空单
         current_price = last_tick.ask;
         
      // 计算当前盈利点数
      double profit_points;
      if(orders[i].direction > 0) // 多单
         profit_points = (current_price - orders[i].open_price) / point_value;
      else // 空单
         profit_points = (orders[i].open_price - current_price) / point_value;
         
      // 检查是否达到移动保本条件(300点盈利)
      if(profit_points >= 300)
      {
         // 计算新的止损价格(成本价+50点保护)
         double new_sl;
         if(orders[i].direction > 0) // 多单
            new_sl = orders[i].open_price + 50 * point_value;
         else // 空单
            new_sl = orders[i].open_price - 50 * point_value;
            
         // 检查是否需要修改止损
         if((orders[i].direction > 0 && (orders[i].sl < orders[i].open_price || orders[i].sl == 0)) || 
            (orders[i].direction < 0 && (orders[i].sl > orders[i].open_price || orders[i].sl == 0)))
         {
            // 只有当新止损价与当前止损价不同时才执行修改
            if(MathAbs(new_sl - orders[i].sl) > point_value * 0.1)
            {
               Trade.PositionModify(orders[i].ticket, new_sl, orders[i].tp);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查部分平仓条件                                                 |
//+------------------------------------------------------------------+

{
   // 使用账户总盈利作为判断依据
   // 如果账户总盈利为负，则不执行平仓
   if(account_profit <= 0)
      return;
   
   // 计算账户总盈利的百分比阈值
   // 这里InpPartialClosePercent1和InpPartialClosePercent2是输入参数，表示平仓的百分比
   // 例如，如果账户总盈利为1000，InpPartialClosePercent1为50，则阈值为500
   double profit_threshold1 = account_profit * (InpPartialClosePercent1 / 100.0);
   double profit_threshold2 = account_profit * (InpPartialClosePercent2 / 100.0);
   
   // 标记是否已经执行了第一次平仓
   static bool first_close_executed = false;
   // 标记是否已经执行了第二次平仓
   static bool second_close_executed = false;
   
   // 检查是否达到第一次平仓条件
   if(!first_close_executed && account_profit >= profit_threshold1)
   {
      first_close_executed = true;
      
      // 遍历所有订单，执行部分平仓
      for(int i = 0; i < ArraySize(orders); i++)
      {
         // 计算平仓比例
         double close_percent = InpPartialClosePercent1 / 100.0;
         double close_volume = orders[i].lot * close_percent;
         
         // 执行部分平仓
         Trade.PositionClosePartial(orders[i].ticket, close_volume, 0);
      }
   }
   // 检查是否达到第二次平仓条件
   else if(!second_close_executed && account_profit >= profit_threshold2)
   {
      second_close_executed = true;
      
      // 遍历所有订单，执行部分平仓
      for(int i = 0; i < ArraySize(orders); i++)
      {
         // 计算平仓比例
         double close_percent = InpPartialClosePercent2 / 100.0;
         double close_volume = orders[i].lot * close_percent;
         
         // 执行部分平仓
         Trade.PositionClosePartial(orders[i].ticket, close_volume, 0);
      }
   }
   
   // 如果账户总盈利下降到阈值以下，重置平仓标记，允许再次平仓
   if(account_profit < profit_threshold2)
      second_close_executed = false;
      
   if(account_profit < profit_threshold1)
      first_close_executed = false;
}

//+------------------------------------------------------------------+
//| 检查订单复活                                                     |
//+------------------------------------------------------------------+
void CheckOrderRevival()
{
   // 简单实现：当没有订单时，检查是否有符合复活条件的历史订单
   if(total_orders > 0)
      return;
      
   // 获取当前时间
   datetime current_time = TimeCurrent();
   
   // 获取历史订单
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong deal_ticket = HistoryDealGetTicket(i);
      if(deal_ticket > 0)
      {
         if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) == Symbol() && 
            HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) == Trade.RequestMagic())
         {
            // 检查是否是平仓交易
            if(HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
               // 获取原始订单信息
               ulong position_ticket = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);
               double position_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
               double position_volume = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
               int position_type = (int)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
               int direction = (position_type == DEAL_TYPE_BUY) ? 1 : -1;
               
               // 检查复活条件
               bool revival_condition = false;
               
               // 检查RSI条件
               if(direction > 0 && current_rsi > InpRevivalRSIThresholdBuy) // 多单复活条件
                  revival_condition = true;
               else if(direction < 0 && current_rsi < InpRevivalRSIThresholdSell) // 空单复活条件
                  revival_condition = true;
               
               // 检查ATR条件
               if(revival_condition && current_atr >= InpRevivalATRFilter * current_atr)
               {
                  // 计算复活手数
                  double revival_lot = position_volume * InpRevivalLotMultiplier;
                  revival_lot = formatlots(Symbol(), revival_lot);
                  
                  // 获取当前价格
                  MqlTick last_tick;
                  SymbolInfoTick(Symbol(), last_tick);
                  
                  double entry_price;
                  if(direction > 0) // 多单
                     entry_price = last_tick.ask;
                  else // 空单
                     entry_price = last_tick.bid;
                  
                  // 计算止损止盈
                  double sl = CalculateStopLoss(entry_price, direction);
                  double tp = CalculateTakeProfit(entry_price, direction);
                  
                  // 构建注释
                  string comment = "Revived=1";
                  
                  // 执行复活交易
                  if(direction > 0) // 多单
                     Trade.Buy(revival_lot, Symbol(), entry_price, sl, tp, comment);
                  else // 空单
                     Trade.Sell(revival_lot, Symbol(), entry_price, sl, tp, comment);
                  
                  break; // 只复活一个订单
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 手动开仓 - 买入                                                  |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
   // 计算开仓手数
   double lot = CalculateInitialLot();
   
   // 获取当前价格
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   double entry_price = last_tick.ask;
   
   // 计算止损
   double sl = CalculateStopLoss(entry_price, 1);
   double tp = CalculateTakeProfit(entry_price, 1);
   
   // 构建注释
   string comment = "Level=1";
   
   // 执行交易 - 使用0作为价格参数表示市价下单
   if(Trade.Buy(lot, Symbol(), 0, sl, tp, comment))
   {
      Print("多单开仓成功，手数: ", lot, ", 价格: ", SymbolInfoDouble(Symbol(), SYMBOL_ASK));
   }
   else
   {
      Print("多单开仓失败，错误代码: ", Trade.ResultRetcode(), ", 描述: ", Trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
//| 手动开仓 - 卖出                                                  |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
   // 计算开仓手数
   double lot = CalculateInitialLot();
   
   // 获取当前价格
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   double entry_price = last_tick.bid;
   
   // 计算止损
   double sl = CalculateStopLoss(entry_price, -1);
   double tp = 0; // 不设置止盈价格，使用分批止盈机制
   
   // 构建注释
   string comment = "Level=1";
   
   // 执行交易 - 使用0作为价格参数表示市价下单
   if(Trade.Sell(lot, Symbol(), 0, sl, tp, comment))
   {
      Print("空单开仓成功，手数: ", lot, ", 价格: ", SymbolInfoDouble(Symbol(), SYMBOL_BID));
   }
   else
   {
      Print("空单开仓失败，错误代码: ", Trade.ResultRetcode(), ", 描述: ", Trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
//| 平仓所有持仓                                                     |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   // 使用ClosePositionsModule中的异步平仓函数
   Trade.SetDeviationInPoints(INT_MAX);
   Trade.SetAsyncMode(true);
   Trade.SetMarginMode();
   Trade.LogLevel(LOG_LEVEL_ERRORS);

   for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
   {
      bool result = true;
      m_arr_tickets.Shutdown();

      for(int i = 0; i < PositionsTotal() && !IsStopped(); i++)
      {
         if(PositionInfo.SelectByIndex(i))
         {
            if(PositionInfo.Symbol() == Symbol() && PositionInfo.Magic() == Trade.RequestMagic())
               m_arr_tickets.Add(PositionInfo.Ticket());
         }
      }

      for(int i = 0; i < m_arr_tickets.Total() && !IsStopped(); i++)
      {
         ulong ticket = m_arr_tickets.At(i);
         if(PositionInfo.SelectByTicket(ticket))
         {
            int freeze_level = (int)SymbolInfoInteger(PositionInfo.Symbol(), SYMBOL_TRADE_FREEZE_LEVEL);
            double point = SymbolInfoDouble(PositionInfo.Symbol(), SYMBOL_POINT);
            bool TP_check = (MathAbs(PositionInfo.PriceCurrent() - PositionInfo.TakeProfit()) > freeze_level * point);
            bool SL_check = (MathAbs(PositionInfo.PriceCurrent() - PositionInfo.StopLoss()) > freeze_level * point);

            if(TP_check && SL_check)
            {
               Trade.SetExpertMagicNumber(PositionInfo.Magic());
               Trade.SetTypeFillingBySymbol(PositionInfo.Symbol());
               if(Trade.PositionClose(ticket) && (Trade.ResultRetcode() == TRADE_RETCODE_DONE || Trade.ResultRetcode() == TRADE_RETCODE_PLACED))
               {
                  PrintFormat("Position ticket #%I64u on %s to be closed.", ticket, PositionInfo.Symbol());
                  PlaySound("expert.wav");
               }
               else
               {
                  PrintFormat("> Error: closing position ticket #%I64u on %s failed. Retcode=%u (%s)", ticket, PositionInfo.Symbol(), Trade.ResultRetcode(), Trade.ResultComment());
                  result = false;
               }
            }
            else
            {
               PrintFormat("> Error: closing position ticket #%I64u on %s is prohibited. Position TP or SL is too close to activation price [FROZEN].", ticket, PositionInfo.Symbol());
               result = false;
            }
         }
      }

      if(result)
         break;

      Sleep(SLEEPTIME);
      PlaySound("timeout.wav");
   }
}