//+------------------------------------------------------------------+
//|                                          多因子量化交易.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Math\Stat\Math.mqh>
#include <Math\Stat\Normal.mqh>

// 全局变量
CTrade         Trade;          // 交易对象
CSymbolInfo    SymbolInfo;     // 品种信息对象

//--- 市场状态参数
input int      InpHMMPeriod = 100;       // 隐马尔可夫模型周期
input double   InpFractalThreshold = 0.65;// 分形维度阈值
input int      InpATRPeriod_Asia = 21;    // ATR周期(亚盘)
input int      InpATRPeriod_EU = 14;      // ATR周期(欧盘)
input int      InpATRPeriod_US = 9;       // ATR周期(美盘)

//--- 交易信号参数
input double   InpImbalanceThreshold = 0.15; // 订单失衡阈值
input int      InpEMAFast = 5;             // 快速EMA周期
input int      InpEMASlow = 20;            // 慢速EMA周期
input int      InpADXPeriod = 14;          // ADX周期
input double   InpADXThreshold = 25;        // ADX阈值

//--- 仓位管理参数
input double   InpLeverage_Asia = 3;        // 基础杠杆(亚盘)
input double   InpLeverage_EU = 5;          // 基础杠杆(欧盘)
input double   InpLeverage_US = 8;          // 基础杠杆(美盘)
input double   InpMaxLot_Asia = 0.3;        // 最大单量(亚盘)
input double   InpMaxLot_EU = 0.5;          // 最大单量(欧盘)
input double   InpMaxLot_US = 1.0;          // 最大单量(美盘)

//--- 风险控制参数
input int      InpVaRPeriod = 252;         // VaR计算周期
input double   InpVaRLevel = 0.95;          // VaR置信水平
input double   InpMaxRiskPercent = 0.02;    // 最大风险比例
input double   InpMinLiquidity = 500000;    // 最小流动性阈值

//--- 订单执行参数
input double   InpSlippage_Asia = 0.5;      // 滑点容忍(亚盘)
input double   InpSlippage_EU = 0.3;        // 滑点容忍(欧盘)
input double   InpSlippage_US = 0.2;        // 滑点容忍(美盘)
input double   InpEntropyThreshold_Asia = 1.8; // 熵阈值(亚盘)
input double   InpEntropyThreshold_EU = 1.5;   // 熵阈值(欧盘)
input double   InpEntropyThreshold_US = 1.2;   // 熵阈值(美盘)

//--- 系统参数
input long     InpMagicNumber = 234567;     // EA幻数
input string   InpPythonPath = "C:\\Python39\\"; // Python安装路径

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化交易设置
   Trade.SetExpertMagicNumber(InpMagicNumber);
   SymbolInfo.Name(Symbol());
   
   // 检查品种
   if(Symbol() != "XAUUSD")
   {
      Print("EA只支持XAUUSD品种!");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // 检查Python环境
   if(!FileIsExist(InpPythonPath + "python.exe"))
   {
      Print("未找到Python环境，请检查路径设置!");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清理工作
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 更新市场状态
   UpdateMarketState();
   
   // 扫描流动性
   if(!CheckLiquidity())
      return;
      
   // 检查风险
   if(!CheckRisk())
      return;
   
   // 生成交易信号
   if(ValidateSignal())
   {
      ExecuteOrders();
      AdjustPositions();
   }
}

//+------------------------------------------------------------------+
//| 市场状态更新函数                                                  |
//+------------------------------------------------------------------+
void UpdateMarketState()
{
   // 计算技术指标
   double atr = iATR(Symbol(), PERIOD_CURRENT, GetCurrentATRPeriod(), 0);
   double adx = iADX(Symbol(), PERIOD_CURRENT, InpADXPeriod, PRICE_CLOSE, MODE_MAIN, 0);
   double ema_fast = iMA(Symbol(), PERIOD_CURRENT, InpEMAFast, 0, MODE_EMA, PRICE_CLOSE, 0);
   double ema_slow = iMA(Symbol(), PERIOD_CURRENT, InpEMASlow, 0, MODE_EMA, PRICE_CLOSE, 0);
   
   // 计算订单流失衡度
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   double imbalance = (last_tick.volume_real - last_tick.volume) / 
                     (last_tick.volume_real + last_tick.volume);
                     
   // TODO: 调用Python进行HMM预测
}

//+------------------------------------------------------------------+
//| 流动性检查函数                                                    |
//+------------------------------------------------------------------+
bool CheckLiquidity()
{
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   double book_depth = last_tick.volume_real + last_tick.volume;
   double atr = iATR(Symbol(), PERIOD_CURRENT, GetCurrentATRPeriod(), 0);
   
   // 检查流动性黑洞
   if(book_depth < InpMinLiquidity && atr > 0.0015 && 
      TimeToStruct(TimeCurrent()).sec > 3540)
      return false;
      
   return true;
}

//+------------------------------------------------------------------+
//| 风险检查函数                                                      |
//+------------------------------------------------------------------+
bool CheckRisk()
{
   // 计算历史VaR
   double var95 = CalculateHistoricalVaR(InpVaRPeriod, InpVaRLevel);
   double max_risk = AccountBalance() * InpMaxRiskPercent;
   
   if(PositionRisk() > MathMin(var95, max_risk))
   {
      // 平仓50%持仓
      ForceClosePositions(50);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 信号验证函数                                                      |
//+------------------------------------------------------------------+
bool ValidateSignal()
{
   // 计算技术指标
   double ema_fast = iMA(Symbol(), PERIOD_CURRENT, InpEMAFast, 0, MODE_EMA, PRICE_CLOSE, 0);
   double ema_slow = iMA(Symbol(), PERIOD_CURRENT, InpEMASlow, 0, MODE_EMA, PRICE_CLOSE, 0);
   double adx = iADX(Symbol(), PERIOD_CURRENT, InpADXPeriod, PRICE_CLOSE, MODE_MAIN, 0);
   
   // 计算订单流失衡度
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   double imbalance = (last_tick.volume_real - last_tick.volume) / 
                     (last_tick.volume_real + last_tick.volume);
   
   // 生成量子随机数(模拟)
   double q_random = MathRand() / 32767.0;
   
   bool trend_signal = (ema_fast > ema_slow && adx > InpADXThreshold);
   
   return (MathAbs(imbalance) > InpImbalanceThreshold && trend_signal && q_random < 0.55);
}

//+------------------------------------------------------------------+
//| 订单执行函数                                                      |
//+------------------------------------------------------------------+
void ExecuteOrders()
{
   // 获取当前市场状态
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   // 计算订单参数
   double lot = CalculateLotSize();
   double sl = CalculateStopLoss();
   double tp = CalculateTakeProfit();
   
   // 执行订单
   if(last_tick.volume_real > last_tick.volume)
   {
      Trade.Buy(lot, NULL, last_tick.ask, sl, tp, "MultiFactorEA");
   }
   else
   {
      Trade.Sell(lot, NULL, last_tick.bid, sl, tp, "MultiFactorEA");
   }
}

//+------------------------------------------------------------------+
//| 仓位调整函数                                                      |
//+------------------------------------------------------------------+
void AdjustPositions()
{
   // 获取当前持仓
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket <= 0) continue;
      
      if(PositionSelectByTicket(ticket))
      {
         // 检查持仓时间
         datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
         if(TimeCurrent() - open_time > GetMaxHoldingTime())
         {
            Trade.PositionClose(ticket);
            continue;
         }
         
         // 更新止损止盈
         double new_sl = CalculateStopLoss();
         double new_tp = CalculateTakeProfit();
         Trade.PositionModify(ticket, new_sl, new_tp);
      }
   }
}

//+------------------------------------------------------------------+
//| 辅助函数                                                          |
//+------------------------------------------------------------------+

// 获取当前时段的ATR周期
int GetCurrentATRPeriod()
{
   int hour = TimeHour(TimeCurrent());
   
   if(hour >= 0 && hour < 8)      // 亚盘
      return InpATRPeriod_Asia;
   else if(hour >= 8 && hour < 16) // 欧盘
      return InpATRPeriod_EU;
   else                           // 美盘
      return InpATRPeriod_US;
}

// 计算历史VaR
double CalculateHistoricalVaR(int period, double confidence_level)
{
   double returns[];
   ArrayResize(returns, period);
   
   // 计算历史收益率
   for(int i = 0; i < period; i++)
   {
      double close1 = iClose(Symbol(), PERIOD_D1, i);
      double close2 = iClose(Symbol(), PERIOD_D1, i+1);
      returns[i] = (close1 - close2) / close2;
   }
   
   // 排序
   ArraySort(returns);
   
   // 计算VaR
   int index = (int)((1 - confidence_level) * period);
   return -returns[index];
}

// 计算仓位风险
double PositionRisk()
{
   double risk = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket <= 0) continue;
      
      if(PositionSelectByTicket(ticket))
      {
         double pos_profit = PositionGetDouble(POSITION_PROFIT);
         double pos_volume = PositionGetDouble(POSITION_VOLUME);
         risk += MathAbs(pos_profit) * pos_volume;
      }
   }
   
   return risk;
}

// 强制平仓
void ForceClosePositions(int percent)
{
   int total = PositionsTotal();
   int to_close = (int)MathCeil(total * percent / 100.0);
   
   for(int i = 0; i < to_close; i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket <= 0) continue;
      
      Trade.PositionClose(ticket);
   }
}

// 计算下单量
double CalculateLotSize()
{
   datetime current_time = TimeCurrent();
   int hour = TimeHour(current_time);
   double max_lot;
   
   if(hour >= 0 && hour < 8)      // 亚盘
      max_lot = InpMaxLot_Asia;
   else if(hour >= 8 && hour < 16) // 欧盘
      max_lot = InpMaxLot_EU;
   else                           // 美盘
      max_lot = InpMaxLot_US;
      
   // 根据账户余额和风险设置计算实际下单量
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_based_lot = balance * InpMaxRiskPercent / 1000.0; // 假设每手1000美元保证金
   
   return MathMin(max_lot, risk_based_lot);
}

// 计算止损价格
double CalculateStopLoss()
{
   double atr = iATR(Symbol(), PERIOD_CURRENT, GetCurrentATRPeriod());
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   if(last_tick.volume_real > last_tick.volume) // 做多
      return last_tick.ask - atr * 2;
   else // 做空
      return last_tick.bid + atr * 2;
}

// 计算止盈价格
double CalculateTakeProfit()
{
   double atr = iATR(Symbol(), PERIOD_CURRENT, GetCurrentATRPeriod());
   MqlTick last_tick;
   SymbolInfoTick(Symbol(), last_tick);
   
   if(last_tick.volume_real > last_tick.volume) // 做多
      return last_tick.ask + atr * 3;
   else // 做空
      return last_tick.bid - atr * 3;
}

// 获取最大持仓时间（秒）
int GetMaxHoldingTime()
{
   int hour = TimeHour(TimeCurrent());
   
   if(hour >= 0 && hour < 8)      // 亚盘
      return 14400;  // 4小时
   else if(hour >= 8 && hour < 16) // 欧盘
      return 7200;   // 2小时
   else                           // 美盘
      return 3600;   // 1小时
}